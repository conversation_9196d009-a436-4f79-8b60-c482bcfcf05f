<!-- 新增RelationLines.vue -->
<template>
  <svg class="relation-layer">
    <!-- 绘制组件间连线 -->
    <line 
      v-for="(conn, index) in connections"
      :key="index"
      :x1="conn.source.x" 
      :y1="conn.source.y"
      :x2="conn.target.x"
      :y2="conn.target.y"
      stroke="#4285F4"
      stroke-width="2"
    />
    
    <!-- 绘制数据绑定指示器 -->
    <circle 
      v-for="binding in dataBindings"
      :key="binding.id"
      :cx="binding.x"
      :cy="binding.y"
      r="5"
      fill="#34A853"
    />
  </svg>
</template>

<script setup> 
import { ref } from 'vue';

const connections = ref([]);
const dataBindings = ref([]);

const addConnection = (source, target) => {
  connections.value.push({ source, target });
};

const addDataBinding = (x, y) => {
  dataBindings.value.push({ id: dataBindings.value.length, x, y });
};


</script>