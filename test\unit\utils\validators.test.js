import { requiredValidator, emailValidator, passwordValidator } from '@/utils/validators';

describe('validators', () => {
  test('requiredValidator checks for required fields', () => {
    expect(requiredValidator(null)).toBe('This field is required');
    expect(requiredValidator('')).toBe('This field is required');
    expect(requiredValidator('value')).toBe(true);
  });

  test('emailValidator checks for valid emails', () => {
    expect(emailValidator('<EMAIL>')).toBe(true);
    expect(emailValidator('invalid-email')).toBe('The Email field must be a valid email');
  });

  test('passwordValidator checks for valid passwords', () => {
    expect(passwordValidator('Password1!')).toBe(true);
    expect(passwordValidator('short')).toBe('Field must contain at least one uppercase, lowercase, special character and digit with min 8 chars');
  });
}); 