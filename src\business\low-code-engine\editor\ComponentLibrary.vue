<template>
  <div class="component-library">
    <!-- 场景选择器 -->
    <div class="scenario-selector">
      <h4 class="section-title">
        <VIcon icon="mdi-target" size="16" class="mr-2" />
        Scenario Selector
      </h4>
      <VSelect 
        :model-value="selectedScenario" 
        :items="scenarios" 
        item-title="title" 
        item-value="value"
        placeholder="Select Scenario..." 
        density="compact" 
        variant="outlined" 
        hide-details
        @update:model-value="handleScenarioChange" 
      />
    </div>

    <!-- 快速生成区域 -->
    <div class="quick-generate-section">
      <h4 class="section-title">
        <VIcon icon="mdi-lightning-bolt" size="16" class="mr-2" />
        Quick Generation
      </h4>
      <div class="quick-generate-grid">
        <div 
          v-for="pageType in quickPageTypes" 
          :key="pageType.type"
          class="quick-generate-item"
          @click="generateSmartPage(pageType.type)"
        >
          <VIcon :icon="pageType.icon" color="#44D62C" size="20" class="mb-2" />
          <span class="page-type-label">{{ pageType.label }}</span>
        </div>
      </div>
    </div>

    <!-- 组件库 -->
    <div class="components-section">
      <h4 class="section-title">
        <VIcon icon="mdi-puzzle" size="16" class="mr-2" />
        Component Library
      </h4>
      
      <!-- 组件分类标签 -->
      <div class="component-tabs">
        <button 
          v-for="category in componentCategories" 
          :key="category.key"
          :class="['tab-button', { active: activeCategory === category.key }]"
          @click="activeCategory = category.key"
        >
          {{ category.label }}
        </button>
      </div>

      <!-- 组件列表 -->
      <div class="components-list">
        <div 
          v-for="component in currentCategoryComponents" 
          :key="component.type"
          class="component-item"
          draggable="true"
          :data-component="JSON.stringify(component)"
          @dragstart="handleDragStart($event)"
          @dragend="handleDragEnd($event)"
          ref="componentRefs"
        >
          <VIcon :icon="component.icon" color="#44D62C" size="16" class="mr-2" />
          <span>{{ component.label }}</span>
          <div class="component-preview">
            <component :is="component.preview" v-if="component.preview" />
        </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  selectedScenario: String,
  scenarios: Array
})

// Emits
const emit = defineEmits([
  'scenario-change',
  'component-drag-start',
  'generate-page'
])

// 响应式数据
const activeCategory = ref('basic')
const componentRefs = ref([])

// 快速页面类型
const quickPageTypes = [
  { type: 'list', label: 'List Page', icon: 'mdi-format-list-bulleted' },
  { type: 'create', label: 'Create Page', icon: 'mdi-plus' },
  { type: 'edit', label: 'Edit Page', icon: 'mdi-pencil' },
  { type: 'detail', label: 'Detail Page', icon: 'mdi-eye' },
]

// 组件分类
const componentCategories = [
  { key: 'basic', label: 'Basic' },
  { key: 'form', label: 'Form' },
  { key: 'data', label: 'Data' },
  { key: 'layout', label: 'Layout' }
]

// 组件库定义
const componentLibrary = {
  basic: [
    { type: 'VCard', label: 'Card', icon: 'mdi-card-outline', preview: 'VCard' },
    { type: 'VBtn', label: 'Button', icon: 'mdi-button-cursor', preview: 'VBtn' },
    { type: 'VIcon', label: 'Icon', icon: 'mdi-emoticon', preview: 'VIcon' },
    { type: 'VDivider', label: 'Divider', icon: 'mdi-minus', preview: 'VDivider' }
  ],
  form: [
    { type: 'VTextField', label: 'Input', icon: 'mdi-form-textbox', preview: 'VTextField' },
    { type: 'VSelect', label: 'Select', icon: 'mdi-form-select', preview: 'VSelect' },
    { type: 'VCheckbox', label: 'Checkbox', icon: 'mdi-checkbox-marked', preview: 'VCheckbox' },
    { type: 'VRadioGroup', label: 'Radio', icon: 'mdi-radiobox-marked', preview: 'VRadioGroup' },
    { type: 'VTextarea', label: 'Textarea', icon: 'mdi-form-textarea', preview: 'VTextarea' },
    { type: 'VDatePicker', label: 'Date Picker', icon: 'mdi-calendar', preview: 'VDatePicker' }
  ],
  data: [
    { type: 'VDataTable', label: 'Table', icon: 'mdi-table', preview: 'VDataTable' },
    { type: 'VList', label: 'List', icon: 'mdi-format-list-bulleted', preview: 'VList' },
    { type: 'VTreeview', label: 'Treeview', icon: 'mdi-file-tree', preview: 'VTreeview' },
    { type: 'VTimeline', label: 'Timeline', icon: 'mdi-timeline', preview: 'VTimeline' }
  ],
  layout: [
    { type: 'VRow', label: 'Row', icon: 'mdi-view-column', preview: 'VRow' },
    { type: 'VCol', label: 'Col', icon: 'mdi-view-column-outline', preview: 'VCol' },
    { type: 'VContainer', label: 'Container', icon: 'mdi-view-grid', preview: 'VContainer' },
    { type: 'VSheet', label: 'Sheet', icon: 'mdi-view-dashboard-outline', preview: 'VSheet' }
  ]
}

// 计算属性
const currentCategoryComponents = computed(() => {
  return componentLibrary[activeCategory.value] || []
})

// 方法
function handleScenarioChange(scenarioId) {
  emit('scenario-change', scenarioId)
}

function generateSmartPage(pageType) {
  emit('generate-page', pageType)
}

/**
 * 拖拽开始处理
 */
function handleDragStart(event) {
  console.log('ComponentLibrary: 开始拖拽', event)
  
  if (!event || !event.target || !event.dataTransfer) {
    console.error('ComponentLibrary: 无效的拖拽事件', event)
    return
  }
  
  try {
    const componentData = JSON.parse(event.target.dataset.component)
    console.log('ComponentLibrary: 组件数据', componentData)
    
    event.dataTransfer.setData('component', JSON.stringify(componentData))
    event.dataTransfer.effectAllowed = 'copy'
    
    // 添加拖拽样式
    event.target.classList.add('dragging')
    
    emit('component-drag-start', event, componentData)
  } catch (error) {
    console.error('ComponentLibrary: 拖拽开始处理失败', error)
  }
}

/**
 * 拖拽结束处理
 */
function handleDragEnd(event) {
  console.log('ComponentLibrary: 拖拽结束', event)
  
  if (!event || !event.target) {
    return
  }
  
  event.target.classList.remove('dragging')
}
</script>

<style scoped>
.component-library {
  width: 280px;
  min-width: 280px; /* 确保最小宽度 */
  max-width: 280px; /* 确保最大宽度 */
  background: #2d2d2d;
  border-right: 1px solid #404040;
  padding: 16px;
  overflow-y: auto; /* 内容溢出时垂直滚动 */
  overflow-x: hidden; /* 水平不滚动 */
  height: 100%;
  flex-shrink: 0; /* 防止收缩 */
}

.scenario-selector {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #404040;
}

.quick-generate-section {
  margin-bottom: 24px;
}

.section-title {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.quick-generate-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.quick-generate-item {
  background: linear-gradient(135deg, #44D62C20, #44D62C10);
  border: 1px solid #44D62C40;
  border-radius: 8px;
  padding: 12px 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #ffffff;
}

.quick-generate-item:hover {
  background: linear-gradient(135deg, #44D62C30, #44D62C20);
  border-color: #44D62C;
  transform: translateY(-1px);
}

.page-type-label {
  font-size: 11px;
  font-weight: 500;
  display: block;
  margin-top: 4px;
}

.components-section {
  margin-bottom: 20px;
}

.component-tabs {
  display: flex;
  background: #404040;
  border-radius: 6px;
  padding: 2px;
  margin-bottom: 12px;
}

.tab-button {
  flex: 1;
  background: transparent;
  border: none;
  color: #cccccc;
  padding: 6px 8px;
  font-size: 11px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-button.active {
  background: #44D62C;
  color: #ffffff;
}

.components-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.component-item {
  background: #404040;
  border: 1px solid #555;
  border-radius: 6px;
  padding: 10px 12px;
  cursor: grab;
  transition: all 0.2s ease;
  color: #ffffff;
  font-size: 12px;
  display: flex;
  align-items: center;
  position: relative;
  user-select: none;
}

.component-item:hover {
  border-color: #44D62C;
  background: #4d4d4d;
  transform: translateX(2px);
}

.component-item.dragging {
  opacity: 0.8;
  transform: rotate(5deg);
  z-index: 1000;
  cursor: grabbing;
}

.component-preview {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0.3;
  pointer-events: none;
}


/* 滚动条样式 */
.component-library::-webkit-scrollbar {
  width: 6px;
}

.component-library::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.component-library::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 3px;
}

.component-library::-webkit-scrollbar-thumb:hover {
  background: #666;
}
</style>