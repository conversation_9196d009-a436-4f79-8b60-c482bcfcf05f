# 低代码平台元数据结构设计方案（用户模块场景深度示例）

## 1. 设计要点

1. 组件树结构：页面由组件树描述，支持嵌套、递归。
2. 属性配置：每个组件有独立的props，支持UI属性、校验、数据绑定等。
3. 事件与动作：支持事件（如onClick、@update:model-value等）与动作（如API调用、弹窗、页面跳转、消息提示等）。
4. 数据源绑定：支持组件与后端API、静态数据、data、computed等数据源绑定。
5. 权限与可见性：支持组件级权限、条件渲染。
6. 样式与布局：支持class、style、unocss等灵活样式配置。
7. 插槽与自定义渲染：支持表格自定义列、批量操作区、分页区等插槽。
8. 页面级元信息：如页面标题、路由、权限等。

## 2. 页面元数据结构示例

```json
{
  "id": "user-list-page",
  "name": "用户管理",
  "path": "/user/list",
  "title": "用户列表",
  "layout": "default",
  "data": {
    "searchQuery": {/* ... */},
    "usersData": {/* ... */},
    "accounts": {/* ... */},
    "totalUsers": 0,
    "selectedRows": [],
    "selectedIds": [],
    "expanded": [],
    "status": [/* ... */],
    "userGroupList": [/* ... */],
    "roleList": [/* ... */],
    "paginationOptions": [10, 20, 50, 100]
  },
  "components": [
    {
      "type": "VCard",
      "props": { "class": "mb-6" },
      "children": [
        {
          "type": "VCardTitle",
          "children": [
            {
              "type": "div",
              "props": { "class": "d-flex align-center flex-wrap" },
              "children": [
                { "type": "text", "props": { "text": "Users" } },
                { "type": "VSpacer" },
                {
                  "type": "div",
                  "props": { "class": "app-user-search-filter d-flex align-center gap-4" },
                  "children": [
                    {
                      "type": "VBtn",
                      "props": { "text": "CREATE" },
                      "events": { "click": [{ "action": "handleAddUser" }] },
                      "permission": ["Add", "UserList"]
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          "type": "VCardText",
          "props": { "class": "px-0" },
          "children": [
            {
              "type": "VRow",
              "children": [
                // 搜索区各 VCol 组件，内嵌 AppTextField、AppAutocomplete、AppDateTimePicker、AppSelect 等
                {
                  "type": "VCol",
                  "props": { "cols": 12, "sm": 4, "xl": 3, "xxl": 2 },
                  "children": [
                    {
                      "type": "AppTextField",
                      "model": "searchQuery.email",
                      "props": {
                        "placeholder": "Search Email",
                        "clearable": true
                      },
                      "events": {
                        "update:model-value": [{ "action": "handleSearch" }]
                      }
                    }
                  ]
                },
                // 其他搜索项同理...
              ]
            }
          ]
        },
        // 批量操作提示区
        {
          "type": "VCardText",
          "slot": "batch-actions",
          "v-if": "selectedRows.length > 0",
          "children": [
            // 选中统计、批量启用/禁用、清空选中等
            {
              "type": "VBtn",
              "props": { "text": "DISABLE" },
              "events": { "click": [{ "action": "batchDisable" }] },
              "permission": ["Edit", "UserList"],
              "v-if": "hasShowDisable"
            },
            {
              "type": "VBtn",
              "props": { "text": "ENABLE", "color": "primary" },
              "events": { "click": [{ "action": "batchEnable" }] },
              "permission": ["Edit", "UserList"],
              "v-if": "hasShowEnable"
            }
          ]
        },
        // 数据表格
        {
          "type": "VDataTableServer",
          "props": {
            "items": "{{accounts}}",
            "item-value": "accountId",
            "items-length": "{{totalUsers}}",
            "headers": "{{headers}}",
            "loading": "{{isLoading}}",
            "expanded": "{{expanded}}",
            "show-select": true
          },
          "events": {
            "click:row": [{ "action": "handleRowClick" }],
            "update:sort-by": [{ "action": "handleSortChange" }],
            "update:modelValue": [{ "action": "handleSelectChange" }]
          },
          "slots": {
            "expanded-row": {/* ... */},
            "item.email": {/* ... */},
            "item.name": {/* ... */},
            "item.groups": {/* ... */},
            "item.status": {/* ... */},
            "item.createdDateTime": {/* ... */},
            "item.updatedDateTime": {/* ... */},
            "item.actions": {/* ... */},
            "bottom": {/* ... */}
          }
        }
      ]
    }
  ],
  "apis": [
    {
      "id": "user-list",
      "type": "http",
      "method": "POST",
      "url": "/api/admin-api/v1/account/page",
      "params": {/* ... */}
    },
    {
      "id": "user-batch-status",
      "type": "http",
      "method": "PUT",
      "url": "/api/admin-api/v1/account/batch/status",
      "params": {/* ... */}
    }
  ],
  "methods": {
    "handleAddUser": {
      "type": "navigate",
      "to": "/user/list/add"
    },
    "handleSearch": {
      "type": "setVariable",
      "variable": "searchQuery.page",
      "value": 1,
      "then": [
        { "action": "callApi", "apiId": "user-list" }
      ]
    },
    "editUser": {
      "type": "navigate",
      "to": "/user/list/edit",
      "params": { "id": "{{item.accountId}}" }
    },
    "batchEnable": {
      "type": "confirm",
      "title": "Confirm Enable Users",
      "text": "Are you sure you want to enable the selected user(s)?",
      "onConfirm": [
        {
          "action": "callApi",
          "apiId": "user-batch-status",
          "params": {
            "status": 1,
            "ids": "{{selectedRows.map(item => item.accountId)}}"
          }
        },
        { "action": "message", "messageType": "success", "text": "Users enabled successfully" },
        { "action": "callApi", "apiId": "user-list" }
      ]
    },
    "toggleStatusId": {
      "params": ["item"],
      "type": "confirm",
      "title": "{{item.accountStatusId === 1 ? 'Disable User?' : 'Enable User?'}}",
      "text": "{{item.accountStatusId === 1 ? 'Once disabled, user will lose access to the system.' : 'Once enabled, user will regain access to the system.'}}",
      "onConfirm": [
        {
          "action": "callApi",
          "apiId": "user-batch-status",
          "params": {
            "status": "{{item.accountStatusId === 1 ? 0 : 1}}",
            "ids": "[{{item.accountId}}]"
          }
        },
        { "action": "message", "messageType": "success", "text": "User status updated successfully" },
        { "action": "callApi", "apiId": "user-list" }
      ]
    }
  }
}
```

## 3. 结构说明与扩展性

- **data**：页面用到的所有响应式数据、computed、数据源，等价于 Vue 的 data。
- **components**：支持插槽、条件渲染、权限、事件、批量操作、分页等
- **apis**：支持多API、参数映射、批量操作
- **methods**：页面级方法定义，集中管理所有事件处理逻辑。事件通过组件的 events 字段声明，指向 methods 中的具体方法名。方法支持多种动作类型（API、变量赋值、弹窗、消息、跳转、链式/条件动作等），可访问页面变量、API结果等上下文，便于维护和复用。
- **events/actions**：支持多事件、多动作（API、弹窗、跳转、消息等）
- **slots**：支持表格自定义渲染、底部分页等

## 4. 总结

- 该元数据结构高度贴合实际业务页面，兼容主流低代码平台设计思想，适配Vue3 + Vuetify + unocss项目
- 具备良好的扩展性和可维护性，支持复杂业务场景
- methods 字段作为一级字段，极大提升了低代码平台的灵活性和可维护性，适配绝大多数业务场景
- 可根据实际业务灵活扩展（如流程、审批、图表等） 

## 5. 方法与事件处理函数设计方案

### 1. 设计思想
- 事件（如 click、change、update:model-value 等）通过元数据声明，指向具体的方法名或动作。
- 方法（methods）集中定义，便于维护和复用。
- 支持多种动作类型（API请求、变量赋值、弹窗、消息、跳转、批量操作等）。
- 事件可传递参数，方法可访问页面变量、API结果等上下文。
- **支持参数占位符/声明**：在 methods 结构中通过 `params` 字段显式声明方法可接收的参数，提升可读性和可维护性。

### 2. 元数据结构示例

#### 事件声明（组件内）
```json
{
  "type": "VBtn",
  "props": { "text": "CREATE" },
  "events": {
    "click": [
      { "action": "handleAddUser" }
    ]
  }
}
```

#### 方法定义（页面级 methods，含参数声明）
```json
"methods": {
  "toggleStatusId": {
    "params": ["item"],
    "type": "confirm",
    "title": "{{item.accountStatusId === 1 ? 'Disable User?' : 'Enable User?'}}",
    "text": "{{item.accountStatusId === 1 ? 'Once disabled, user will lose access to the system.' : 'Once enabled, user will regain access to the system.'}}",
    "onConfirm": [
      {
        "action": "callApi",
        "apiId": "user-batch-status",
        "params": {
          "status": "{{item.accountStatusId === 1 ? 0 : 1}}",
          "ids": "[{{item.accountId}}]"
        }
      },
      { "action": "message", "messageType": "success", "text": "User status updated successfully" },
      { "action": "callApi", "apiId": "user-list" }
    ]
  }
}
```

#### 事件声明时传参（可选增强）
```json
"events": {
  "click": [
    { "action": "toggleStatusId", "args": ["item"] }
  ]
}
```

### 3. 动作类型说明
- `callApi`：调用API，支持参数、回调、结果映射
- `setVariable`：设置变量或表单字段
- `navigate`：页面跳转
- `message`：消息提示（success、error、info等）
- `confirm`：弹窗确认，支持onConfirm链式动作
- `openDialog`/`closeDialog`：弹窗操作
- `custom`：自定义JS表达式（如需扩展）

### 4. 执行流程
1. 组件声明事件 → 事件触发
2. 查找并执行对应 methods 中的动作
3. 支持链式、条件、参数传递
4. 支持异步（如API、弹窗确认）

### 5. 结构说明与建议
- 事件声明与方法定义分离，便于维护和扩展
- 方法支持多种动作类型，满足复杂业务需求
- 支持参数、链式、条件、异步，灵活应对各种场景
- 建议所有页面级方法统一在 methods 字段集中声明
- **建议所有涉及上下文参数（如 item、selectedRows 等）的方法，均通过 params 字段显式声明参数来源，提升可读性和可维护性。**

### 6. 总结
- 该设计方式可极大提升低代码平台的灵活性和可维护性，适配绝大多数业务场景
- 如需更复杂的自定义逻辑，可通过 `custom` 类型扩展 