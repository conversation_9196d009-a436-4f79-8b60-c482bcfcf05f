import { computed, onMounted, ref, watch } from "vue";

import { debounce } from "@/utils/helpers";
import message from "@/utils/message";

export const useTable = ({
  // base options
  createDefaultQuery = () => ({}),
  fetchData = async () => {},
  transformResponse = (res) => res?.data,
  rowKey = 'id',
} = {}) => {
  // state management
  const searchQuery = ref(createDefaultQuery());
  const isLoading = ref(false);
  const tableData = ref(null);
  const total = ref(0);
  const itemsPerPage = ref(10);
  const page = ref(1);

  // 计算属性
  const items = computed(() => {
    if (!tableData.value) return [];
    return tableData.value.map((item, index) => ({
      ...item,
      no: ((page.value - 1) * itemsPerPage.value) + index + 1
    }));
  });


  // load data
  const loadData = async () => {
    try {
      isLoading.value = true;
      tableData.value = null;
      const params = getQueryParams();
      const res = await fetchData(params);
      const response = transformResponse(res);
      tableData.value = response?.records || [];
      total.value = response?.total || 0;
    } catch (error) {
      console.error("Error loading data:", error);
      message.error("Failed to load data");
    } finally {
      isLoading.value = false;
    }
  };

  const handleSearch = () => {
    page.value = 1;
    searchQuery.value.page = 1;
    loadData();
  };

  // get query params
  const getQueryParams = () => {
    const params = {};

    Object.keys(searchQuery.value).forEach(key => {
      if (key !== 'sortBy' && key !== 'orderBy') {
        params[key] = searchQuery.value[key];
      }
    });

    // deal with sorting
    if (searchQuery.value.sortBy && searchQuery.value.orderBy) {
      params.orderItemList = [{
        column: searchQuery.value.sortBy,
        asc: searchQuery.value.orderBy === "asc",
      }];
    } else {
      params.orderItemList = null;
    }

    return params;
  };

  const resetSearchQuery = () => {
    searchQuery.value = createDefaultQuery();
  };


  // deal with page change
  const handlePageChange = (newPage) => {
    page.value = newPage;
    searchQuery.value.page = newPage;
    loadData();
  };

  // deal with items per page change
  const handleItemsPerPageChange = (newItemsPerPage) => {
    itemsPerPage.value = newItemsPerPage;
    searchQuery.value.size = newItemsPerPage;
    page.value = 1;
    searchQuery.value.page = 1;
    loadData();
  };

  const handleSortChange = (options) => {
    console.log("options", options);
    const sortBy = options[0]?.key
    const orderBy = options[0]?.order
    searchQuery.value.sortBy = sortBy
    searchQuery.value.orderBy = orderBy
    page.value = 1
    searchQuery.value.page = 1
    loadData()
  }

  // initial load
  onMounted(() => {
    loadData();
  });

  return {
    // state
    searchQuery,
    isLoading,
    tableData,
    itemsPerPage,
    page,
    items,
    total,

    // methods
    loadData,
    handleSearch,
    resetSearchQuery,
    handlePageChange,
    handleItemsPerPageChange,
    getQueryParams,
    handleSortChange,
  };
};
