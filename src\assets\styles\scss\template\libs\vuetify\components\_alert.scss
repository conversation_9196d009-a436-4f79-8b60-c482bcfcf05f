@use "@styles/scss/base/mixins";
@use "@configured-variables" as variables;

/* 👉 Alert
/ ℹ️ custom icon styling */

$alert-prepend-icon-font-size: 1.375rem !important;

.v-alert {
  .v-alert__content {
    line-height: 1.375rem;
  }

  &:not(.v-alert--prominent) .v-alert__prepend {
    block-size: 1.875rem !important;
    inline-size: 1.875rem !important;

    .v-icon {
      margin: auto;
      block-size: 1.375rem !important;
      font-size: 1.375rem !important;
      inline-size: 1.375rem !important;
    }
  }

  .v-alert-title {
    margin-block-end: 0.25rem;
  }

  .v-alert__close {
    .v-btn--icon {
      .v-icon {
        block-size: 1.25rem;
        font-size: 1.25rem;
        inline-size: 1.25rem;
      }

      .v-btn__overlay,
      .v-ripple__container {
        opacity: 0;
      }
    }
  }

  &:not(.v-alert--prominent) {
    /* stylelint-disable-next-line no-duplicate-selectors */
    .v-alert__prepend {
      border-radius: 0.375rem;
    }

    &.v-alert--variant-flat,
    &.v-alert--variant-elevated {
      .v-alert__prepend {
        background-color: #fff;

        @include mixins.elevation(2);
      }
    }

    &.v-alert--variant-tonal {
      .v-alert__prepend {
        z-index: 1;
        background-color: rgb(var(--v-theme-surface));
      }
    }
  }
}

.v-alert.v-alert--density-compact {
  border-radius: 0.25rem;
}

.v-alert.v-alert--density-default {
  border-radius: 0.5rem;
}

@each $color-name in variables.$theme-colors-name {
  .v-alert {
    &:not(.v-alert--prominent) {
      &.bg-#{$color-name},
      &.text-#{$color-name} {
        .v-alert__prepend .v-icon {
          color: rgb(var(--v-theme-#{$color-name})) !important;
        }
      }

      &.v-alert--variant-tonal {
        &.text-#{$color-name},
        &.bg-#{$color-name} {
          .v-alert__underlay {
            background: rgb(var(--v-theme-#{$color-name})) !important;
          }

          .v-alert__prepend {
            background-color: rgb(var(--v-theme-#{$color-name}));

            .v-icon {
              color: #fff !important;
            }
          }
        }
      }

      &.v-alert--variant-outlined {
        &.text-#{$color-name},
        &.bg-#{$color-name} {
          .v-alert__prepend {
            background-color: rgba(var(--v-theme-#{$color-name}), 0.16);
          }
        }
      }
    }
  }
}
