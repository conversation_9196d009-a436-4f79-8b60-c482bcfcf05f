export const useSubTaskType = () => {
    // Declare a reactive list to store subtask types
    const subTaskTypeList = ref([])
    // Function to load subtask type data from the API
    const loadData = async () => {
        const res = await $api('api/pin-generator/v1/batch-task/sub-task/type', {
            method: 'GET',
        })
        // Update subTaskTypeList with transformed response data if res.data is an array
        subTaskTypeList.value = Array.isArray(res.data)
            ? res.data.map(item => ({
                // Spread the original item properties
                ...item,
                title: item, // Set the display title using the name property
                value: item    // Assign the id as the value property
            })) : [];
    }
    // Execute loadData when the component is mounted
    onMounted(async () => {
        await loadData()
    })
    // Return the reactive subTaskTypeList for use in the component
    return { subTaskTypeList }
}

