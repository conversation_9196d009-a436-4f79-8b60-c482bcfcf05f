import { AppContentLayoutNav, ContentWidth, FooterType, NavbarType } from '@layouts/enums'

import { Skins } from '@/config/enums'
import { VIcon } from 'vuetify/components/VIcon'
import { breakpointsVuetifyV3 } from '@vueuse/core'
import { defineThemeConfig } from '@/config'
import logo from '@images/logo.svg?raw'

// ❗ Logo SVG must be imported with ?raw suffix



export const { themeConfig, layoutConfig } = defineThemeConfig({
  app: {
    title: 'razer',
    logo: h('div', { innerHTML: logo, style: 'line-height:0; color: rgb(var(--v-global-theme-primary))' }),
    contentWidth: ContentWidth.Fluid,
    contentLayoutNav: AppContentLayoutNav.Vertical,
    overlayNavFromBreakpoint: breakpointsVuetifyV3.lg - 1, // 1 for matching with vuetify breakpoint. Docs: https://next.vuetifyjs.com/en/features/display-and-platform/
    i18n: {
      enable: false,
      defaultLocale: 'en',
      langConfig: [
        {
          label: 'English',
          i18nLang: 'en',
          isRTL: false,
        },
        {
          label: 'French',
          i18nLang: 'fr',
          isRTL: false,
        },
        {
          label: 'Arabic',
          i18nLang: 'ar',
          isRTL: true,
        },
      ],
    },
    // Whether to show search bar
    showSearchBar: false,
    // Whether to show theme switcher
    showThemeSwitcher: false,
    // Show shortcuts
    showShortcuts: false,
    // Show notifications
    showNotifications: true,
    // Whether to show customizer
    showCustomizer: true,
    theme: 'dark',
    skin: Skins.Default,
    iconRenderer: VIcon,
  },
  navbar: {
    type: NavbarType.Sticky,
    navbarBlur: true,
  },
  footer: { type: FooterType.Static },
  verticalNav: {
    isVerticalNavCollapsed: false,
    defaultNavItemIconProps: { icon: '' },
    isVerticalNavSemiDark: false,
  },
  horizontalNav: {
    type: 'sticky',
    transition: 'slide-y-reverse-transition',
    popoverOffset: 6,
  },

  /*
    // ℹ️  In below Icons section, you can specify icon for each component. Also you can use other props of v-icon component like `color` and `size` for each icon.
    // Such as: chevronDown: { icon: 'tabler-chevron-down', color:'primary', size: '24' },
    */
  icons: {
    chevronDown: { icon: 'tabler-chevron-down' },
    chevronRight: { icon: 'tabler-chevron-right', size: 20 },
    close: { icon: 'tabler-x', size: 22 },
    verticalNavPinned: { icon: 'tabler-menu-2', size: 22 },
    verticalNavUnPinned: { icon: 'tabler-menu-2', size: 22 },
    sectionTitlePlaceholder: { icon: 'tabler-minus', size: 22 },
  },
})
