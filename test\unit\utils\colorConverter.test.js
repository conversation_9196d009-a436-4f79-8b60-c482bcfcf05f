import { hexToRgb, rgbaToHex } from '@/utils/colorConverter';

describe('colorConverter', () => {
  test('hexToRgb converts hex to rgb correctly', () => {
    expect(hexToRgb('#03F')).toBe('0,51,255');
    expect(hexToRgb('#0033FF')).toBe('0,51,255');
    expect(hexToRgb('#FFFFFF')).toBe('255,255,255');
    expect(hexToRgb('#000000')).toBe('0,0,0');
    expect(hexToRgb('invalid')).toBeNull();
  });

  test('rgbaToHex converts rgba to hex correctly', () => {
    expect(rgbaToHex('rgba(255, 0, 0, 1)')).toBe('#ff0000ff');
    expect(rgbaToHex('rgba(255, 0, 0, 0.5)')).toBe('#ff000080');
    expect(rgbaToHex('rgb(0, 255, 0)')).toBe('#00ff00');
    expect(rgbaToHex('rgba(0, 0, 255, 0)')).toBe('#0000ff00');
  });
}); 