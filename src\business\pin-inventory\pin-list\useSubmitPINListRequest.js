import message from '@/utils/message'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

export const initialFormState = {
  batchName: '',
  productId: null,
  razerGoldPINStatus: null,
  redemptionFromId: null,
  redemptionRegionId: null,
  redemptionCurrencyCode: null,
  pinCardTypeId: null,
  activationPeriodStart: null,
  activationPeriodEnd: null,
  redemptionPeriodStart: null,
  redemptionPeriodEnd: null,
  timezone: null,
}

export const useSubmitPINListRequest = (formRef) => {
  const router = useRouter()
  const isSubmitting = ref(false)

  const submitRequest = async () => {
    await confirmDialog({
      title: 'Submit RG PIN List Request?',
      text: 'Once submitted, this action cannot be undone.',
      confirmButtonText: 'SUBMIT',
      confirmButtonColor: 'primary',
      cancelButtonText: 'CANCEL',
      async onConfirm(close) {
        if (isSubmitting.value) return

        const { valid, errors } = await formRef.value?.validate()

        if (!valid) {
          message.error(errors)
          return
        }

        try {
          isSubmitting.value = true

          const params = formRef.value?.getParams()

          const res = await $api('/api/pin-inventory/v1/pin-list/csv', {
            method: 'POST',
            body: params,
          })

          message.success('Successfully Submitted PIN List Request')
          close()
          await router.push('/pin-inventory/pin-list')
        } catch (error) {
          message.error(error?._data?.message || error?.message || error)
        } finally {
          isSubmitting.value = false
        }
      },
    })
  }

  const cancelRequest = () => {
    formRef.value?.resetForm()
    router.back()
  }

  return {
    isSubmitting,
    submitRequest,
    cancelRequest,
  }
}
