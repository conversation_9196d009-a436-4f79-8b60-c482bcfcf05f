export const useRole = (roleName) => {
  const roleList = ref([])
  const loadData = async () => {
    const res = await $api('/api/admin-api/v1/role/query-all', {
      method: 'GET',
      params: {
        roleName
      }
    })
    roleList.value = res.data?.map(item => ({
      ...item,
      title: item.name + ' (' + item.userGroupName + ')',
      value: item.id
    }))
  }
  onMounted(async () => {
    await loadData()
  })


  return { roleList }
}

