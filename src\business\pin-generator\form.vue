<script setup>
import { usePin } from '@/business/pin-generator/usePin'
import { requiredValidator } from '@/utils/validators'

const { razerGoldPINGroups } = usePin()

// form data
const formModel = defineModel({
  type: Object,
  required: true,
})

const props = defineProps({
  type: {
    type: String,
  required: true,
  },
})

const formRef = ref(null)
const isApprove = computed(() => props.type === 'approve')

// expose methods and data to parent component
defineExpose({
  validate: () => {
    return formRef.value.validate()
  },
  getParams: () => {
    const params = {
      needSFTP: formModel.value.needSFTP || false,
      razerGoldPINGroup: formModel.value.razerGoldPINGroup,
      quantity: formModel.value.quantity
    }
    if (formModel.value.id) {
      params.id = formModel.value.id
    }
    return params
  },
  resetForm: () => {
    formModel.value = {
        razerGoldPINGroup: null,
        quantity: null,
        needSFTP: false,
    }
  }
})
const handleSelectPinGroup = function(id) {
  formModel.value.razerGoldPINGroup = id
}

const updateSelectedSFTPFile = function (value) {
  formModel.value.needSFTP = value
}
</script>

<template>
  <VCard ref="cardRef">
    <VRow class="d-flex">
      <VCol cols="12" md="5" xl="5" style="width: 40%;">
        <div class="form-wrapper">
          <VCardTitle>
            1. Enter RG PIN Generation Info
          </VCardTitle>
          <VCardText>
            <VForm ref="formRef">
              <VRow>
                <VCol cols="12">
                  <AppAutocomplete v-model="formModel.razerGoldPINGroup" placeholder="Select RG PIN Group" label="RG PIN Group*" :disabled="isApprove"
                    :items="razerGoldPINGroups" clearable clear-icon="tabler-x" @update:model-value="handleSelectPinGroup" :rules="[requiredValidator]"
                    :item-title="name" :item-value="id" />
                </VCol>

                <VCol cols="12">
                  <AppTextField type="number" v-model="formModel.quantity" label="Quantity Of PINs*" placeholder="0" :rules="[requiredValidator]" :disabled="isApprove"/>
                </VCol>
                <VCol cols="12">
                    <VCheckbox label="Create SFTP File"  :model-value="formModel.needSFTP"  @update:model-value="updateSelectedSFTPFile" :disabled="isApprove"/>
                </VCol>

              </VRow>
            </VForm>
          </VCardText>
        </div>
      </VCol>
    </VRow>
  </VCard>
</template>

<style lang="scss" scoped>
.form-wrapper {
  background: #1e1e1e;
  border-radius: 16px;
  height: 100%;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
}

:deep(.v-card-text) {
  padding-bottom: 0px !important;
}


:deep(.v-switch .v-label) {
  padding-inline-end: 10px;
  padding-inline-start: 0px;
}

.v-row:last-child {
  margin-top: auto;
  padding: 16px;
  background: var(--v-theme-surface);
  position: sticky;
  bottom: 0;
  z-index: 1;
}
</style>