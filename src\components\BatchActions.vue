<template>
  <div v-if="count > 0" class="flex items-center gap-4 px-4 py-2 border rounded bg-gray-50">
    <span class="text-primary font-semibold text-base">已选 {{ count }} 项</span>
    <button
      class="text-gray-400 hover:text-primary text-lg cursor-pointer bg-transparent border-none outline-none"
      title="清空选择"
      @click="$emit('clear')"
      type="button"
    >
      <i class="i-mdi-close-circle"></i>
    </button>
    <div class="border-l h-5 mx-2"></div>
    <button
      class="text-error hover:bg-error/10 px-3 py-1 rounded border border-error bg-transparent cursor-pointer"
      @click="$emit('batchDelete')"
      type="button"
    >批量删除</button>
    <slot></slot>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
const props = defineProps({
  count: {
    type: Number,
    default: 0
  }
})
defineEmits(['clear', 'batchDelete'])
</script> 