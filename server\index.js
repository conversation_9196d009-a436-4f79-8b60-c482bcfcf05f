import express from "express";
import path from "path";
import { fileURLToPath } from "url";
import https from "https";
import net from "net";
import { httpPort, httpsPort, ssl } from "../config/index.js";
import compression from 'compression'

// Get equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.url}`);
  next();
});

// Set correct MIME types
app.use((req, res, next) => {
  if (req.path.endsWith(".js")) {
    res.type("application/javascript; charset=utf-8");
  } else if (req.path.endsWith(".css")) {
    res.type("text/css; charset=utf-8");
  }
  next();
});


app.set("x-powered-by", false);
 
app.use(function (req, res, next) {
  res.setHeader("Content-Security-Policy", "frame-ancestors 'none'");
  res.setHeader("X-Frame-Options", "deny");
  res.setHeader("X-Content-Type-Options", "nosniff");
  res.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
  res.setHeader(
    "Strict-Transport-Security",
    "max-age=31536000; includeSubDomains; preload"
  );
  next();
});

// 启用 Gzip 压缩
app.use(compression())

// 添加缓存控制
app.use(express.static(path.join(__dirname, "../dist"), {
  maxAge: '1d',
  etag: true,
  fallthrough: true,
  index: false
}))

// Check if port is in use
const checkPort = (port) => {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.once("error", () => resolve(false));
    server.once("listening", () => {
      server.close();
      resolve(true);
    });
    server.listen(port);
  });
};

const startServer = async () => {
  // If port 80 is in use, use port 3000
  const port = (await checkPort(httpPort)) ? httpPort : 80;
  console.log("Starting server on port:", port);

  // Static resource middleware
  app.use(express.static(path.join(__dirname, "../dist")));

  // Health check endpoint
  app.get("/health", (req, res) => {
    res.status(200).json({ utcDateTime: new Date().toISOString() });
  });

  // All routes return index.html
  app.get("*", (req, res) => {
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });
    res.sendFile(path.join(__dirname, "../dist/index.html"));
  });

  // Start HTTP server
  app
    .listen(port, "0.0.0.0", () => {
      console.log(`HTTP Server running on port ${port}`);
    })
    .on("error", (err) => {
      console.error("Server error:", err);
    });

  // Add error handling middleware before all routes
  app.use((err, req, res, next) => {
    console.error("Error:", err);
    res.status(500).json({ error: err.message });
  });
};

startServer();

// Start HTTPS server if SSL certificates are configured
if (ssl.key && ssl.cert && ssl.ca) {
  const credentials = {
    ca: ssl.ca.replace(/@@/g, "\n"),
    key: ssl.key.replace(/@@/g, "\n"),
    cert: ssl.cert.replace(/@@/g, "\n"),
  };
  https
    .createServer(credentials, app)
    .listen(httpsPort, () => {
      console.log(`HTTPS Server running on port ${httpsPort}`);
    })
    .on("error", (err) => {
      console.error("HTTPS Server error:", err);
    });
}


export default app;
