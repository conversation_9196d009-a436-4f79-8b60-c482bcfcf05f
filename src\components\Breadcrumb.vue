<template>
  <nav aria-label="breadcrumb">
    <v-breadcrumbs
      :items="breadcrumbItems"
      class="pa-0"
    >
      <template #divider>
        <span class="text-white">/</span>
      </template>
      
      <template #title="{ item }">
        <span
          v-if="item.active"
          class="breadcrumb-text"
          :class="{ 'active': item.active }"
        >
          {{ item.title }}
        </span>
        <span
          v-else
          class="breadcrumb-text"
          @click="handleClick(item)"
        >
          {{ item.title }}
        </span>
      </template>
    </v-breadcrumbs>
  </nav>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import navigation from '@/config/navigation'
import { isPlainObject } from '@/utils/helpers'
const route = useRoute()
const router = useRouter()

// 检查路径是否为有效路由
const isValidPath = (path) => {
  if (!path) return false
  try {
    const route = router.resolve(path)
    return route && route.matched.length > 0
  } catch {
    return false
  }
}

// Find all parent nodes corresponding to the current path
const findPath = (tree, currentPath, parentPath = []) => {
  for (const node of tree) {
    // Build the current node path
    let nodePath = ''
    if (isPlainObject(node.to)) {
      nodePath = node.to.path || ''
    } 
    
    // Add current node to the path
    const currentParentPath = [...parentPath, node]
    
    // If it's a parent menu with child nodes, check child nodes
    if (node.children) {
      // Check if child nodes contain current path
      const childMatch = node.children?.some(child => {
        let childPath = ''
        if (isPlainObject(child.to)) {
          childPath = child.to.path || ''
        }
        return currentPath.startsWith(childPath)
      })
      
      if (childMatch) {
        // If child node matches, continue searching child nodes
        const found = findPath(node.children, currentPath, currentParentPath)
        if (found) return found
      }
    }
    
    // Check if current node matches
    if (nodePath && currentPath.startsWith(nodePath)) {
      return currentParentPath
    }
  }
  return null
}

const breadcrumbItems = computed(() => {
  // Get complete hierarchy of current path
  const pathNodes = findPath(navigation, route.path)

  if (!pathNodes) {
    return []
  }

  // Add current route if not already in parentNodes
  if (!pathNodes?.some(node => node?.to?.path === route.path)) {
    pathNodes.push({
      title: route.meta.breadcrumb,
      path: route.path,
      active: false,
    })
  }

  // Convert to breadcrumb items
  const items = pathNodes.map((node, index) => ({
    title: node.title,
    path: typeof node.to === 'string' ? '/' + node.to.replace(/-/g, '/') : node?.to?.path,
    active: index === pathNodes.length - 1,
  }))
  return items
})

const handleClick = (item) => {
  if (!item.path || !isValidPath(item.path)) return
  
  router.push(item.path)
}
</script>

<style scoped>
.v-breadcrumbs {
  min-height: 40px;
}

:deep(.v-breadcrumbs-item--disabled){
  opacity: 1;
}

:deep(.v-breadcrumbs-item) {
  padding: 0;
}

.breadcrumb-text {
  padding: 0;
  color: white;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.375rem;
  letter-spacing: 0.0071428571em;
}

.breadcrumb-text.active {
  color: rgb(var(--v-theme-primary));
  cursor: default;
}

.breadcrumb-text.disabled {
  cursor: default;
  opacity: 0.6;
}

.breadcrumb-text:not(.active):not(.disabled):hover {
  opacity: 0.8;
}
</style> 