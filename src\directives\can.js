import { useAuthStore } from '@/stores/useAuthStore'

/**
 * Check if user has permission
 * @param {string} action - Action to check (e.g. 'Add', 'Edit', etc.)
 * @param {string} resource - Resource name (e.g. 'Users', 'Roles', etc.)
 * @param {Array} permissions - User's permission list
 * @returns {boolean}
 */
export function hasPermission(action, resource, permissions = []) {
  // If no permissions list provided, get from store
  const authStore = useAuthStore()
  const userPermissions = permissions.length ? permissions : authStore.permissions

  // Check if permission list contains this permission
  return userPermissions?.some(permission => 
    permission.action === action && 
    permission.resource.toLowerCase() === resource.toLowerCase()
  )
}

export const vCan = {
  mounted(el, binding) {
    const { value } = binding
    if (!value || !Array.isArray(value) || value.length !== 2) {
      console.warn('v-can directive requires an array with exactly two elements: [action, resource]')
      return
    }

    const [action, resource] = value
    if (!hasPermission(action, resource)) {
      el.style.display = 'none'
    }
  },

  updated(el, binding) {
    const { value } = binding
    if (!value || !Array.isArray(value) || value.length !== 2) return

    const [action, resource] = value
    el.style.display = hasPermission(action, resource) ? '' : 'none'
  }
}

// Export plugin installation function
export function installCanDirective(app) {
  app.directive('can', vCan)
} 