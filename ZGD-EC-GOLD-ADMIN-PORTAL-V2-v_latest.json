{"executionRoleArn": "arn:aws:iam::903306222264:role/%ROLE%", "taskRoleArn": "arn:aws:iam::903306222264:role/%ROLE%", "containerDefinitions": [{"logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "%LOG_NAME%", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "%LOG_STREAM%"}}, "portMappings": [{"hostPort": 80, "protocol": "tcp", "containerPort": 80}, {"hostPort": 443, "protocol": "tcp", "containerPort": 443}], "environment": [{"name": "NODE_ENV", "value": "%NODE_ENV%"}], "secrets": "%SECRET%", "mountPoints": [], "volumesFrom": [], "image": "903306222264.dkr.ecr.ap-southeast-1.amazonaws.com/gold-admin-portal-v2:develop_v", "essential": true, "name": "ZGD-EC-GOLD-ADMIN-PORTAL-V2"}], "placementConstraints": [], "memory": "%MEMORY%", "family": "ZGD-ECS-FGTD-GOLD-ADMIN-PORTAL-V2", "requiresCompatibilities": ["FARGATE", "EC2"], "networkMode": "awsvpc", "cpu": "%CPU%", "volumes": []}