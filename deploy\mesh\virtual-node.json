{"meshName": "%MESH_NAME%", "spec": {"backendDefaults": {"clientPolicy": {"tls": {"enforce": false, "validation": {"trust": {"file": {"certificateChain": "/keys/%MESH_ENV%-%MESH_TYPE%-chain.pem"}}}}}}, "backends": [{"virtualService": {"virtualServiceName": "order.%MESH_DOMAIN%"}}], "listeners": [{"portMapping": {"port": 80, "protocol": "http"}, "healthCheck": {"healthyThreshold": 5, "intervalMillis": 30000, "path": "/", "port": 80, "protocol": "http", "timeoutMillis": 5000, "unhealthyThreshold": 2}, "tls": {"certificate": {"file": {"certificateChain": "/keys/%MESH_ENV%-%MESH_TYPE%-chain.pem", "privateKey": "/keys/%MESH_ENV%-%MESH_TYPE%-key.pem"}}, "mode": "STRICT"}}], "serviceDiscovery": {"awsCloudMap": {"namespaceName": "%MESH_DOMAIN%", "serviceName": "%SERVICE_DISCOVERY%"}}}, "virtualNodeName": "%SERVICE_DISCOVERY%_vn"}