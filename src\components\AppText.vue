<script setup>
import { hasPermission } from '@/directives/can'
defineOptions({
  name: 'AppText',
  inheritAttrs: false,
})

const props = defineProps({
  auth: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['click'])

const { class: _class, ...restAttrs } = useAttrs()

const [action, resource] = props.auth

const canView = computed(() => hasPermission(action, resource))

const dynamicClass = computed(() => {
 
  return [
    _class,
    canView.value ? 'text-[#BBBBBB] hover:underline cursor-pointer hover:text-primary' : 'text-[#BBBBBB]',
  ]
})

const handleClick = (e) => {
  if (canView.value) {
    emit('click', e)
  }
}
</script>

<template>
  <a
    :class="dynamicClass"
    v-bind="restAttrs"
    @click.stop="handleClick"
  >
    <slot />
  </a>
</template>

<style scoped>

</style>