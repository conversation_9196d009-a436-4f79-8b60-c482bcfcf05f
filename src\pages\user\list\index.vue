<script setup>
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { useRole } from "@/hooks/useRole";
import { useGroup } from "@/hooks/useGroup";
import { dateUtil } from "@/utils/day";
import message from "@/utils/message";
import { getActiveRoleList } from "@/business/user/list/useUserDetail";
import { hasPermission } from "@/directives/can";
import { PAGINATION_OPTIONS as paginationOptions } from "@/utils/constants";

// 👉 create default query
const createDefaultQuery = () => {
  return {
    email: "",
    roleIdList: [],
    userGroupIdList: [],
    createdOn: "",
    lastModified: "",
    status: null,
    page: 1,
    size: 10,
    sortBy: null,
    orderBy: null,
  };
};

// 👉 Store
const searchQuery = ref(createDefaultQuery());
const isLoading = ref(false);
const usersData = ref(null);

// Data table options
const itemsPerPage = ref(10);
const page = ref(1);
const selectedRows = ref([]);
const selectedIds = ref([]);

const expanded = ref([]);

const handleSortChange = (options) => {
  const sortBy = options[0]?.key;
  const orderBy = options[0]?.order;
  searchQuery.value.sortBy = sortBy;
  searchQuery.value.orderBy = orderBy;
  page.value = 1;
  searchQuery.value.page = 1;
  loadData();
};

const handleSelectChange = selection => {
  selectedRows.value = usersData.value.records.filter(item => selection.includes(item.accountId));
}

const { userGroupList } = useGroup();
const { roleList } = useRole();

// 👉 Table Headers
const headers = [
  {
    title: "", // checkbox column
    key: "data-table-select",
    sortable: false,
    fixed: true,
    width: 50,
  },
  {
    title: "No",
    key: "no",
    sortable: false,
    width: 70,
  },
  {
    title: "Email",
    key: "email",
  },
  {
    title: "Name",
    key: "name",
  },
  {
    title: "User Groups & Roles",
    key: "groups",
    sortable: false,
  },
  {
    title: "Created On",
    key: "createdDateTime",
  },
  {
    title: "Last Modified",
    key: "updatedDateTime",
  },
  {
    title: "Status",
    key: "status",
    sortable: false,
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    fixed: true,
    width: 120,
  },
];

// 👉 reset query params
const resetQueryParams = () => {
  searchQuery.value = createDefaultQuery();
};

// 👉 get query params
const getQueryParams = () => {
  const params = {};
  params.page = searchQuery.value.page;
  params.size = searchQuery.value.size;
  params.emailLike = searchQuery.value.email;
  params.userGroupIdList = searchQuery.value.userGroupIdList;
  params.roleIdList = searchQuery.value.roleIdList;

  params.statusId = searchQuery.value.status;
  if (searchQuery.value.sortBy && searchQuery.value.orderBy) {
    params.orderItemList = [
      {
        column: searchQuery.value.sortBy || "",
        asc: searchQuery.value.orderBy === "asc",
      },
    ];
  } else {
    params.orderItemList = null;
  }
  // Process creation time
  if (searchQuery.value.createdOn) {
    const [start, end] = searchQuery.value.createdOn.split("to");
    if (start) {
      params.gmtCreateGe = dateUtil.format(start, "YYYY-MM-DDTHH:mm:ss.SSSZ");
      params.gmtCreateLe = dateUtil.format(start, "YYYY-MM-DDT23:59:59.SSSZ");
    }
    if (end) {
      params.gmtCreateLe = dateUtil.format(end, "YYYY-MM-DDT23:59:59.SSSZ");
    }
  }
  // Process last modified time
  if (searchQuery.value.lastModified) {
    const [start, end] = searchQuery.value.lastModified.split("to");
    if (start) {
      params.gmtUpdateGe = dateUtil.format(start, "YYYY-MM-DDTHH:mm:ss.SSSZ");
      params.gmtUpdateLe = dateUtil.format(start, "YYYY-MM-DDT23:59:59.SSSZ");
    }
    if (end) {
      params.gmtUpdateLe = dateUtil.format(end, "YYYY-MM-DDT23:59:59.SSSZ");
    }
  }
  return params;
};

// 👉 load data
const loadData = async () => {
  try {
    isLoading.value = true;
    usersData.value = [];
    const params = getQueryParams();
    const res = await $api("/api/admin-api/v1/account/page", {
      method: "POST",

      body: params,
    });
    usersData.value = res.data;
  } catch (error) {
    console.error("Error loading data:", error);
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  loadData();
});

const handleSearch = () => {
  page.value = 1;
  searchQuery.value.page = 1;
  loadData();
};

const handlePageChange = (newPage) => {
  page.value = newPage;
  searchQuery.value.page = newPage;
  selectedRows.value = [];
  selectedIds.value = [];
  loadData();
};

const handleItemsPerPageChange = (newItemsPerPage) => {
  newItemsPerPage = parseInt(newItemsPerPage, 10);
  itemsPerPage.value = newItemsPerPage;
  searchQuery.value.size = newItemsPerPage;
  page.value = 1;
  searchQuery.value.page = 1;
  selectedRows.value = [];
  selectedIds.value = [];
  loadData();
};

// 👉 computed
const accounts = computed(() =>
  usersData.value?.records?.map((item, index) => ({
    ...item,
    no: (page.value - 1) * itemsPerPage.value + index + 1,
  }))
);

const totalUsers = computed(() => usersData.value?.total || 0);

const hasShowEnable = computed(() =>
  selectedRows.value.some((item) => item.accountStatusId === 0)
);

const hasShowDisable = computed(() =>
  selectedRows.value.some((item) => item.accountStatusId === 1)
);

// 👉 search filters
const status = [
  {
    title: "Enable",
    value: 1,
  },
  {
    title: "Disable",
    value: 0,
  },
];


// 👉 Get user group roles
const getUserGroupRoles = (userGroupList) => {
  if (!userGroupList) return [];
  const roles = [];
  userGroupList.forEach((item) => {
    if (item.roleList) {
      const activeRoleList = getActiveRoleList(item.roleList);
      roles.push(...activeRoleList);
    }
  });
  return roles;
};

// 👉 Get user groups
const getUserGroups = (userGroupList) => {
  const groups = userGroupList.filter((item) => {
    const roleList = item.roleList;
    if (!roleList) return false;
    return roleList.some((role) => role.statusId === 1);
  });
  return groups;
};

// 👉 Resolve user groups and roles display text
const resolveUserGroupAndRoles = (userGroupList) => {
  const groups = getUserGroups(userGroupList);
  const roles = getUserGroupRoles(userGroupList);

  if (groups.length === 0) {
    return "";
  }

  // If there is only one userGroup and no roles, return userGroup
  if (groups.length === 1 && !roles.length) {
    return groups[0].name;
  }

  // If there is only one userGroup and one role, return userGroup and role
  if (groups.length === 1 && roles.length === 1) {
    return `${groups[0].name} & ${roles[0].name}`;
  }

  // If there are multiple userGroups and no roles, return groups count
  if (groups.length && !roles.length) {
    return `${groups.length} groups`;
  }

  // If there are multiple userGroups and roles, return groups and roles count
  if (groups.length && roles.length) {
    return `${groups.length} groups & ${roles.length} roles`;
  }
};

const router = useRouter();

const handleAddUser = () => {
  router.push("/user/list/add");
};

const viewUser = async (item) => {
  router.push(`/user/list/detail?id=${item.accountId}`);
};

// 👉 edit user
const editUser = async (item) => {
  router.push(`/user/list/edit?id=${item.accountId}`);
};

// 👉 batch enable
const batchEnable = () => {
  let isConfirmed = false;
  confirmDialog({
    title: "Confirm Enable Users",
    text: "Are you sure you want to enable the selected user(s)? Once enabled, they will regain access to the system.",
    confirmButtonText: "CONFIRM ENABLE",
    confirmButtonColor: "primary",
    cancelButtonText: "CANCEL",
    async onConfirm(close) {
      if (isConfirmed) return;
      isConfirmed = true;
      try {
        const params = selectedRows.value.map((item) => ({
          id: item.accountId,
        }));
        await $api(`/api/admin-api/v1/account/batch/status?status=1`, {
          method: "PUT",
          body: params,
        });
        message.success("Users enabled successfully");
        close();
        selectedRows.value = [];
        selectedIds.value = [];
        loadData();
      } catch (error) {
        message.error("Failed to enable users");
      } finally {
        isConfirmed = false;
      }
    },
  });
};

// 👉 batch disable
const batchDisable = () => {
  let isConfirmed = false;
  confirmDialog({
    title: "Confirm Disable Users",
    text: "Are you sure you want to disable the selected user(s)? Once disabled, they will lose access to the system.",
    confirmButtonText: "CONFIRM DISABLE",
    confirmButtonColor: "error",
    cancelButtonText: "CANCEL",
    async onConfirm(close) {
      if (isConfirmed) return;
      isConfirmed = true;
      try {
        console.log("onConfirm", selectedRows.value);
        const params = selectedRows.value.map((item) => ({
          id: item.accountId,
        }));
        await $api(`/api/admin-api/v1/account/batch/status?status=0`, {
          method: "PUT",
          body: params,
        });
        message.success("Users disabled successfully");
        close();
        selectedRows.value = [];
        selectedIds.value = [];
        loadData();
      } catch (error) {
        message.error("Failed to disable users");
      } finally {
        isConfirmed = false;
      }
    },
  });
};

const isExpanded = (item) => {
  return expanded.value.includes(item.accountId);
};

// 👉 Check if row can be expanded
const canExpand = (item) => {
  const groups = getUserGroups(item.userGroupList);
  const roles = getUserGroupRoles(item.userGroupList);
  return groups.length > 1 || roles.length > 1;
};

// 👉 Handle row click
const handleRowClick = (event, { item }) => {
  console.log("canExpand", canExpand(item))
  if (!canExpand(item)) return;

  const index = expanded.value.indexOf(item.accountId);
  if (index === -1) {
    expanded.value.push(item.accountId);
  } else {
    expanded.value.splice(index, 1);
  }

  console.log("expanded", expanded.value);
};

const clearAllSelected = () => {
  selectedRows.value = [];
  selectedIds.value = [];
};

// 👉 toggle status
const toggleStatusId = async (item, e) => {
  e.preventDefault();

  if (!hasPermission('Edit', 'UserList')) return
  
  confirmDialog({
    title: `${item.accountStatusId === 1 ? 'Disable User?' : 'Enable User?'}`,
    text: `${item.accountStatusId === 1 ? 'Once disabled, user will lose access to the system.' : 'Once enabled, user will regain access to the system.'}`,
    confirmButtonText: item.accountStatusId === 1  ? "DISABLE" : "ENABLE",
    confirmButtonColor: item.accountStatusId === 1 ? "error" : "primary",
    cancelButtonText: 'CANCEL',
    async onConfirm(close) {
      const params = [{ id: item.accountId }]
      const status = item.accountStatusId === 1 ? 0 : 1
      await $api(`/api/admin-api/v1/account/batch/status?status=${status}`, {
        method: 'PUT',
        body: params
      })
      message.success('User status updated successfully')
      close()
      loadData()
    }
  })

}
</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            Users
            <VSpacer />
            <div class="app-user-search-filter d-flex align-center gap-4">
              <VBtn v-can="['Add', 'UserList']" @click="handleAddUser">
                CREATE
              </VBtn>
            </div>
          </div>
        </VCardTitle>
      </VCardItem>

      <VCardText class="px-0">
        <VRow>
          <!-- 👉 Search Email -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField
              v-model="searchQuery.email"
              placeholder="Search Email"
              clearable
              @update:model-value="handleSearch"
            />
          </VCol>
          <!-- 👉 Select Group -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete
              v-model="searchQuery.userGroupIdList"
              placeholder="Select User Group"
              :items="userGroupList"
              clearable
              clear-icon="tabler-x"
              multiple
              filterable
              @update:model-value="handleSearch"
            >
              <template #selection="{ item, index}">
                <span
                  v-if="index === 0"
                >
                  {{ item.title }}
                </span>
                <span
                  v-else-if="index === 1"
                  class="text-caption align-self-center text-primary font-weight-medium ml-2"
                >
                 (+{{ searchQuery.userGroupIdList.length - 1 }})
                </span>
              </template>
            </AppAutocomplete>
          </VCol>
          <!-- 👉 Select Role -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete
              v-model="searchQuery.roleIdList"
              placeholder="Select Role"
              :items="roleList"
              clearable
              clear-icon="tabler-x"
              multiple
              filterable
              @update:model-value="handleSearch"
            >
            <template #selection="{ item, index}">
                <span
                  v-if="index === 0"
                >
                  {{ item.title }}
                </span>
                <span
                  v-else-if="index === 1"
                  class="text-caption align-self-center text-primary font-weight-medium ml-2"
                >
                 (+{{ searchQuery.roleIdList.length - 1 }})
                </span>
              </template>
            </AppAutocomplete>
          </VCol>
          <!-- 👉 Select Created On -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppDateTimePicker
              v-model="searchQuery.createdOn"
              :config="{
                mode: 'range',
                enableTime: false,
                dateFormat: 'Y-m-d',
              }"
              placeholder="Select Created On"
              clearable
              @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Select Last Modified -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppDateTimePicker
              v-model="searchQuery.lastModified"
              :config="{
                mode: 'range',
                enableTime: false,
                dateFormat: 'Y-m-d',
              }"
              placeholder="Select Last Modified"
              clearable
              @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Select Status -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppSelect
              v-model="searchQuery.status"
              placeholder="Select Status"
              :items="status"
              clearable
              clear-icon="tabler-x"
              @update:model-value="handleSearch"
            />
          </VCol>
        </VRow>
      </VCardText>

      <!-- Selection hint -->
      <transition name="fade-transition" v-if="selectedRows.length">
        <VCardText
          class="d-flex align-center gap-4 py-2 px-4 bg-grey-lighten-4 justify-start"
        >
          <div class="d-flex align-center gap-4">
            <span class="text-primary font-weight-medium">{{
              selectedRows.length
            }}</span>
            items selected
          </div>
          <VIcon size="20" icon="mdi-close-circle" @click="clearAllSelected" v-tooltip="'Cancel'"/>
          <VDivider
            vertical
            color="#999"
            class="my-auto"
            v-can="['Edit', 'UserList']"
          />
          <VBtn
            @click="batchDisable"
            variant="tonal"
            color="default"
            v-can="['Edit', 'UserList']"
            data-test-id="disable"
            v-if="hasShowDisable"
          >
            DISABLE
          </VBtn>
          <VBtn
            @click="batchEnable"
            variant="tonal"
            color="primary"
            v-can="['Edit', 'UserList']"
            data-test-id="enable"
            v-if="hasShowEnable"
          >
            ENABLE
          </VBtn>
          <VSpacer />
        </VCardText>
      </transition>

      <!-- SECTION datatable -->
      <VDataTableServer
        v-model:model-value="selectedIds"
        :items="accounts"
        :item-value="(item) => item.accountId"
        :items-length="totalUsers"
        :headers="headers"
        :loading="isLoading"
        class="text-no-wrap"
        v-model:expanded="expanded"
        @click:row="handleRowClick"
        show-select
        @update:sort-by="handleSortChange"
        @update:modelValue="handleSelectChange"
      >
        <!-- Expanded-row -->
        <template #expanded-row="{ item }">
          <tr class="expanded-row">
            <td :colspan="4"></td>
            <td :colspan="1">
              <div class="expanded-content">
                <!-- Product Groups -->
                <div
                  class="group-section mb-4"
                  v-for="group in getUserGroups(item.userGroupList)"
                  :key="group.id"
                >
                  <h3 class="text-h6 font-weight-medium mb-2">
                    {{ group.name }}
                  </h3>
                  <div
                    class="group-items"
                    v-for="role in getActiveRoleList(group.roleList)"
                    :key="role.id"
                  >
                    <div class="group-item d-flex align-center">
                      <VIcon
                        icon="tabler-point"
                        size="16"
                        class="me-2"
                        color="grey-400"
                      />
                      <span class="text-grey-400">{{ role.name }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </td>
            <td :colspan="4"></td>
          </tr>
        </template>
        <!-- Email -->
        <template #[`item.email`]="{ item }">
          <AppText  @click.prevent="viewUser(item)" :auth="['View', 'UserList']"
          >{{ item.accountEmail }}</AppText>
        </template>

        <template #[`item.name`]="{ item }">
          {{ item.accountName }}
        </template>

        <!-- Groups -->
        <template #[`item.groups`]="{ item }">
          <div class="d-flex align-center">
            {{ resolveUserGroupAndRoles(item.userGroupList) }}
            <VIcon
              v-if="canExpand(item)"
              :icon="
                isExpanded(item) ? 'tabler-chevron-up' : 'tabler-chevron-down'
              "
              size="20"
              class="ms-2"
              color="grey-400"
            />
          </div>
        </template>

        <!-- Status -->
        <template #[`item.status`]="{ item }">
          <VSwitch :model-value="item.accountStatusId" @click.stop="toggleStatusId(item, $event)" :false-value="0" :true-value="1" :class="hasPermission('Edit', 'UserList') ? 'cursor-pointer ' : 'opacity-50'">
          </VSwitch>
        </template>

        <!-- Created -->
        <template #[`item.createdDateTime`]="{ item }">
          {{ dateUtil.format(item.createdDateTime) }}
        </template>

        <!-- Last Modified -->
        <template #[`item.updatedDateTime`]="{ item }">
          {{ dateUtil.format(item.updatedDateTime) }}
        </template>

        <!-- Actions -->
        <template #[`item.actions`]="{ item }">
          <IconBtn
            @click.stop="editUser(item)"
            v-tooltip="'Edit User'"
            v-can="['Edit', 'UserList']"
          >
            <VIcon icon="mdi-pencil"  class="hover:text-primary" />
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination
            :page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalUsers"
            @update:page="handlePageChange"
          >
            <div class="d-flex gap-3">
              <AppSelect
                :model-value="itemsPerPage"
                :items="paginationOptions"
                @update:model-value="handleItemsPerPageChange"
              />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
    <!-- 👉 Add New User -->
  </section>
</template>

<style lang="scss" scoped>
.custom-link {
  color: rgb(var(--v-theme-primary));
  text-decoration: none;
  cursor: pointer;
  font-weight: 400;
  padding: 0 8px;
  min-height: 36px;
  display: inline-flex;
  align-items: center;

  &:hover {
    opacity: 0.8;
    text-decoration: none;
    background: rgba(var(--v-theme-primary), 0.05);
    border-radius: 4px;
  }
}

// Add divider style
:deep(.v-theme--dark.v-divider--vertical) {
  height: 22px;
  opacity: 1;
}

// Add expanded row style
.expanded-row {
  position: relative;

  &::after {
    content: "";
    position: absolute;
    width: 100%;
    border-top: 1px solid transparent;
    top: -1px;
    left: 0;
  }
}

// Add expanded content style
.expanded-content {
  background-color: transparent;
  .group-section {
    h3 {
      color: rgb(var(--v-theme-on-surface));
      font-size: 0.875rem;
    }

    .group-items {
      padding-left: 8px;

      .group-item {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        span {
          font-size: 0.875rem;
        }
      }
    }
  }
}

// Add icon animation
.v-icon {
  transition: transform 0.3s ease;

  &.rotated {
    transform: rotate(180deg);
  }
}

:deep(.v-data-table) {
  .v-table__wrapper {
    overflow: auto;
  }

  tr:has(.tabler-chevron-up) .v-data-table-column--last-fixed {
    border-bottom: none;
  }

  .v-data-table-column--last-fixed {
    right: 0;
    border-bottom: 1px solid
      rgba(var(--v-border-color), var(--v-border-opacity));
  }
}


</style>
