import Big from 'big.js'
const bigUtil = {
  plus(a, b) {
    if (a === undefined || b === undefined) return 0
    return parseFloat(new Big(a).plus(b))
  },
  minus(a, b) {
    if (a === undefined || b === undefined) return 0
    return parseFloat(new Big(a).minus(b))
  },
  times(a, b) {
    if (a === undefined || b === undefined) return 0
    return parseFloat(new Big(a).times(b))
  },
  div(a, b) {
    if (a === undefined || b === undefined) return 0
    return parseFloat(new Big(a).div(b))
  },
}

export default bigUtil