#!/usr/bin/env bash
set -eu

mesh_replace_deploy() {
  
  sed -e "s;%MESH_NAME%;$MeshName;g" \
	  -e "s;%MESH_DOMAIN%;$MeshDomain;g" \
    -e "s;%SERVICE_DISCOVERY%;$ServiceDiscovery;g" \
    -e "s;%MESH_TYPE%;$MESH_TYPE;g" \
    -e "s;%MESH_SERVICE_NAME%;$TASK_NAME;g" \
    -e "s;%MESH_ENV%;$DEPLOY_ENV;g" \
	"$1" > "$2"
}

mesh_replace_taskdef() {
  
  local _logName=$1
  local _logStream=$2
  local _inputFileName=$3
  local _outputFileName=$4

  sed -e "s;%ROLE%;$ROLE;g" \
    -e "s;%SERVICE_NAME%;$SERVICE_NAME;g" \
    -e "s;%SERVICE_ENV%;$SERVICE_ENV;g" \
    -e "s;%LOG_NAME%;$_logName;g" \
    -e "s;%REGION%;$REGION;g" \
    -e "s;%LOG_STREAM%;$_logStream;g" \
    -e "s;%REPOSITORY_NAME%;$REPOSITORY_NAME;g" \
    -e "s;%IMAGE_ENV%;$IMAGE_ENV;g" \
    -e "s;%BUILD_NUMBER%;$BUILD_NUMBER;g" \
    -e "s;%NAME%;$CONTAINER_NAME;g" \
    -e "s;%FAMILY%;$FAMILY;g" \
    -e "s;%MESH_NAME%;$MeshName;g" \
    -e "s;%SERVICE_DISCOVERY%;$ServiceDiscovery;g" \
    -e "s;%MESH_TYPE%;$MESH_TYPE;g" \
    -e "s;%MEMORY%;$MEMORY;g" \
    -e "s;\"%MEMORY_INT%\";$MEMORY;g" \
    -e "s;%CPU%;$CPU;g" \
    "$_inputFileName" > "$_outputFileName"
}

mesh_deploy_appmesh() {
  mesh_replace_deploy "deploy/virtual-node.json" "virtual-node.json"

  local virtualNodeName
  virtualNodeName=$(aws appmesh describe-virtual-node --mesh-name "$MeshName" --virtual-node-name "$ServiceDiscovery"_vn --region "$REGION" | jq .virtualNode.virtualNodeName)
  if [[ -z $virtualNodeName ]]
  then
    echo "create virtual node"
    aws appmesh create-virtual-node --cli-input-json file://${WORKSPACE}/virtual-node.json --region "$REGION"
  else
    echo "update virtual node"
    aws appmesh update-virtual-node --cli-input-json file://${WORKSPACE}/virtual-node.json --region "$REGION"
  fi
  
  local virtualRouterName
  virtualRouterName=$(aws appmesh describe-virtual-router --mesh-name "$MeshName" --virtual-router-name "$TASK_NAME"_vr --region "$REGION" | jq .virtualRouter.virtualRouterName)
  if [[ -z $virtualRouterName ]]
  then
    echo "create virtual router"
    mesh_replace_deploy "deploy/virtual-router.json" "virtual-router.json"
    aws appmesh create-virtual-router --cli-input-json file://${WORKSPACE}/virtual-router.json --region "$REGION"
  fi

  local routeName
  routeName=$(aws appmesh describe-route --mesh-name "$MeshName" --virtual-router-name "$TASK_NAME"_vr --route-name "$TASK_NAME"_r --region "$REGION" | jq .route.routerName)
  if [[ -z $routeName ]]
  then
    echo "create route"
    mesh_replace_deploy "deploy/route.json" "route.json"
    aws appmesh create-route --cli-input-json file://${WORKSPACE}/route.json --region "$REGION"
  # else
  #   if [[ $MESH_USE_VERSION != 1 ]]
  #   then
  #     echo "update route"
  #     aws appmesh update-route --cli-input-json file://${WORKSPACE}/route.json --region "$REGION"
  #   fi
  fi

  local virtualServiceName
  virtualServiceName=$(aws appmesh describe-virtual-service --mesh-name "$MeshName" --virtual-service-name "$TASK_NAME"."$MeshDomain" --region "$REGION" | jq .virtualService.virtualServiceName)
  if [[ -z $virtualServiceName ]]
  then
    echo "create virtual service"
    mesh_replace_deploy "deploy/virtual-service.json" "virtual-service.json" 
    aws appmesh create-virtual-service --cli-input-json file://${WORKSPACE}/virtual-service.json --region "$REGION"
  fi

  local gatewayRouteName
  gatewayRouteName=$(aws appmesh describe-gateway-route --mesh-name "$MeshName" --gateway-route-name "$TASK_NAME" --virtual-gateway-name "$MESH_TYPE"_gw --region "$REGION" | jq .gatewayRoute.gatewayRouteName)
  if [[ -z $gatewayRouteName ]]
  then
    echo "create gateway route"
    mesh_replace_deploy "deploy/gateway-route.json" "gateway-route.json" 
    aws appmesh create-gateway-route --cli-input-json file://${WORKSPACE}/gateway-route.json --region "$REGION"
  fi
}

mesh_get_service_discovery_arn()
{
  echo "get service discovery namespace Id"
  local serviceDiscoveryId
  serviceDiscoveryId=$(aws servicediscovery list-namespaces --region "$REGION" | jq -r --arg domain "$MeshDomain" '.Namespaces[] | select(.Name==$domain) | .Id')
  echo "DISCOVERY_ID $serviceDiscoveryId"

  local serviceDiscoveryArn
  serviceDiscoveryArn=$(aws servicediscovery list-services --region "$REGION" --filters Name=NAMESPACE_ID,Values="$serviceDiscoveryId",Condition=EQ | jq -r --arg SERVICE "$ServiceDiscovery" '.Services[] | select(.Name==$SERVICE) | .Arn') 

  if [[ -z $serviceDiscoveryArn ]]
  then
    echo "Create Service Discovery servicename"
    serviceDiscoveryArn=$(aws servicediscovery create-service --name "$ServiceDiscovery" \
      --dns-config "NamespaceId=$serviceDiscoveryId,RoutingPolicy=WEIGHTED,DnsRecords=[{Type=A,TTL=60}]" \
      --health-check-custom-config 'FailureThreshold=1' \
      --region "$REGION" | jq -r '.Service.Arn')
  fi

  RETURN_VALUE=$serviceDiscoveryArn
}

mesh_create_service() {
  local revision=$1
  echo "create mesh service revision $revision"

  mesh_deploy_appmesh
  
  common_get_subnet "$DEPLOY_ENV" "$SERVICE_ENV" "$REGION"
  local subnets=$RETURN_VALUE

  common_get_security_group "$DEPLOY_ENV" "$SERVICE_ENV" "$REGION"
  local securityGroup=$RETURN_VALUE

  mesh_get_service_discovery_arn
  local serviceDiscoveryArn=$RETURN_VALUE

  aws ecs create-service \
    --cluster "$CLUSTER" \
    --service-name "$DEPLOY_SERVICE_NAME" \
    --desired-count "$SERVICE_COUNT" \
    --task-definition "$FAMILY:$revision" \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[$subnets],securityGroups=[$securityGroup],assignPublicIp=DISABLED}" \
    --service-registries registryArn="${serviceDiscoveryArn}" \
    --platform-version "$PLATFORM_VERSION" \
    --enable-ecs-managed-tags \
    --propagate-tags SERVICE \
    --tags "$TAGS" \
    --region "$REGION"
}

mesh_update_service()
{
  local revision=$1
  echo "update mesh service revision $revision"

  mesh_replace_deploy "deploy/virtual-node.json" "virtual-node.json"
  aws appmesh update-virtual-node --cli-input-json file://${WORKSPACE}/virtual-node.json --region "$REGION"
  
  aws ecs update-service \
    --cluster "$CLUSTER" \
    --region "$REGION" \
    --service "$DEPLOY_SERVICE_NAME" \
    --task-definition "$FAMILY:$revision" \
    --platform-version "$PLATFORM_VERSION" \
    --force-new-deployment
}

create_auto_scaling()
{
    echo "create autoscaling"
  aws application-autoscaling register-scalable-target \
    --service-namespace ecs \
    --scalable-dimension ecs:service:DesiredCount \
    --resource-id service/"$CLUSTER/$DEPLOY_SERVICE_NAME" \
    --min-capacity $SERVICE_COUNT \
    --max-capacity $SERVICE_MAX_COUNT \
    --region $REGION

  echo "put-scaling-policy tracking-cpu-scaling-policy"
  aws application-autoscaling put-scaling-policy \
    --region $REGION \
    --service-namespace ecs \
    --scalable-dimension ecs:service:DesiredCount \
    --resource-id service/"$CLUSTER/$DEPLOY_SERVICE_NAME" \
    --policy-name tracking-cpu-scaling-policy \
    --policy-type TargetTrackingScaling \
    --target-tracking-scaling-policy-configuration '{ "TargetValue": 80.0, "PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization" }, "ScaleOutCooldown": 60,"ScaleInCooldown": 60}'

  echo "put-scaling-policy tracking-memory-scaling-policy"
  aws application-autoscaling put-scaling-policy \
    --region $REGION \
    --service-namespace ecs \
    --scalable-dimension ecs:service:DesiredCount \
    --resource-id service/"$CLUSTER/$DEPLOY_SERVICE_NAME" \
    --policy-name tracking-memory-scaling-policy \
    --policy-type TargetTrackingScaling \
    --target-tracking-scaling-policy-configuration '{ "TargetValue": 80.0, "PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization" }, "ScaleOutCooldown": 60,"ScaleInCooldown": 60}'
}

echo mesh_create

if [[ -z $MESH_TYPE ]]; then echo Mesh Type is not define; exit 1; fi

readonly MeshDomain=${MESH_TYPE}.${SERVICE_ENV}
readonly MeshName="$MESH_TYPE-$SERVICE_ENV"

declare ServiceDiscovery=$TASK_NAME

if [[ $MESH_USE_VERSION == 1 ]]
then
  ServiceDiscovery=${ServiceDiscovery}-${BUILD_NUMBER}
  DEPLOY_SERVICE_NAME=$DEPLOY_SERVICE_NAME-${BUILD_NUMBER}
fi

common_init "$DEPLOY_ENV" "$SERVICE_ENV" "$REGION"
common_create_repository "$SERVICE_NAME"

declare -u logName="${DEPLOY_ENV}-${MESH_TYPE}-MESH"
readonly logStream=$TASK_NAME

common_create_log "$logName"

common_create_taskdef

mesh_replace_taskdef "$logName" "$logStream" "deploy/taskdef-${SERVICE_ENV}.json" "taskdef-v_latest.json" 

#Register the task definition in the repository
aws ecs register-task-definition --family "$FAMILY" --cli-input-json file://${WORKSPACE}/taskdef-v_latest.json --region "$REGION"
taskDefRevision=$(aws ecs describe-task-definition --task-definition "$FAMILY" --region "$REGION" | jq .taskDefinition.revision)

runningCount=$(aws ecs describe-services --services "$DEPLOY_SERVICE_NAME" --cluster "$CLUSTER" --region "$REGION" | jq .services[].runningCount)
echo "running count $runningCount"

if [[ -z $runningCount || $runningCount = '0' ]] 
then
  mesh_create_service "$taskDefRevision"
  if [[ $ENV == "zgp" ]]; then create_auto_scaling ; fi
else
  mesh_update_service "$taskDefRevision"
fi
