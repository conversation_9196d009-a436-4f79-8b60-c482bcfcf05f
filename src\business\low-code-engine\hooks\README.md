# 低代码平台 Hooks 使用指南

## 分页相关 Hooks

### 基础用法

```javascript
// editor.vue
import { useTablePagination } from '@/business/low-code-engine/hooks/usePagination'

// 使用标准分页组件
const pages = generatePagesFromBackendMeta(meta, useTablePagination())
```

### 可用的分页 Hooks

#### 1. `useTablePagination()` - 标准分页（推荐）
```javascript
import { useTablePagination } from '@/business/low-code-engine/hooks/usePagination'

const config = useTablePagination()
// 返回: { paginationComponent: 'TablePagination' }
```

#### 2. `useTablePaginationLowCode()` - 低代码专用分页
```javascript
import { useTablePaginationLowCode } from '@/business/low-code-engine/hooks/usePagination'

const config = useTablePaginationLowCode()
// 返回: { paginationComponent: 'TablePaginationLowCode' }
```

#### 3. `useCustomDataBinding(dataBinding)` - 自定义数据绑定
```javascript
import { useCustomDataBinding } from '@/business/low-code-engine/hooks/usePagination'

const config = useCustomDataBinding({
  totalItems: 'totalUsers'  // 自定义总数字段名
})
```

#### 4. `useCustomPaginationProps(extraProps)` - 自定义样式Props
```javascript
import { useCustomPaginationProps } from '@/business/low-code-engine/hooks/usePagination'

const config = useCustomPaginationProps({
  color: 'success',
  totalVisible: 10
})
```

#### 5. `usePaginationConfig(options)` - 通用配置Hook
```javascript
import { usePaginationConfig, PAGINATION_COMPONENTS } from '@/business/low-code-engine/hooks/usePagination'

const config = usePaginationConfig({
  component: PAGINATION_COMPONENTS.TABLE_PAGINATION,
  dataBinding: { totalItems: 'totalUsers' },
  extraProps: { color: 'primary' }
})
```

#### 6. `useRecommendedPagination()` - 推荐配置
```javascript
import { useRecommendedPagination } from '@/business/low-code-engine/hooks/usePagination'

const config = useRecommendedPagination()
// 等同于 useTablePagination()
```

### 预设配置

```javascript
import { PAGINATION_PRESETS } from '@/business/low-code-engine/hooks/usePagination'

// 直接使用预设
const pages = generatePagesFromBackendMeta(meta, PAGINATION_PRESETS.standard)
const pages = generatePagesFromBackendMeta(meta, PAGINATION_PRESETS.lowCode)
const pages = generatePagesFromBackendMeta(meta, PAGINATION_PRESETS.userList)
const pages = generatePagesFromBackendMeta(meta, PAGINATION_PRESETS.styled)
```

### 在不同场景中使用

#### 用户管理页面
```javascript
import { useCustomDataBinding } from '@/business/low-code-engine/hooks/usePagination'

const userPaginationConfig = useCustomDataBinding({
  totalItems: 'totalUsers'
})
const pages = generatePagesFromBackendMeta(meta, userPaginationConfig)
```

#### 带样式的分页
```javascript
import { useCustomPaginationProps } from '@/business/low-code-engine/hooks/usePagination'

const styledPaginationConfig = useCustomPaginationProps({
  color: 'success',
  showFirstLastPage: true,
  totalVisible: 10
})
const pages = generatePagesFromBackendMeta(meta, styledPaginationConfig)
```

### 为什么使用 Hooks？

✅ **符合Vue 3最佳实践** - Composition API的标准用法  
✅ **更好的代码组织** - 逻辑封装和复用  
✅ **类型安全** - 更好的TypeScript支持  
✅ **测试友好** - 独立的可测试单元  
✅ **可维护性** - 集中化的配置管理 