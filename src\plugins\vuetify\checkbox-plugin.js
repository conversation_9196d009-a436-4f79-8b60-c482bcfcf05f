export default {
  install(app) {
    // 创建一个全局组件实例注册表
    const componentRegistry = new Map();
    
    // 提供一个全局方法来注册组件实例
    app.config.globalProperties.$registerCheckbox = function(id, instance) {
      componentRegistry.set(id, instance);
    };
    
    // 提供一个全局方法来获取组件实例
    app.config.globalProperties.$getCheckbox = function(id) {
      return componentRegistry.get(id);
    };

    // 等待Vue和Vuetify完全初始化
    app.mixin({
      mounted() {
        // 只在根组件上执行一次
        if (this.$root === this) {
          // 延迟执行以确保Vuetify组件已注册
          requestAnimationFrame(() => {
            // 初始化处理函数
            setupCheckboxHandlers();

            // 监听DOM变化
            const observer = new MutationObserver((mutations) => {
              // 检查是否有新的复选框添加到DOM中或者复选框状态变化
              let needsSetup = false;
              mutations.forEach((mutation) => {
                if (
                  mutation.type === "childList" &&
                  mutation.addedNodes.length
                ) {
                  needsSetup = true;
                }
                
                // 检查属性变化，特别是checkbox相关属性的变化
                if (mutation.type === "attributes" && 
                    (mutation.attributeName === "aria-checked" ||
                     mutation.attributeName === "checked" ||
                     mutation.attributeName === "data-indeterminate" ||
                     mutation.attributeName === "class") &&
                    (mutation.target.closest('.v-data-table') ||
                     mutation.target.classList.contains('v-data-table-rows'))) {
                  needsSetup = true;
                }
              });

              if (needsSetup) {
                setupCheckboxHandlers();
              }
            });

            observer.observe(document.body, {
              childList: true,
              subtree: true,
              attributes: true, 
              attributeFilter: ["aria-checked", "checked", "data-indeterminate", "class"] 
            });

            // 监听表格行复选框的各种事件
            const events = ['change', 'click', 'input'];
            events.forEach(eventType => {
              document.body.addEventListener(eventType, (e) => {
                const target = e.target;
                if ((target.type === 'checkbox' || 
                    target.classList && target.classList.contains('v-checkbox-btn')) && 
                    target.closest('.v-data-table')) {
                  // 当表格相关的复选框状态改变时，更新表头复选框状态
                  setupCheckboxHandlers();
                }
              }, true);
            });

            // 定期检查表格状态（以防有些变化未被捕获）
            setInterval(() => {
              const tables = document.querySelectorAll('.v-data-table');
              if (tables.length > 0) {
                setupCheckboxHandlers();
              }
            }, 2000); // 每2秒检查一次
          });
        }
      },
      // 监听数据变化的组件更新事件
      updated() {
        // 检查是否与表格相关的组件
        if (this.$el && (
            this.$el.classList && this.$el.classList.contains('v-data-table') ||
            this.$el.querySelector && this.$el.querySelector('.v-data-table')
        )) {
          setupCheckboxHandlers();
        }
      }
    });

    // 保存已处理过的input元素
    const handledInputs = new WeakSet();

    function setupCheckboxHandlers() {
      // 尝试获取Vue组件实例函数
      const tryGetVueInstance = (element) => {
        // 尝试多种可能的Vue实例引用属性
        if (element.__vnode) return element.__vnode;
        if (element.__vue__) return element.__vue__;
        if (element.__vue) return element.__vue;
        if (element._vnode) return element._vnode;
        
        // 也可以尝试查找父元素上的Vue实例
        const parent = element.parentElement;
        if (parent && parent.closest('.v-checkbox')) {
          return tryGetVueInstance(parent.closest('.v-checkbox'));
        }
        
        return null;
      };

      // 查找所有表头复选框
      const headerCheckboxes = document.querySelectorAll(
        ".v-data-table__thead .v-checkbox-btn"
      );

      // 记录处理过的表格
      const processedTables = new Set();

      headerCheckboxes.forEach((checkbox) => {
        // 查找该表头所属的表格
        const table = checkbox.closest(".v-data-table");
        if (!table || processedTables.has(table)) return;
        
        // 标记该表格已处理
        processedTables.add(table);
        
        // 查找该表格内的所有行复选框
        const rowCheckboxes = table.querySelectorAll(
          'tbody .v-checkbox-btn input[type="checkbox"]'
        );
        
        // 计算当前的选择状态
        const checkedCount = Array.from(rowCheckboxes).filter(cb => cb.checked).length;
        const allChecked = checkedCount === rowCheckboxes.length && rowCheckboxes.length > 0;
        const indeterminate = checkedCount > 0 && !allChecked;

        // 获取input元素
        const input = checkbox.querySelector('input[type="checkbox"]');
        if (!input) return;
        
        // 处理复选框，无论是否处理过
        if (!handledInputs.has(input)) {
          handledInputs.add(input);

          // 尝试获取Vue组件实例
          const vueInstance = tryGetVueInstance(checkbox);
          if (vueInstance) {
            console.log("找到Vue组件实例:", vueInstance);
          }

          const descriptor = Object.getOwnPropertyDescriptor(
            HTMLInputElement.prototype,
            "indeterminate"
          );
          const originalSet = descriptor.set;
          const originalGet = descriptor.get;

          Object.defineProperty(input, "indeterminate", {
            set(value) {
              // 调用原始setter
              originalSet.call(this, value);

              // 设置data-indeterminate属性
              if (value) {
                checkbox.setAttribute("data-indeterminate", "true");
                console.log("设置indeterminate属性:", checkbox);
                
                // 如果有Vue实例，尝试设置组件属性
                const vueInstance = tryGetVueInstance(checkbox);
                if (vueInstance) {
                  console.log("通过Vue实例设置indeterminate", vueInstance);
                }
              } else {
                checkbox.removeAttribute("data-indeterminate");
              }
            },
            get() {
              return originalGet.call(this);
            },
          });
        }

        // 保存原始点击处理函数（如果尚未保存）
        if (!checkbox._originalClick) {
          checkbox._originalClick = checkbox.onclick;
          
          // 替换点击处理函数
          checkbox.onclick = function (e) {
            // 检查是否是半选状态
            if (
              input.indeterminate ||
              checkbox.hasAttribute("data-indeterminate")
            ) {
              e.preventDefault();
              e.stopPropagation();
              console.log("拦截到半选状态复选框点击，取消所有选择");

              // 尝试通过Vue组件实例清除选择
              const vueInstance = tryGetVueInstance(checkbox);
              if (vueInstance) {
                console.log("尝试通过Vue实例清除选择");
                // 这里可以尝试调用组件方法或设置属性
              }

              const clearBtn = document.querySelector(
                "[data-testid='clear-btn']"
              );

              if (clearBtn) {
                clearBtn.click();
              } else {
                // 查找所有行复选框
                const rowCheckboxes = table.querySelectorAll(
                  'tbody .v-checkbox-btn input[type="checkbox"]'
                );

                // 取消所有选择
                rowCheckboxes.forEach((cb) => {
                  if (cb.checked) {
                    cb.checked = false;
                    cb.dispatchEvent(new Event("change", { bubbles: true }));
                    cb.dispatchEvent(new Event("input", { bubbles: true }));
                    
                    // 查找复选框所在的Vue组件并尝试更新
                    const checkboxCell = cb.closest('.v-checkbox-btn');
                    if (checkboxCell) {
                      const cellVueInstance = tryGetVueInstance(checkboxCell);
                      if (cellVueInstance) {
                        console.log("通过Vue实例更新行复选框状态");
                      }
                    }
                  }
                });

                // 更新表头复选框状态
                input.checked = false;
                input.indeterminate = false;
                // 触发事件
                input.dispatchEvent(new Event("change", { bubbles: true }));
                input.dispatchEvent(new Event("input", { bubbles: true }));
                // 清除自身data-indeterminate属性
                checkbox.removeAttribute("data-indeterminate");

                return false;
              }
            }

            // 如果不是半选状态，调用原始处理函数
            if (checkbox._originalClick) {
              return checkbox._originalClick.call(this, e);
            }
          };
        }

        // 更新复选框状态
        if (indeterminate) {
          input.indeterminate = true;
          checkbox.setAttribute("data-indeterminate", "true");
          
          // 尝试通过Vue实例设置状态
          const vueInstance = tryGetVueInstance(checkbox);
          if (vueInstance) {
            console.log("通过Vue实例设置半选状态");
          }
        } else {
          input.indeterminate = false;
          checkbox.removeAttribute("data-indeterminate");
          input.checked = allChecked;
        }
      });

      // 查找所有自定义复选框
      const customCheckboxes = document.querySelectorAll('.v-checkbox-with-custom-behavior');
      customCheckboxes.forEach((checkbox) => {
        // 处理自定义复选框
        // 这里可以根据需要添加自定义逻辑
      });
    }
  },
};
