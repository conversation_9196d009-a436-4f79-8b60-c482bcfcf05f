<script setup>
import { computed } from 'vue'
import TablePagination from './TablePagination.vue'
import { useLowCodeData } from '@/hooks/useLowCodeData'

const props = defineProps({
  page: {
    type: Number,
    required: true,
  },
  itemsPerPage: {
    type: Number,
    required: true,
  },
  totalItems: {
    type: Number,
    required: true,
  },
  // 低代码特有的数据映射配置
  dataMapping: {
    type: Object,
    default: () => ({
      page: 'page',
      itemsPerPage: 'itemsPerPage', 
      totalItems: 'totalUsers' // 注意这里映射到 totalUsers
    })
  }
})

const emit = defineEmits(['update:page'])

// 使用低代码数据绑定 hook
const { useReactiveDataBatch, isLowCodeContext } = useLowCodeData()

// 创建响应式数据绑定
const reactiveData = useReactiveDataBatch({
  [props.dataMapping.page]: props.page,
  [props.dataMapping.itemsPerPage]: props.itemsPerPage,
  [props.dataMapping.totalItems]: props.totalItems
})

// 计算最终的 props 值
const finalPage = computed(() => reactiveData[props.dataMapping.page].value)
const finalItemsPerPage = computed(() => reactiveData[props.dataMapping.itemsPerPage].value)
const finalTotalItems = computed(() => reactiveData[props.dataMapping.totalItems].value)

const handlePageUpdate = (newPage) => {
  console.log(`[TablePaginationLowCode] Page update: ${newPage}, isLowCodeContext: ${isLowCodeContext}`)
  emit('update:page', newPage)
}
</script>

<template>
  <TablePagination
    :page="finalPage"
    :items-per-page="finalItemsPerPage"
    :total-items="finalTotalItems"
    @update:page="handlePageUpdate"
  >
    <template #default>
      <slot />
    </template>
  </TablePagination>
</template> 