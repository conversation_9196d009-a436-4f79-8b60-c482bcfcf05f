{"id": "user-list-page", "module": "User Management", "name": "Users", "path": "/user/list", "layout": "default", "lifecycle": {"onMounted": [{"action": "initData"}, {"action": "handleSearch"}]}, "data": {"searchQuery": {"email": "", "roleIdList": [], "userGroupIdList": [], "createdOn": "", "lastModified": "", "status": null, "page": 1, "size": 10, "sortBy": null, "orderBy": null}, "isLoading": false, "usersData": null, "itemsPerPage": 10, "page": 1, "selectedRows": [], "selectedIds": [], "expanded": [], "accounts": [], "totalUsers": 0, "hasShowEnable": false, "hasShowDisable": false, "status": [{"title": "Enable", "value": 1}, {"title": "Disable", "value": 0}], "userGroupList": [], "roleList": [], "paginationOptions": [10, 20, 50, 100], "forceUpdateKey": 0, "headers": [{"title": "", "key": "data-table-select", "sortable": false, "fixed": true, "width": 50}, {"title": "No", "key": "no", "sortable": false, "width": 70}, {"title": "Email", "key": "email"}, {"title": "Name", "key": "name"}, {"title": "User Groups & Roles", "key": "groups", "sortable": false}, {"title": "Created On", "key": "createdDateTime"}, {"title": "Last Modified", "key": "updatedDateTime"}, {"title": "Status", "key": "status", "sortable": false}, {"title": "Actions", "key": "actions", "sortable": false, "fixed": true, "width": 120}]}, "components": [{"type": "<PERSON>ard", "props": {"class": "mb-6"}, "children": [{"type": "VCardItem", "props": {"class": "px-0 pb-4"}, "children": [{"type": "VCardTitle", "props": {"class": "px-0"}, "children": [{"type": "div", "props": {"class": "d-flex align-center flex-wrap"}, "children": [{"type": "div", "props": {"text": "Users"}}, {"type": "VSpacer"}, {"type": "div", "props": {"class": "app-user-search-filter d-flex align-center gap-4"}, "children": [{"type": "VBtn", "props": {"text": "CREATE"}, "events": {"click": [{"action": "handleAddUser"}]}, "permission": ["Add", "UserList"]}]}]}]}]}, {"type": "VCardText", "props": {"class": "px-0"}, "children": [{"type": "VRow", "children": [{"type": "VCol", "props": {"cols": 12, "sm": 4, "xl": 3, "xxl": 2}, "children": [{"type": "AppTextField", "model": "{{searchQuery.email}}", "props": {"placeholder": "Search Email", "clearable": true}, "events": {"update:modelValue": [{"action": "handleSearch"}]}}]}, {"type": "VCol", "props": {"cols": 12, "sm": 4, "xl": 3, "xxl": 2}, "children": [{"type": "AppAutocomplete", "model": "{{searchQuery.userGroupIdList}}", "props": {"placeholder": "Select User Group", "items": "{{userGroupList}}", "clearable": true, "clearIcon": "tabler-x", "multiple": true, "filterable": true}, "events": {"update:modelValue": [{"action": "handleSearch"}]}}]}, {"type": "VCol", "props": {"cols": 12, "sm": 4, "xl": 3, "xxl": 2}, "children": [{"type": "AppAutocomplete", "model": "{{searchQuery.roleIdList}}", "props": {"placeholder": "Select Role", "items": "{{roleList}}", "clearable": true, "clearIcon": "tabler-x", "multiple": true, "filterable": true}, "events": {"update:modelValue": [{"action": "handleSearch"}]}}]}, {"type": "VCol", "props": {"cols": 12, "sm": 4, "xl": 3, "xxl": 2}, "children": [{"type": "AppDateTimePicker", "model": "{{searchQuery.createdOn}}", "props": {"config": {"mode": "range", "enableTime": false, "dateFormat": "Y-m-d"}, "placeholder": "Select Created On", "clearable": true}, "events": {"update:modelValue": [{"action": "handleSearch"}]}}]}, {"type": "VCol", "props": {"cols": 12, "sm": 4, "xl": 3, "xxl": 2}, "children": [{"type": "AppDateTimePicker", "model": "{{searchQuery.lastModified}}", "props": {"config": {"mode": "range", "enableTime": false, "dateFormat": "Y-m-d"}, "placeholder": "Select Last Modified", "clearable": true}, "events": {"update:modelValue": [{"action": "handleSearch"}]}}]}, {"type": "VCol", "props": {"cols": 12, "sm": 4, "xl": 3, "xxl": 2}, "children": [{"type": "AppSelect", "model": "{{searchQuery.status}}", "props": {"placeholder": "Select Status", "items": "{{status}}", "clearable": true, "clearIcon": "tabler-x"}, "events": {"update:modelValue": [{"action": "handleSearch"}]}}]}]}]}, {"type": "VCardText", "props": {"class": "d-flex align-center gap-4 py-2 px-4 bg-grey-lighten-4 justify-start"}, "v-if": "{{selectedRows.length > 0}}", "children": [{"type": "span", "props": {"class": "text-primary font-weight-medium", "text": "{{selectedRows.length}}"}}, {"type": "span", "props": {"text": "items selected"}}, {"type": "VIcon", "props": {"size": 20, "icon": "mdi-close-circle"}, "events": {"click": [{"action": "clearAllSelected"}]}}, {"type": "VDivider", "props": {"vertical": true, "color": "#999", "class": "my-auto"}, "permission": ["Edit", "UserList"]}, {"type": "VBtn", "props": {"variant": "tonal", "color": "default", "text": "DISABLE", "dataTestId": "disable"}, "events": {"click": [{"action": "batchDisable"}]}, "permission": ["Edit", "UserList"], "v-if": "hasShowDisable"}, {"type": "VBtn", "props": {"variant": "tonal", "color": "primary", "text": "ENABLE", "dataTestId": "enable"}, "events": {"click": [{"action": "batchEnable"}]}, "permission": ["Edit", "UserList"], "v-if": "hasShowEnable"}, {"type": "VSpacer"}]}, {"type": "VDataTableServer", "model": "{{selectedIds}}", "props": {"items": "{{accounts}}", "itemValue": "accountId", "itemsLength": "{{totalUsers}}", "headers": "{{headers}}", "loading": "{{isLoading}}", "expanded": "{{expanded}}", "showSelect": true, "class": "text-no-wrap"}, "events": {"click:row": [{"action": "handleRowClick"}], "update:sort-by": [{"action": "handleSortChange", "params": ["options"]}], "update:modelValue": [{"action": "handleSelectChange"}]}, "slots": {"expanded-row": {"type": "tr", "props": {"class": "expanded-row"}, "children": [{"type": "td", "props": {"colspan": 4}}, {"type": "td", "props": {"colspan": 1}, "children": [{"type": "div", "props": {"class": "expanded-content pa-4"}, "children": [{"type": "div", "props": {"text": "Static Test: Expanded content is displayed"}}, {"type": "div", "props": {"text": "Dynamic Test - Email: {{item.accountEmail}}"}}, {"type": "div", "props": {"text": "Dynamic Test - Username: {{item.accountName}}"}}, {"type": "div", "props": {"text": "Dynamic Test - ID: {{item.accountId}}"}}]}]}, {"type": "td", "props": {"colspan": 4}}]}, "item.no": {"type": "span", "props": {"text": "{{item.no}}"}}, "item.email": {"type": "AppText", "props": {"text": "{{item.accountEmail}}", "auth": ["View", "UserList"]}, "events": {"click": [{"action": "viewUser", "params": ["item"]}]}}, "item.name": {"type": "span", "props": {"text": "{{item.accountName}}"}}, "item.groups": {"type": "div", "props": {"class": "d-flex align-center"}, "children": [{"type": "span", "props": {"text": "{{resolveUserGroupAndRoles(item.userGroupList)}}"}}, {"type": "VIcon", "v-if": "canExpand(item)", "props": {"icon": "{{isExpanded(item) ? 'tabler-chevron-up' : 'tabler-chevron-down'}}", "size": 20, "class": "ms-2", "color": "grey-400"}}]}, "item.status": {"type": "VSwitch", "props": {"modelValue": "{{item.accountStatusId}}", "falseValue": 0, "trueValue": 1, "class": "{{hasPermission('Edit', 'UserList') ? 'cursor-pointer' : 'opacity-50'}}"}, "events": {"click": [{"action": "toggleStatusId", "params": ["item", "$event"]}]}}, "item.createdDateTime": {"type": "span", "props": {"text": "{{dateUtil.format(item.createdDateTime)}}"}}, "item.updatedDateTime": {"type": "span", "props": {"text": "{{dateUtil.format(item.updatedDateTime)}}"}}, "item.actions": {"type": "IconBtn", "props": {"v-tooltip": "'Edit User'"}, "events": {"click": [{"action": "editUser", "params": ["item"]}]}, "permission": ["Edit", "UserList"], "children": [{"type": "VIcon", "props": {"icon": "mdi-pencil", "class": "hover:text-primary"}}]}, "bottom": {"type": "TablePagination", "key": "{{page}}", "props": {"page": "{{page}}", "itemsPerPage": "{{itemsPerPage}}", "totalItems": "{{totalUsers}}"}, "events": {"update:page": [{"action": "handlePageChange"}]}, "children": [{"type": "div", "props": {"class": "d-flex gap-3"}, "children": [{"type": "AppSelect", "props": {"modelValue": "{{itemsPerPage}}", "items": "{{paginationOptions}}"}, "events": {"update:modelValue": [{"action": "handleItemsPerPageChange"}]}}]}]}}}]}], "apis": [{"id": "init-group-list", "type": "http", "method": "GET", "url": "/api/admin-api/v1/user-group/query-all"}, {"id": "init-role-list", "type": "http", "method": "GET", "url": "/api/admin-api/v1/role/query-all"}, {"id": "user-list", "type": "http", "method": "POST", "url": "/api/admin-api/v1/account/page"}, {"id": "user-batch-status", "type": "http", "method": "PUT", "url": "/api/admin-api/v1/account/batch/status", "urlParams": ["status"], "bodyParams": ["ids"]}], "methods": {"initData": {"type": "custom", "script": "console.log('initData', this); return Promise.all([this.apiManager.callApi('init-group-list'), this.apiManager.callApi('init-role-list')]).then(([groupRes, roleRes]) => { this.setData('userGroupList', Array.isArray(groupRes.data) ? groupRes.data.map(item => ({ ...item, title: item.name, value: item.id })) : []); this.setData('roleList', Array.isArray(roleRes.data) ? roleRes.data.map(item => ({ ...item, title: item.name, value: item.id })) : []); }).catch(err => { console.error('Failed to initialize data:', err); });"}, "handleAddUser": {"type": "navigate", "to": "/user/list/add"}, "handleSearch": {"type": "custom", "script": "this.setData('page', 1); this.data.searchQuery.page = 1; this.apiManager.callApi('user-list', this.getQueryParams(this.data.searchQuery)).then(res => { const accountsWithNo = (res.data.records || []).map((item, idx) => ({ ...item, no: (this.page - 1) * this.itemsPerPage + idx + 1 })); this.setData('accounts', accountsWithNo); this.setData('totalUsers', res.data.total || 0); }).catch(err => { console.error('API call failed:', err); });"}, "editUser": {"params": ["item"], "type": "navigate", "to": "/user/list/edit", "paramsMap": {"id": "{{item.accountId}}"}}, "viewUser": {"params": ["item"], "type": "navigate", "to": "/user/list/detail", "paramsMap": {"id": "{{item.accountId}}"}}, "batchEnable": {"type": "confirm", "title": "Confirm Enable Users", "text": "Are you sure you want to enable the selected user(s)? Once enabled, they will regain access to the system.", "confirmButtonText": "CONFIRM ENABLE", "confirmButtonColor": "primary", "cancelButtonText": "CANCEL", "onConfirm": [{"action": "callApi", "apiId": "user-batch-status", "params": {"status": 1, "ids": "{{selectedRows.map(item => ({id: item.accountId}))}}"}, "onSuccess": [{"action": "message", "messageType": "success", "text": "Users enabled successfully"}, {"action": "clearAllSelected"}, {"action": "handleSearch"}], "onError": [{"action": "message", "messageType": "error", "text": "Failed to enable users"}]}]}, "batchDisable": {"type": "confirm", "title": "Confirm Disable Users", "text": "Are you sure you want to disable the selected user(s)? Once disabled, they will lose access to the system.", "confirmButtonText": "CONFIRM DISABLE", "confirmButtonColor": "error", "cancelButtonText": "CANCEL", "onConfirm": [{"action": "callApi", "apiId": "user-batch-status", "params": {"status": 0, "ids": "{{selectedRows.map(item => ({id: item.accountId}))}}"}, "onSuccess": [{"action": "message", "messageType": "success", "text": "Users disabled successfully"}, {"action": "clearAllSelected"}, {"action": "handleSearch"}], "onError": [{"action": "message", "messageType": "error", "text": "Failed to disable users"}]}]}, "toggleStatusId": {"params": ["item"], "type": "confirm", "title": "{{item.accountStatusId === 1 ? 'Disable User?' : 'Enable User?'}}", "text": "{{item.accountStatusId === 1 ? 'Once disabled, user will lose access to the system.' : 'Once enabled, user will regain access to the system.'}}", "confirmButtonText": "{{item.accountStatusId === 1 ? 'DISABLE' : 'ENABLE'}}", "confirmButtonColor": "{{item.accountStatusId === 1 ? 'error' : 'primary'}}", "cancelButtonText": "CANCEL", "onConfirm": [{"action": "callApi", "apiId": "user-batch-status", "params": {"status": "{{item.accountStatusId === 1 ? 0 : 1}}", "ids": "{{[{id: item.accountId}]}}"}, "onSuccess": [{"action": "message", "messageType": "success", "text": "User status updated successfully"}, {"action": "handleSearch"}], "onError": [{"action": "message", "messageType": "error", "text": "Failed to update user status"}]}]}, "handleSortChange": {"type": "custom", "script": "const options = arguments[0]; this.data.searchQuery.sortBy = options[0]?.key; this.data.searchQuery.orderBy = options[0]?.order; this.setData('page', 1); this.data.searchQuery.page = 1; this.apiManager.callApi('user-list', this.getQueryParams(this.data.searchQuery)).then(res => { const accountsWithNo = (res.data.records || []).map((item, idx) => ({ ...item, no: (1 - 1) * this.itemsPerPage + idx + 1 })); this.setData('accounts', accountsWithNo); this.setData('totalUsers', res.data.total || 0); }).catch(err => { console.error('handleSortChange call failed:', err); });"}, "handleSelectChange": {"type": "custom", "script": "const selection = arguments[0]; const selectedItems = (this.data.accounts || []).filter(item => { const itemId = Number(item.accountId); return selection.some ? selection.some(selId => Number(selId) === itemId) : selection.includes(Number(item.accountId)); }); this.setData('selectedRows', selectedItems); this.setData('selectedIds', selection); this.updateButtonVisibility(); this.setData('forceUpdateKey', (this.data.forceUpdateKey || 0) + 1);"}, "handleRowClick": {"type": "custom", "script": "const event = arguments[0]; const dataObj = arguments[1]; const item = dataObj?.item; if (!item) { return; } if (!this.canExpand(item)) { return; } const currentExpanded = [...this.expanded]; const index = currentExpanded.indexOf(item.accountId); if (index === -1) { currentExpanded.push(item.accountId); } else { currentExpanded.splice(index, 1); } this.setData('expanded', currentExpanded);"}, "clearAllSelected": {"type": "custom", "script": "this.setData('selectedRows', []); this.setData('selectedIds', []); this.updateButtonVisibility(); this.setData('forceUpdateKey', (this.data.forceUpdateKey || 0) + 1);"}, "handlePageChange": {"type": "custom", "script": "const newPage = arguments[0]; this.setData('page', newPage); this.data.searchQuery.page = newPage; this.setData('selectedRows', []); this.setData('selectedIds', []); this.updateButtonVisibility(); this.apiManager.callApi('user-list', this.getQueryParams(this.data.searchQuery)).then(res => { const accountsWithNo = (res.data.records || []).map((item, idx) => ({ ...item, no: (newPage - 1) * this.itemsPerPage + idx + 1 })); this.setData('accounts', accountsWithNo); this.setData('totalUsers', res.data.total || 0); this.setData('forceUpdateKey', this.forceUpdateKey + 1); }).catch(err => { console.error('API call failed:', err); });"}, "handleItemsPerPageChange": {"type": "custom", "script": "const newItemsPerPage = arguments[0]; const newSize = parseInt(newItemsPerPage, 10); this.setData('itemsPerPage', newSize); this.data.searchQuery.size = newSize; this.setData('page', 1); this.data.searchQuery.page = 1; this.setData('selectedRows', []); this.setData('selectedIds', []); this.updateButtonVisibility(); this.apiManager.callApi('user-list', this.getQueryParams(this.data.searchQuery)).then(res => { const accountsWithNo = (res.data.records || []).map((item, idx) => ({ ...item, no: (1 - 1) * newSize + idx + 1 })); this.setData('accounts', accountsWithNo); this.setData('totalUsers', res.data.total || 0); }).catch(err => { console.error('handleItemsPerPageChange call failed:', err); });"}, "getActiveRoleList": {"params": ["roleList"], "type": "custom", "script": "return (roleList || []).filter(function(role) { return role.statusId === 1; });"}, "getUserGroups": {"params": ["userGroupList"], "type": "custom", "script": "return (userGroupList || []).filter(function(item) { var roleList = item.roleList; if (!roleList) return false; return roleList.some(function(role) { return role.statusId === 1; }); });"}, "getUserGroupRoles": {"params": ["userGroupList"], "type": "custom", "script": "if (!userGroupList) return []; var roles = []; var self = this; userGroupList.forEach(function(item) { if (item.roleList) { var activeRoleList = self.getActiveRoleList(item.roleList); roles = roles.concat(activeRoleList); } }); return roles;"}, "resolveUserGroupAndRoles": {"params": ["userGroupList"], "type": "custom", "script": "var groups = this.getUserGroups(userGroupList); var roles = this.getUserGroupRoles(userGroupList); if (groups.length === 0) { return ''; } if (groups.length === 1 && !roles.length) { return groups[0].name; } if (groups.length === 1 && roles.length === 1) { return groups[0].name + ' & ' + roles[0].name; } if (groups.length && !roles.length) { return groups.length + ' groups'; } if (groups.length && roles.length) { return groups.length + ' groups & ' + roles.length + ' roles'; }"}, "canExpand": {"params": ["item"], "type": "custom", "script": "const groups = this.getUserGroups(item.userGroupList); const roles = this.getUserGroupRoles(item.userGroupList); return groups.length > 1 || roles.length > 1;"}, "isExpanded": {"params": ["item"], "type": "custom", "script": "return this.expanded && this.expanded.includes(item.accountId);"}, "getQueryParams": {"type": "custom", "params": ["searchQuery"], "script": "const params = {}; params.page = searchQuery.page; params.size = searchQuery.size; params.emailLike = searchQuery.email; params.userGroupIdList = searchQuery.userGroupIdList; params.roleIdList = searchQuery.roleIdList; params.statusId = searchQuery.status; if (searchQuery.sortBy && searchQuery.orderBy) { params.orderItemList = [{ column: searchQuery.sortBy || '', asc: searchQuery.orderBy === 'asc' }]; } else { params.orderItemList = null; } if (searchQuery.createdOn) { const [start, end] = searchQuery.createdOn.split('to'); if (start) { params.gmtCreateGe = dateUtil.format(start, 'YYYY-MM-DDTHH:mm:ss.SSSZ'); params.gmtCreateLe = dateUtil.format(start, 'YYYY-MM-DDT23:59:59.SSSZ'); } if (end) { params.gmtCreateLe = dateUtil.format(end, 'YYYY-MM-DDT23:59:59.SSSZ'); } } if (searchQuery.lastModified) { const [start, end] = searchQuery.lastModified.split('to'); if (start) { params.gmtUpdateGe = dateUtil.format(start, 'YYYY-MM-DDTHH:mm:ss.SSSZ'); params.gmtUpdateLe = dateUtil.format(start, 'YYYY-MM-DDT23:59:59.SSSZ'); } if (end) { params.gmtUpdateLe = dateUtil.format(end, 'YYYY-MM-DDT23:59:59.SSSZ'); } } return params;"}, "updateButtonVisibility": {"type": "custom", "script": "const selectedRows = this.data.selectedRows || []; const hasShowEnable = selectedRows.some(item => item.accountStatusId === 0); const hasShowDisable = selectedRows.some(item => item.accountStatusId === 1); this.setData('hasShowEnable', hasShowEnable); this.setData('hasShowDisable', hasShowDisable);"}}}