/**
 * Switch字段测试
 * 验证Switch字段在搜索模式下生成下拉框配置
 */

import { mapFieldToComponent } from '../src/business/low-code-engine/meta-manager/componentMapping.js'

// 模拟Switch字段定义
const switchField = {
  name: 'status',
  displayName: '状态',
  layout: {
    component: 'SWITCH'
  },
  required: false
}

// 测试搜索模式下的配置
const searchConfig = mapFieldToComponent(switchField, 'search')

console.log('Switch字段搜索配置:', JSON.stringify(searchConfig, null, 2))

// 验证配置是否正确
const expected = {
  type: 'AppSelect',
  model: '{{searchQuery.status}}',
  props: {
    items: [
      { title: 'Enable', value: 1 },
      { title: 'Disable', value: 0 }
    ],
    clearable: true,
    placeholder: '搜索状态'
  },
  events: {
    'update:modelValue': [
      { action: 'handleSearch' }
    ]
  }
}

console.log('预期配置:', JSON.stringify(expected, null, 2))
console.log('配置是否包含items:', !!searchConfig.props.items)
console.log('items长度:', searchConfig.props.items?.length) 