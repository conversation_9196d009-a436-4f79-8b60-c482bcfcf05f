# 低代码平台分页组件配置指南

## 概述

低代码平台支持两种分页组件：
- `TablePagination` - 标准分页组件
- `TablePaginationLowCode` - 低代码专用分页组件

## 快速配置

### 1. 使用标准TablePagination组件（推荐）

```javascript
// editor.vue
import { useTablePagination } from '@/business/low-code-engine/config/pagination-examples'

// 生成页面时指定分页组件
const pages = generatePagesFromBackendMeta(meta, useTablePagination)
```

### 2. 使用TablePaginationLowCode组件

```javascript
// editor.vue  
import { useTablePaginationLowCode } from '@/business/low-code-engine/config/pagination-examples'

const pages = generatePagesFromBackendMeta(meta, useTablePaginationLowCode)
```

## 高级配置

### 自定义数据绑定

```javascript
const customPagination = {
  paginationComponent: PAGINATION_COMPONENTS.TABLE_PAGINATION,
  paginationConfig: {
    dataBinding: {
      page: 'currentPage',      // 自定义页码字段名
      itemsPerPage: 'pageSize', // 自定义每页数量字段名
      totalItems: 'totalUsers'  // 自定义总数字段名
    }
  }
}
```

### 自定义样式和Props

```javascript
const styledPagination = {
  paginationComponent: PAGINATION_COMPONENTS.TABLE_PAGINATION,
  paginationConfig: {
    extraProps: {
      color: 'primary',
      showFirstLastPage: true,
      totalVisible: 7
    }
  }
}
```

## 组件对比

| 特性 | TablePagination | TablePaginationLowCode |
|------|-----------------|------------------------|
| **性能** | ⚡ 更快 | 🔧 稍慢（包装层） |
| **兼容性** | ✅ 标准Vue组件 | 🎯 低代码专用 |
| **数据绑定** | 直接props | 响应式数据绑定 |
| **使用场景** | 一般页面 | 低代码生成页面 |
| **推荐指数** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 生成的JSON配置示例

### TablePagination配置
```json
{
  "type": "TablePagination",
  "props": {
    "page": "{{page}}",
    "itemsPerPage": "{{itemsPerPage}}",
    "totalItems": "{{totalCount}}"
  },
  "events": {
    "update:page": [{ "action": "handlePageChange" }]
  }
}
```

### TablePaginationLowCode配置
```json
{
  "type": "TablePaginationLowCode", 
  "props": {
    "page": "{{page}}",
    "itemsPerPage": "{{itemsPerPage}}",
    "totalItems": "{{totalCount}}"
  },
  "events": {
    "update:page": [{ "action": "handlePageChange" }]
  }
}
```

## 切换分页组件

只需修改editor.vue中的导入：

```javascript
// 使用标准分页
import { useTablePagination } from '@/business/low-code-engine/config/pagination-examples'

// 或使用低代码分页  
import { useTablePaginationLowCode } from '@/business/low-code-engine/config/pagination-examples'
```

## 故障排除

1. **分页不显示**：检查VDataTableServer的hideDefaultFooter是否设置为true
2. **分页事件无效**：确认handlePageChange方法已正确实现
3. **样式异常**：检查组件是否正确注册到RenderNode中

## 推荐用法

🎯 **推荐使用`TablePagination`组件**，因为：
- 性能更好
- 代码更简洁
- 维护成本更低
- 兼容性更好 