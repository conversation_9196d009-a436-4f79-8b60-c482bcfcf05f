<script setup>
import Form from "@/business/payment-partner-commission/form.vue";
import { ref, provide } from "vue";
import { useRouter } from "vue-router";

definePage({
  meta: {
    navActiveLink: 'payment-partner-commission-commission-scheme-managers',
    breadcrumb: 'Create New Commission Scheme',
  },
})

const activeIndex = ref([0]);
const form = ref({});
const formRef = ref(null);
const formMethods = ref({});
const router = useRouter();
provide("formMethods", formMethods.value);



const clearData = () => {
  useStorage('razerGoldPaymentChannelItems').remove()
  useStorage('razerGoldPinPaymentChannelItems').remove()
  useStorage('thirdPartyPaymentChannelItems').remove()

  useStorage('razerGoldSelectedIds').remove()
  useStorage('razerGoldPinSelectedIds').remove()
  useStorage('thirdPartySelectedIds').remove()

  useStorage('razerGoldItems').remove()
  useStorage('razerGoldPinItems').remove()
  useStorage('thirdPartyItems').remove()

  useStorage('razerGoldSearchQuery').remove()
  useStorage('razerGoldPinSearchQuery').remove()
  useStorage('thirdPartySearchQuery').remove()

  useStorage('razerGoldSelectedRows').remove()
  useStorage('razerGoldPinSelectedRows').remove()
  useStorage('thirdPartySelectedRows').remove()

}

const handleCancel = () => {
  clearData()
  router.back();
};

const handleSubmit = () => {
  formRef.value.validate().then(({ valid, errors }) => {
    if (valid) {
      confirmDialog({
        title: 'Submit Merchant Commission Scheme?',
        text: 'Once submitted, you cannot edit until the approval process is done and this cannot be undone.',
        cancelButtonText: 'CANCEL',
        confirmButtonText: 'SUBMIT',
        confirmButtonColor: 'primary',
        confirmButtonTextColor: '#000000',
        async onConfirm(close) {
          const params = formRef.value.getParams()
          console.log("params", params);
          await $api("/api/admin-api/v1/request", {
            method: "POST",
            body: params
          })
          message.success("Request submitted successfully")
          close()
          clearData()
          router.push({
            path: "/payment-partner-commission/commission-scheme-managers"
          })
        }
      })
    } else {
      console.log("errors", errors);
    }
  })
};

onMounted(() => {
  clearData()
});
</script>

<template>
  <VCard>
    <VCardTitle class="px-0 text-white h-38px!"
      >Create New Commission Scheme</VCardTitle
    >
    <VCardText class="p-0! mt-4">
      <Form v-model:activeIndex="activeIndex" v-model:form="form" ref="formRef"/>
      <div class="flex justify-end">
        <VBtn variant="outlined" color="default" class="mr-4" @click="handleCancel">CANCEL</VBtn>
        <VBtn class="w-120px" @click="handleSubmit">SUBMIT</VBtn>
      </div>
    </VCardText>
  </VCard>
</template>

<style scoped></style>
