#!/usr/bin/env bash

declare -x SERVICE_NAME=voucher-console
declare -xr BUILD=FARGATE

declare -x ROUTE_RECORD=voucherconsole
declare -x LOADBALANCER_WAN
declare -xr USE_ROUTE_53=0
declare -x MEMORY
declare -x CPU
declare -x SLOW_START

deploy_override()
{
    MEMORY=1024
    CPU=512
    SLOW_START=1

    case $ENV in
        zgq)
        LOADBALANCER_WAN=ZGQ-ALB-WAN-OFFICE
        ;;
        zgp)
        LOADBALANCER_WAN=ZGP-ALB-WAN-OFFICE
        ;;
    esac
}


