export const useSubTaskStatus = () => {
    const subTaskStatusList = ref([])

    const loadData = async () => {
        const res = await $api('api/pin-generator/v1/batch-task/sub-task/status', {
            method: 'GET',
        })
        subTaskStatusList.value = Array.isArray(res.data)
            ? res.data.map(item => ({
                ...item,
                title: item,
                value: item
            })) : [];
    }

    onMounted(async () => {
        await loadData()
    })

    return { subTaskStatusList }
}