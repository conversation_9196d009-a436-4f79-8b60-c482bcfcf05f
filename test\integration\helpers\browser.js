/**
 * Wait for element to load
 * @param {string} selector - CSS selector
 * @param {number} timeout - Timeout in milliseconds
 */
export async function waitForSelector(selector, timeout = 10000) {
  await page.waitForSelector(selector, { timeout })
}

/**
 * Wait for page load completion
 */
export async function waitForPageLoad() {
  await page.waitForNavigation({ waitUntil: 'networkidle0' })
}

/**
 * Get element text content
 * @param {string} selector - CSS selector
 */
export async function getElementText(selector) {
  return page.$eval(selector, (el) => el.textContent)
}

/**
 * Click element
 * @param {string} selector - CSS selector
 */
export async function clickElement(selector) {
  await waitForSelector(selector)
  await page.click(selector)
}

/**
 * Fill form input
 * @param {string} selector - CSS selector
 * @param {string} value - Value to input
 */
export async function fillForm(selector, value) {
  await waitForSelector(selector)
  await page.type(selector, value)
}

/**
 * Delay execution
 * @param {number} ms - Delay in milliseconds
 */
export async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
} 