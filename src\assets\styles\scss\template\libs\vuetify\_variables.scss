@use "sass:math";

$font-family-custom: "Public Sans",sans-serif,-apple-system,blinkmacsystemfont,
  "Segoe UI",roboto,"Helvetica Neue",arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";

/* 👉 Typography custom variables */
$typography-h5-font-size: 1.125rem;
$typography-body-1-font-size: 0.9375rem;
$typography-body-1-line-height: 1.375rem;

@forward "../../../base/libs/vuetify/variables"  with (
  $body-font-family: $font-family-custom !default,
  $border-radius-root: 6px !default,

  // 👉 Rounded
  $rounded: (
    "sm": 4px,
    "lg": 8px,
    "shaped": 30px 0,
  ) !default,

  // 👉 Shadows
  $shadow-key-umbra: (
    0: (0 0 0 0 rgba(var(--v-shadow-key-umbra-color), 1)),
    1: (0 2px 4px rgba(var(--v-shadow-key-umbra-color), 0.12)),
    2: (0 1px 6px rgba(var(--v-shadow-key-umbra-color), var(--v-shadow-xs-opacity))),
    3: (0 3px 8px rgba(var(--v-shadow-key-umbra-color), 0.14)),
    4: (0 2px 8px rgba(var(--v-shadow-key-umbra-color), var(--v-shadow-sm-opacity))),
    5: (0 4px 10px rgba(var(--v-shadow-key-umbra-color), 0.15)),
    6: (0 3px 12px rgba(var(--v-shadow-key-umbra-color), var(--v-shadow-md-opacity))),
    7: (0 4px 18px rgba(var(--v-shadow-key-umbra-color), 0.1)),
    8: (0 4px 18px rgba(var(--v-shadow-key-umbra-color), var(--v-shadow-lg-opacity))),
    9: (0 5px 14px rgba(var(--v-shadow-key-umbra-color), 0.18)),
    10: (0 5px 30px rgba(var(--v-shadow-key-umbra-color), var(--v-shadow-xl-opacity))),
    11: (0 5px 16px rgba(var(--v-shadow-key-umbra-color), 0.2)),
    12: (0 6px 17px rgba(var(--v-shadow-key-umbra-color), 0.22)),
    13: (0 6px 18px rgba(var(--v-shadow-key-umbra-color), 0.22)),
    14: (0 6px 19px rgba(var(--v-shadow-key-umbra-color), 0.24)),
    15: (0 7px 20px rgba(var(--v-shadow-key-umbra-color), 0.24)),
    16: (0 7px 21px rgba(var(--v-shadow-key-umbra-color), 0.26)),
    17: (0 7px 22px rgba(var(--v-shadow-key-umbra-color), 0.26)),
    18: (0 8px 23px rgba(var(--v-shadow-key-umbra-color), 0.28)),
    19: (0 8px 24px 6px rgba(var(--v-shadow-key-umbra-color), 0.28)),
    20: (0 9px 25px rgba(var(--v-shadow-key-umbra-color), 0.3)),
    21: (0 9px 26px rgba(var(--v-shadow-key-umbra-color), 0.32)),
    22: (0 9px 27px rgba(var(--v-shadow-key-umbra-color), 0.32)),
    23: (0 10px 28px rgba(var(--v-shadow-key-umbra-color), 0.34)),
    24: (0 10px 30px rgba(var(--v-shadow-key-umbra-color), 0.34))
  ) !default,

  $shadow-key-penumbra: (
    0: (0 0 transparent),
    1: (0 0 transparent),
    2: (0 0 transparent),
    3: (0 0 transparent),
    4: (0 0 transparent),
    5: (0 0 transparent),
    6: (0 0 transparent),
    7: (0 0 transparent),
    8: (0 0 transparent),
    9: (0 0 transparent),
    10: (0 0 transparent),
    11: (0 0 transparent),
    12: (0 0 transparent),
    13: (0 0 transparent),
    14: (0 0 transparent),
    15: (0 0 transparent),
    16: (0 0 transparent),
    17: (0 0 transparent),
    18: (0 0 transparent),
    19: (0 0 transparent),
    20: (0 0 transparent),
    21: (0 0 transparent),
    22: (0 0 transparent),
    23: (0 0 transparent),
    24: (0 0 transparent),
  ) !default,

  $shadow-key-ambient: (
    0: (0 0 transparent),
    1: (0 0 transparent),
    2: (0 0 transparent),
    3: (0 0 transparent),
    4: (0 0 transparent),
    5: (0 0 transparent),
    6: (0 0 transparent),
    7: (0 0 transparent),
    8: (0 0 transparent),
    9: (0 0 transparent),
    10: (0 0 transparent),
    11: (0 0 transparent),
    12: (0 0 transparent),
    13: (0 0 transparent),
    14: (0 0 transparent),
    15: (0 0 transparent),
    16: (0 0 transparent),
    17: (0 0 transparent),
    18: (0 0 transparent),
    19: (0 0 transparent),
    20: (0 0 transparent),
    21: (0 0 transparent),
    22: (0 0 transparent),
    23: (0 0 transparent),
    24: (0 0 transparent),
  ) !default,

  // 👉 Typography
  $typography: (
    "h1": (
      "size": 2.875rem,
      "weight": 500,
      "line-height": 4.25rem,
      "letter-spacing": normal
    ),
    "h2": (
      "size": 2.375rem,
      "weight": 500,
      "line-height": 3.5rem,
      "letter-spacing": normal
    ),
    "h3": (
      "size": 1.75rem,
      "weight": 500,
      "line-height": 2.625rem
    ),
    "h4": (
      "size": 1.5rem,
      "weight": 500,
      "line-height": 2.375rem,
      "letter-spacing": normal
    ),
    "h5": (
      "size": $typography-h5-font-size,
      "weight": 500,
      "line-height": 1.75rem
    ),
    "h6":(
      "size": 0.9375rem,
      "line-height": 1.375rem,
      "letter-spacing": normal
    ),
    "body-1":(
      "size": $typography-body-1-font-size,
      "line-height": $typography-body-1-line-height,
      "letter-spacing": normal
    ),
    "body-2": (
      "size": 0.8125rem,
      "line-height": 1.25rem,
      "letter-spacing": normal
    ),
    "subtitle-1":(
      "size": 0.9375rem,
      "weight": 400,
      "line-height": 1.375rem
    ),
    "subtitle-2": (
      "size": 0.8125rem,
      "weight": 400,
      "line-height": 1.25rem,
      "letter-spacing": normal
    ),
    "button": (
      "size": 0.9375rem,
      "weight": 500,
      "line-height": 1.125rem,
      "letter-spacing": 0.0269rem,
      "text-transform": capitalize
    ),
    "caption":(
      "size": 0.8125rem,
      "line-height": 1.125rem,
      "letter-spacing": 0.025rem
    ),
    "overline": (
      "size": 0.75rem,
      "weight": 400,
      "line-height": 0.875rem,
      "letter-spacing": 0.05rem,
    ),
  ) !default,

  // 👉 Alert
  $alert-title-font-size: 1.125rem !default,
  $alert-title-line-height: 1.5rem !default,
  $alert-border-opacity: 0.38 !default,

  // 👉 Badge
  $badge-dot-height: 8px !default,
  $badge-dot-width: 8px !default,
  $badge-min-width: 24px !default,
  $badge-height: 1.5rem !default,
  $badge-font-size: 0.8125rem !default,
  $badge-border-radius: 12px !default,
  $badge-border-color: rgb(var(--v-theme-surface)) !default,
  $badge-border-transform: scale(1.5) !default,
  $badge-dot-border-width: 2px !default,

  // 👉 Chip
  $chip-font-size: 13px !default,
  $chip-font-weight: 500 !default,
  $chip-label-border-radius: 0.375rem !default,
  $chip-height: 32px !default,
  $chip-close-size: 1.25rem !default,
  $chip-elevation: 0 !default,

  // 👉 Button
  $button-height: 38px !default,
  $button-padding-ratio: 1.9 !default,
  $button-line-height: 1.375rem !default,
  $button-disabled-opacity: 0.45 !default,
  $button-disabled-overlay: 0.2025 !default,
  $button-icon-font-size: 0.9375rem !default,

  // 👉 Button Group
  $btn-group-border-radius: 8px !default,

  // 👉 Dialog
  $dialog-card-header-padding: 24px 24px 0 !default,
  $dialog-card-header-text-padding-top: 24px !default,
  $dialog-card-text-padding: 24px !default,
  $dialog-elevation: 8 !default,

  // 👉 Card
  $card-title-font-size: $typography-h5-font-size !default,
  $card-text-font-size: $typography-body-1-font-size !default,
  $card-subtitle-font-size: 0.9375rem !default,
  $card-subtitle-header-padding: 0 !default,
  $card-subtitle-line-height: 1.375rem !default,
  $card-title-line-height: 1.75rem !default,
  $card-text-padding: 24px !default,
  $card-text-line-height: 1.375rem !default,
  $card-item-padding: 24px !default,
  $card-elevation: 6 !default,

  // 👉 Carousel
  $carousel-dot-margin: 0 !default,
  $carousel-dot-inactive-opacity: 0.4 !default,

  // 👉 Expansion Panel
  $expansion-panel-title-padding: 12px 20px 12px 24px !default,
  $expansion-panel-color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity)) !default,
  $expansion-panel-active-title-min-height: 46px !default,
  $expansion-panel-title-min-height: 46px !default,
  $expansion-panel-text-padding: 0 20px 20px 24px !default,

  // 👉 Field
  $field-font-size: 0.9375rem !default,
  $input-density: ("default": -2, "comfortable": -4.5, "compact": -6.5) !default,
  $field-outline-opacity: 0.42 !default,
  $field-border-width: 1px !default,
  $field-focused-border-width: 2px !default,
  $field-control-affixed-padding: 14px !default,

  // 👉 Input
  $input-details-padding-above: 4px !default,
  $input-details-font-size: 0.8125rem !default,

  // 👉 List
  $list-density: ("default": 0, "comfortable": -1.5, "compact": -2.5) !default,
  $list-border-radius: 6px !default,
  $list-item-padding: 8px 20px !default,
  $list-item-icon-size: 22px !default,
  $list-item-icon-margin-end: 10px !default,
  $list-item-icon-margin-start : 12px !default,
  $list-item-subtitle-line-height: 20px !default,
  $list-subheader-font-size: 13px !default,
  $list-subheader-line-height: 1.25rem !default,
  $list-subheader-padding-start: 20px !default,
  $list-subheader-padding-end: 20px !default,
  $list-subheader-min-height: 40px !default,
  $list-item-avatar-margin-start: 12px !default,
  $list-item-avatar-margin-end: 12px !default,
  $list-disabled-opacity: 0.4,

  // 👉 label
  $label-font-size: 0.9375rem !default,

  // 👉 message
  $messages-font-size: 13px !default,

  // 👉 menu
  $menu-elevation: 8 !default,

  // 👉 navigation drawer
  $navigation-drawer-temporary-elevation: 8 !default,

  // 👉 pagination
  $pagination-item-margin: 0.1875rem !default,

  // 👉 Progress Linear
  $progress-linear-background-opacity: 1 !default,

  // 👉 Radio
  $radio-group-label-selection-group-padding-inline: 0 !default,

  // 👉 slider
  $slider-thumb-hover-opacity: var(--v-activated-opacity) !default,
  $slider-thumb-label-padding: 2px 10px !default,
  $slider-thumb-label-font-size: 0.8125rem !default,
  $slider-track-active-size: 6px !default,

  // 👉 select
  $select-chips-margin-bottom: ("default": 1, "comfortable": 1, "compact": 1) !default,

  // 👉 snackbar
  $snackbar-background: rgb(var(--v-tooltip-background)) !default,
  $snackbar-color: rgb(var(--v-theme-surface)) !default,
  $snackbar-content-padding: 12px 16px !default,
  $snackbar-font-size: 0.8125rem !default,
  $snackbar-elevation: 2 !default,
  $snackbar-wrapper-min-height:44px !default,
  $snackbar-btn-padding: 0 9px !default,
  $snackbar-action-margin: 16px !default,

  // 👉 switch
  $switch-inset-track-width: 1.875rem !default,
  $switch-inset-track-height: 1.125rem !default,
  $switch-inset-thumb-height: 0.875rem !default,
  $switch-inset-thumb-width: 0.875rem !default,
  $switch-inset-thumb-off-height: 0.875rem !default,
  $switch-inset-thumb-off-width: 0.875rem !default,
  $switch-thumb-elevation: 2 !default,
  $switch-track-opacity: 1 !default,
  $switch-track-background: rgba(var(--v-theme-on-surface), var(--v-focus-opacity)) !default,
  $switch-thumb-background: rgb(var(--v-theme-on-primary)),

  // 👉 table
  $table-row-height: 50px !default,
  $table-color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity)) !default,
  $table-font-size: 0.9375rem !default,

  // 👉 tabs
  $tabs-height: 42px !default,
  $tab-min-width: 50px !default,

  // 👉 tooltip
  $tooltip-background-color: rgb(var(--v-tooltip-background)) !default,
  $tooltip-text-color: rgb(var(--v-theme-surface)) !default,
  $tooltip-font-size: 0.8125rem !default,
  $tooltip-border-radius: 0.25rem !default,
  $tooltip-padding: 5px 12px !default,

  // 👉 timeline
  $timeline-dot-size: 34px !default,
  $timeline-dot-divider-background: rgba(var(--v-border-color),0.08) !default,
  $timeline-divider-line-background: rgba(var(--v-border-color), var(--v-border-opacity)) !default,
  $timeline-divider-line-thickness: 1.5px !default,
  $timeline-item-padding: 16px !default,
);
