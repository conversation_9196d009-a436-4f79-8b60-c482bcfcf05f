<script setup>
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { useGroup } from "@/hooks/useGroup";
import message from "@/utils/message";
import { hasPermission } from "@/directives/can";
import { isTextOverflow } from "@/utils/helpers";

// 👉 create default query
const createDefaultQuery = () => {
  return {
    name: "",
    description: "",
    status: null,
    userGroupIdList: [],
    page: 1,
    size: 10,
    sortBy: null,
    orderBy: null,
  };
};

// 👉 Store
const searchQuery = ref(createDefaultQuery());
const isLoading = ref(false);
const tableData = ref(null);

// Data table options
const itemsPerPage = ref(10);
const page = ref(1);
const selectedRows = ref([]);

// 👉 update options
const updateOptions = (options) => {
  const sortBy = options[0]?.key;
  const orderBy = options[0]?.order;
  searchQuery.value.sortBy = sortBy;
  searchQuery.value.orderBy = orderBy;
  searchQuery.value.page = 1;
  page.value = 1;
  loadData();
};

const { userGroupList } = useGroup();

// 👉 Table Headers
const headers = [
  {
    title: "No",
    key: "no",
    sortable: false,
    width: 70,
  },
  {
    title: "User Group",
    key: "name",
  },
  {
    title: "Description",
    key: "description",
    sortable: false,
  },
  {
    title: "Status",
    key: "status",
    sortable: false,
  },

  {
    title: "Actions",
    key: "actions",
    sortable: false,
    fixed: true,
    width: 120,
  },
];

// 👉 reset query params
const resetQueryParams = () => {
  searchQuery.value = createDefaultQuery();
};

// 👉 get query params
const getQueryParams = () => {
  const params = {};
  params.name = searchQuery.value.name;
  params.statusId = searchQuery.value.status;
  params.description = searchQuery.value.description;
  params.page = searchQuery.value.page;
  params.size = searchQuery.value.size;
  params.userGroupIdList = searchQuery.value.userGroupIdList;

  if (searchQuery.value.sortBy && searchQuery.value.orderBy) {
    params.orderItemList = [
      {
        column: searchQuery.value.sortBy || "",
        asc: searchQuery.value.orderBy === "asc",
      },
    ];
  } else {
    params.orderItemList = null;
  }
  return params;
};

// 👉 load data
const loadData = async () => {
  try {
    isLoading.value = true;
    tableData.value = [];
    const params = getQueryParams();
    const res = await $api("/api/admin-api/v1/user-group/page-query", {
      method: "POST",
      body: params,
    });
    tableData.value = res.data;
  } catch (error) {
    console.error("Error loading data:", error);
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  loadData();
});

const handleSearch = () => {
  page.value = 1;
  searchQuery.value.page = 1;
  loadData();
};

const handlePageChange = (newPage) => {
  page.value = newPage;
  searchQuery.value.page = newPage;
  loadData();
};

const handleItemsPerPageChange = (newItemsPerPage) => {
  newItemsPerPage = parseInt(newItemsPerPage, 10);
  itemsPerPage.value = newItemsPerPage;
  searchQuery.value.size = newItemsPerPage;
  page.value = 1;
  searchQuery.value.page = 1;
  loadData();
};

// 👉 computed
const accounts = computed(() =>
  tableData.value?.records?.map((item, index) => ({
    ...item,
    no: (page.value - 1) * itemsPerPage.value + index + 1,
  }))
);

const totalUserGroups = computed(() => tableData.value?.total || 0);

// 👉 search filters
const status = [
  {
    title: "Enable",
    value: 1,
  },
  {
    title: "Disable",
    value: 0,
  },
];


const router = useRouter();

const handleAddUserGroup = () => {
  router.push("/user/group/add");
};

// 👉 edit user group
const editUserGroup = async (item) => {
  router.push(`/user/group/edit?id=${item.id}`);
};

// 👉 toggle status
const toggleStatusId = async (item, e) => {
  e.preventDefault();

  if (!hasPermission('Edit', 'UserGroup')) return

  confirmDialog({
    title: `${item.statusId === 1 ? "Disable User Group?" : "Enable User Group?"}`,
    text: `${item.statusId === 1 ? "Once disabled, this group and its roles cannot be assigned to users." : "Once enabled, this group and its roles can be assigned to users."}`,
    confirmButtonText: item.statusId === 1 ? "DISABLE" : "ENABLE",
    confirmButtonColor: item.statusId === 1 ? "error" : "primary",
    cancelButtonText: "CANCEL",
    async onConfirm(close) {
      const params = {
        id: item.id,
        statusId: item.statusId === 1 ? 0 : 1,
        name: item.name,
        description: item.description,
      };
      await $api(`/api/admin-api/v1/user-group`, {
        method: "PUT",
        body: params,
      });
      message.success("UserGroup status updated successfully");
      close();
      loadData();
    },
  });
};
</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            User Groups
            <VSpacer />

            <div class="app-usergroup-search-filter d-flex align-center gap-4">
              <!-- 👉 Add user group button -->
              <VBtn v-can="['Add', 'UserGroup']" @click="handleAddUserGroup">
                CREATE
              </VBtn>
            </div>
          </div>
        </VCardTitle>
      </VCardItem>

      <VCardText class="px-0">
        <VRow>
          <!-- 👉 Search User group -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField
              v-model="searchQuery.name"
              placeholder="Search User group"
              clearable
              @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Select Status -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppSelect
              v-model="searchQuery.status"
              placeholder="Select Status"
              :items="status"
              clearable
              clear-icon="tabler-x"
              @update:model-value="handleSearch"
            />
          </VCol>
        </VRow>
      </VCardText>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer
        :items="accounts"
        item-value="id"
        :items-length="totalUserGroups"
        :headers="headers"
        :loading="isLoading"
        class="text-no-wrap"
        @update:sortBy="updateOptions"
      >
        <!-- User group -->
        <template #[`item.name`]="{ item }">
          {{ item.name }}
        </template>

        <!-- Description -->
        <template #[`item.description`]="{ item }">
          <div v-if="isTextOverflow(item.description, 500)" style="max-width: 500px">
            <VTooltip>
              <template #activator="{ props }">
                <div class="text-truncate" v-bind="props">
                  {{ item.description }}
                </div>
              </template>
              <div>
                {{ item.description }}
              </div>
            </VTooltip>
          </div>
          <div v-else class="text-truncate" style="max-width: 500px">
            {{ item.description }}
          </div>
        </template>

        <!-- Status -->
        <template #[`item.status`]="{ item }">
          <VSwitch
            :model-value="item.statusId"
            @click.stop="toggleStatusId(item, $event)"
            :class="hasPermission('Edit', 'UserGroup') ? 'cursor-pointer' : 'opacity-50'"
            :false-value="0"
            :true-value="1"
          ></VSwitch>
        </template>

        <!-- Actions -->
        <template #[`item.actions`]="{ item }">
          <IconBtn
            @click="editUserGroup(item)"
            v-tooltip="'Edit User Group'"
            v-can="['Edit', 'UserGroup']"
          >
            <VIcon icon="mdi-pencil" class="hover:text-primary" />
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination
            :page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalUserGroups"
            @update:page="handlePageChange"
          >
            <div class="d-flex gap-3">
              <AppSelect
                :model-value="itemsPerPage"
                :items="[
                  { value: 10, title: '10' },
                  { value: 25, title: '25' },
                  { value: 50, title: '50' },
                  { value: 100, title: '100' },
                ]"
                style="inline-size: 6.25rem"
                @update:model-value="handleItemsPerPageChange"
              />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
    <!-- 👉 Add New User Group -->
  </section>
</template>
