@forward "@styles/scss/base/variables" with (
  $default-layout-with-vertical-nav-navbar-footer-roundness: 6px !default,

  $vertical-nav-navbar-style: "floating" !default, // options: elevated, floating

  // 👉 Vertical nav
  $vertical-nav-background-color-rgb: var(--v-theme-surface) !default,
  // ℹ️ This is used to keep consistency between nav items and nav header left & right margin
  // This is used by nav items & nav header
  $vertical-nav-horizontal-spacing: 0 !default,

  // Section title margin top (when its not first child)
  $vertical-nav-section-title-mt: 1rem !default,
  $vertical-nav-navbar-elevation: 4 !default,
  $vertical-nav-horizontal-padding: 0.75rem !default,
  $layout-vertical-nav-collapsed-width: 70px !default,

  // Move logo when vertical nav is mini (collapsed but not hovered)
  $vertical-nav-header-logo-translate-x-when-vertical-nav-mini: -1px !default,

  // Section title margin bottom
  $vertical-nav-section-title-mb: 0.375rem !default,

  // Vertical nav header padding
  $vertical-nav-header-padding: 1.25rem 1.3125rem !default,

  // Vertical nav icons
  $vertical-nav-items-icon-size: 1.375rem !default,
  $vertical-nav-items-nested-icon-size: 0.75rem !default,

  //  👉Footer
  $layout-vertical-nav-footer-height: 54px !default,

  // Gap between top level horizontal nav items
  $horizontal-nav-top-level-items-gap: 6px !default,

  $horizontal-nav-items-icon-margin-inline-end: 0.5rem !default,

  $horizontal-nav-popper-content-top: 0.375rem !default,

  $horizontal-nav-group-arrow-icon-size: 1.25rem !default,
  $horizontal-nav-third-level-icon-size: 0.75rem !default,
  /*
    ❗ Heads up
    ==================
    Here we assume we will always use shorthand property which will apply same padding on four side
    This is because this have been used as value of top property by `.popper-content`
  */
  $horizontal-nav-padding: 0.5rem !default,

  // 👉 Navbar
  $layout-vertical-nav-navbar-height: 64px !default,
  $layout-horizontal-nav-navbar-height: 54px !default,

  // Font sizes
  $font-sizes: (
    "xs": 0.6875rem,
    "sm": 0.8125rem,
    "base": 0.9375rem,
    "lg": 1.125rem,
    "xl": 1.5rem,
    "2xl": 1.75rem,
    "3xl": 2rem,
    "4xl": 2.375rem,
    "5xl": 3rem,
    "6xl": 3.5rem,
    "7xl": 4rem,
    "8xl": 4.5rem,
    "9xl": 5.25rem,
  ) !default,

  // Line heights
  $font-line-height: (
    "xs": 0.9375rem,
    "sm": 1.25rem,
    "base": 1.375rem,
    "lg": 1.75rem,
    "xl": 2.375rem,
    "2xl": 2.625rem,
    "3xl": 2.75rem,
    "4xl": 3.25rem,
    "5xl": 1,
    "6xl": 1,
    "7xl": 1,
    "8xl": 1,
    "9xl": 1
  ) !default,
);

/* Custom shadow opacity */
$shadow-opacity: (
  "sm": 0.3,
  "md": 0.4,
  "lg": 0.5,
) !default;

/* Custom shadow params */
$shadow-params: (
  "sm": 0 2px 6px 0,
  "md": 0 4px 16px 0,
  "lg": 0 6px 20px 0,
) !default;
