#!/usr/bin/env bash
set -eu

fargate_replace_taskdef() {
  
  local logName=$1
  local logStream=$2
  local inputFileName=$3
  local outputFileName=$4

  sed -e "s;%ROLE%;$ROLE;g" \
    -e "s;%SERVICE_NAME%;$SERVICE_NAME;g" \
    -e "s;%LOG_NAME%;$logName;g" \
    -e "s;%REGION%;$REGION;g" \
    -e "s;%LOG_STREAM%;$logStream;g" \
    -e "s;%REPOSITORY_NAME%;$REPOSITORY_NAME;g" \
    -e "s;%IMAGE_ENV%;$IMAGE_ENV;g" \
    -e "s;%BUILD_NUMBER%;$BUILD_NUMBER;g" \
    -e "s;%NAME%;$CONTAINER_NAME;g" \
    -e "s;%FAMILY%;$FAMILY;g" \
    -e "s;%MEMORY%;$MEMORY;g" \
    -e "s;\"%MEMORY_INT%\";$MEMORY;g" \
    -e "s;%CPU%;$CPU;g" \
    "$inputFileName" > "$outputFileName"
}
 
dual_loadbalancer_fargate_create_service() {
    local revision=$1
    echo ">> dual loadbalancer - create fargate service revision $revision"

    common_get_loadbalancer_target "$LOADBALANCER_LAN" "$TARGET_GROUP_NAME" "$VPCID" "$ROUTE_RECORD" "$HOSTED_ZONE" "$HEALTH_CHECK_PATH" "$SLOW_START" "$CONDITIONER_LISTEN_RULE"
    local targetGroupArn=$RETURN_VALUE
    echo ">> targetGroupArn $targetGroupArn"

    if [[ $ENV != zgp && $USE_ROUTE_53 == 1 ]]
    then
      common_add_route53_record "$LOADBALANCER_LAN" "$ROUTE_RECORD" "$HOSTED_ZONE"
    fi

    common_get_loadbalancer_target "$LOADBALANCER_WAN" "$TARGET_GROUP_NAME_WAN" "$VPCID" "$ROUTE_RECORD_WAN" "$HOSTED_ZONE" "$HEALTH_CHECK_PATH" "$SLOW_START" "$CONDITIONER_LISTEN_RULE_WAN"
    local targetGroupWanArn=$RETURN_VALUE
    echo ">> targetGroupWanArn $targetGroupWanArn"

    if [[ $ENV != zgp && $USE_ROUTE_53 == 1 ]]
    then
      common_add_route53_record "$LOADBALANCER_WAN" "$ROUTE_RECORD_WAN" "$HOSTED_ZONE"
    fi

    common_get_subnet "$ENV" "$DEPLOY_ENV" "$REGION"
    local subnets=$RETURN_VALUE
    echo ">> subnets $subnets"

    common_get_security_group "$ENV" "$DEPLOY_ENV" "$REGION"
    local securityGroup=$RETURN_VALUE
    echo ">> securityGroup $securityGroup"

    echo ">> ecs create-service launch type $LAUNCH_TYPE"

    local myloadbalances="[{\"targetGroupArn\":\"${targetGroupArn}\",\"containerName\":\"${CONTAINER_NAME}\",\"containerPort\":443},{\"targetGroupArn\":\"${targetGroupWanArn}\",\"containerName\":\"${CONTAINER_NAME}\",\"containerPort\":443}]"
    echo ">> myloadbalances $myloadbalances"

    aws ecs create-service \
      --cluster "$CLUSTER" \
      --service-name "$DEPLOY_SERVICE_NAME" \
      --desired-count "$SERVICE_COUNT" \
      --task-definition "$FAMILY:$revision" \
      --launch-type "$LAUNCH_TYPE" \
      --network-configuration "awsvpcConfiguration={subnets=[$subnets],securityGroups=[$securityGroup],assignPublicIp=DISABLED}" \
      --load-balancers  "$myloadbalances" \
      --platform-version "$PLATFORM_VERSION" \
      --enable-ecs-managed-tags \
      --propagate-tags SERVICE \
      --tags "$TAGS" \
      --region "$REGION"
}

fargate_create_service() {
    local revision=$1
    echo ">> create fargate service revision $revision"

    local loadBalancer
    local condition

    if [[ $ROUTE_RECORD =~ ^lan ]] 
    then 
      loadBalancer=$LOADBALANCER_LAN 
      condition=$CONDITIONER_LISTEN_RULE
    else 
      loadBalancer=$LOADBALANCER_WAN 
      condition=$$CONDITIONER_LISTEN_WAN_RULE
    fi

    echo ">> loadBalancer $loadBalancer"

    local -u targetGroupName="$TARGET_GROUP_NAME"
    echo ">> targetGroupName $targetGroupName"

    common_get_loadbalancer_target "$loadBalancer" "$targetGroupName" "$VPCID" "$ROUTE_RECORD" "$HOSTED_ZONE" "$HEALTH_CHECK_PATH" "$SLOW_START" "$condition"
    local targetGroupArn=$RETURN_VALUE
    echo ">> targetGroupArn $targetGroupArn"

    if [[ $ENV != zgp && $USE_ROUTE_53 == 1 ]]
    then
      common_add_route53_record "$loadBalancer" "$ROUTE_RECORD" "$HOSTED_ZONE"
    fi

    common_get_subnet "$ENV" "$DEPLOY_ENV" "$REGION"
    local subnets=$RETURN_VALUE
    echo ">> subnets $subnets"

    common_get_security_group "$ENV" "$DEPLOY_ENV" "$REGION"
    local securityGroup=$RETURN_VALUE
    echo ">> securityGroup $securityGroup"

    echo ">> ecs create-service launch type $LAUNCH_TYPE"
    if [[ $LAUNCH_TYPE == 'FARGATE' ]]
    then
      aws ecs create-service \
        --cluster "$CLUSTER" \
        --service-name "$DEPLOY_SERVICE_NAME" \
        --desired-count "$SERVICE_COUNT" \
        --task-definition "$FAMILY:$revision" \
        --launch-type "$LAUNCH_TYPE" \
        --network-configuration "awsvpcConfiguration={subnets=[$subnets],securityGroups=[$securityGroup],assignPublicIp=DISABLED}" \
        --load-balancers "targetGroupArn=${targetGroupArn},containerName=${CONTAINER_NAME},containerPort=443" \
        --platform-version "$PLATFORM_VERSION" \
        --enable-ecs-managed-tags \
        --propagate-tags SERVICE \
        --tags "$TAGS" \
        --region "$REGION"
    else
      aws ecs create-service \
        --cluster "$CLUSTER" \
        --service-name "$DEPLOY_SERVICE_NAME" \
        --desired-count "$SERVICE_COUNT" \
        --task-definition "$FAMILY:$revision" \
        --launch-type "$LAUNCH_TYPE" \
        --network-configuration "awsvpcConfiguration={subnets=[$subnets],securityGroups=[$securityGroup],assignPublicIp=DISABLED}" \
        --load-balancers "targetGroupArn=${targetGroupArn},containerName=${CONTAINER_NAME},containerPort=443" \
        --region "$REGION"
    fi
}

fargate_update_service()
{
  local revision=$1
  echo ">> fargate_update_service revision $revision, desired count $SERVICE_COUNT"

  if [[ $LAUNCH_TYPE == 'FARGATE' ]]
  then
    aws ecs update-service \
    --cluster "$CLUSTER" \
    --region "$REGION" \
    --service "$DEPLOY_SERVICE_NAME" \
    --task-definition "$FAMILY:$revision" \
    --platform-version "$PLATFORM_VERSION" \
    --enable-ecs-managed-tags \
    --propagate-tags TASK_DEFINITION \
    --force-new-deployment
  else
    aws ecs update-service \
      --cluster "$CLUSTER" \
      --region "$REGION" \
      --service "$DEPLOY_SERVICE_NAME" \
      --task-definition "$FAMILY:$revision" \
      --force-new-deployment
  fi
}

fargate_create_auto_scaling()
{
  common_register_autoscaling "$CLUSTER" "$DEPLOY_SERVICE_NAME" "$SERVICE_COUNT" "$SERVICE_MAX_COUNT"
  #common_create_autoscaling_target "$CLUSTER" "$DEPLOY_SERVICE_NAME" "$ALARM_NAME"_AVERAGE_CPU_UTILIZATION  '{"TargetValue":70.0,"PredefinedMetricSpecification":{"PredefinedMetricType":"ECSServiceAverageCPUUtilization"},"ScaleOutCooldown":60,"ScaleInCooldown":60,"DisableScaleIn":true}'
  #common_create_autoscaling_target "$CLUSTER" "$DEPLOY_SERVICE_NAME" "$ALARM_NAME"_AVERAGE_MEM_UTILIZATION '{"TargetValue":70.0,"PredefinedMetricSpecification":{"PredefinedMetricType":"ECSServiceAverageMemoryUtilization"},"ScaleOutCooldown":300,"ScaleInCooldown":300,"DisableScaleIn":true}'
  
  common_create_autoscaling_step "$CLUSTER" "$DEPLOY_SERVICE_NAME" ReqCount-ScaleOutPolicy 'AdjustmentType=ChangeInCapacity,StepAdjustments=[{MetricIntervalLowerBound=0,MetricIntervalUpperBound=100,ScalingAdjustment=2},{MetricIntervalLowerBound=100,MetricIntervalUpperBound=200,ScalingAdjustment=3},{MetricIntervalLowerBound=200,ScalingAdjustment=4}],Cooldown=1800,MetricAggregationType=Average'
  local scaleOutArn=$RETURN_VALUE

  common_create_autoscaling_step "$CLUSTER" "$DEPLOY_SERVICE_NAME" ReqCount-ScaleInPolicy 'AdjustmentType=ChangeInCapacity,StepAdjustments=[{MetricIntervalUpperBound=0,ScalingAdjustment=-1}],Cooldown=1800,MetricAggregationType=Average'
  local scaleInArn=$RETURN_VALUE

  common_create_autoscaling_step "$CLUSTER" "$DEPLOY_SERVICE_NAME" CPUUtilization-ScaleOutPolicy 'AdjustmentType=ChangeInCapacity,StepAdjustments=[{MetricIntervalLowerBound=0,MetricIntervalUpperBound=15,ScalingAdjustment=2},{MetricIntervalLowerBound=15,MetricIntervalUpperBound=30,ScalingAdjustment=3},{MetricIntervalLowerBound=30,ScalingAdjustment=4}],Cooldown=1800,MetricAggregationType=Maximum'
  local cpuScaleOutArn=$RETURN_VALUE

  common_create_alarm_alb "$ALARM_NAME" "$ALARM_ECS_NAME" "$TARGET_GROUP_NAME" "$ALARM_ALERT_ACTION" "$ALARM_ALERT_ACTION" "$scaleOutArn" "$scaleInArn" 
  common_create_alarm_cpu "$ALARM_ECS_NAME" "$CLUSTER" "$DEPLOY_SERVICE_NAME" "$ALARM_ALERT_ACTION" "$ALARM_ALERT_ACTION" "$cpuScaleOutArn"
}

echo fargate_create
common_init "$DEPLOY_ENV" "$SERVICE_ENV" "$REGION"
common_create_repository "$REPOSITORY_NAME"
common_create_log "$LOGNAME"
common_create_taskdef

fargate_replace_taskdef "$LOGNAME" "$LOGSTREAM" "deploy/taskdef-${SERVICE_ENV}.json" "taskdef-v_latest.json" 

# Register the task definition in the repository
aws ecs register-task-definition --family "$FAMILY" --cli-input-json file://${WORKSPACE}/taskdef-v_latest.json --region "$REGION" --tags "$TAGS"

taskDefRevision=$(aws ecs describe-task-definition --task-definition "$FAMILY" --region "$REGION" | jq .taskDefinition.revision)

# Make a single API call and store the result
serviceInfo=$(aws ecs describe-services --services "$DEPLOY_SERVICE_NAME" --cluster "$CLUSTER" --region "$REGION")

echo ">> serviceInfo: $serviceInfo"

# Get Services Details 
# runningCount=$(echo "$serviceInfo" | jq -r '.services[0] | .runningCount')
# serviceArn=$(echo "$serviceInfo" | jq -r '.services[0] | .serviceArn')
serviceArn=$(aws ecs describe-services --services "$DEPLOY_SERVICE_NAME" --cluster "$CLUSTER" --region "$REGION" | jq -r .services[].serviceArn)
runningCount=$(aws ecs describe-services --services "$DEPLOY_SERVICE_NAME" --cluster "$CLUSTER" --region "$REGION" | jq .services[].runningCount)

echo ">> serviceArn: $serviceArn"
echo ">> running count: $runningCount"

# runningCount=$(aws ecs describe-services --services "$DEPLOY_SERVICE_NAME" --cluster "$CLUSTER" --region "$REGION" | jq .services[].runningCount)

if [[ -z $runningCount || $runningCount = '0' ]] 
then
  echo ">> calling fargate_create_service $taskDefRevision"
  if [[ $DUAL_LOADBALANCER == 1 ]]
  then
    dual_loadbalancer_fargate_create_service "$taskDefRevision"
  else
    fargate_create_service "$taskDefRevision"
  fi
  if [[ $ENV == "zgp" ]]; then fargate_create_auto_scaling ; fi
  #fargate_create_auto_scaling
else
 fargate_update_service "$taskDefRevision"
fi

# Update ECS Tags
common_update_ecs_tags "$serviceArn" "$TAGS"
