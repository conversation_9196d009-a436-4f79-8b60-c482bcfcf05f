# Don't remove or change this file name
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: {{ APP_NAME }}
  namespace: {{ APP_NAMESPACE }}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  strategy:
    canary:
      steps:
        # - pause: {}  # 在开发，测试环境，不需要暂停， 在生产环境需要暂停
        - setWeight: 100
        # Refer to https://argo-rollouts.readthedocs.io/en/stable/features/canary/
        # Example:
        # - pause: {}
        # - setWeight: 33
        # - pause: { duration: 10s }
  selector:
    matchLabels:
      app: {{ APP_NAME }}
  template:
    metadata:
      labels:
        app: {{ APP_NAME }}
    spec:
      serviceAccountName: {{ APP_SERVICE_ACCOUNT_NAME }}
      containers:
        - name: {{ APP_NAME }}
          image: "{{ ECR_DOMAIN }}/{{ ECR_NAME }}:{{ IMAGE_TAG }}"
          imagePullPolicy: Always
          resources:
            requests:
              memory: "1024Mi"
              cpu: "0.5"
            limits:
              memory: "1024Mi"
              cpu: "0.5"
          ports:
            - containerPort: 443
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /healthcheck
              port: 443
              scheme: HTTPS
          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 443
              scheme: HTTPS
          startupProbe:
            httpGet:
              path: /healthcheck
              port: 443
              scheme: HTTPS
          volumeMounts:
            - mountPath: "/etc/certs"
              name: certs
              readOnly: true
      volumes:
        - name: certs
          secret:
            secretName: wildcard-gold-razer-com-ssl