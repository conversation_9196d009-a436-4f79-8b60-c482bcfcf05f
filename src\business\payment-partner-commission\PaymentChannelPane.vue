<script setup>
import { ref, computed, watch, reactive, nextTick, inject, onActivated, onDeactivated, onMounted, defineEmits } from "vue";
import bigUtil from "@/utils/bigNumber";
import {
  useLocalPaymentChannelList,
  useLocalCommissionTerritoryList,
} from "./usePaymentChannel";
import dateUtil from "@/utils/day";
import { requiredValidator, commissionRateValidator } from "@/utils/validators";
import { useStorage } from "@/hooks/useStorage";

import BulkEdit from "./BulkEdit.vue";
import PaymentChannelTable from "./PaymentChannelTable.vue";

// Add component name
defineOptions({
  name: "PaymentChannelPane",
});

// 定义组件的 emits
const emit = defineEmits(['update:paymentChannelList']);

// props 定义
const props = defineProps({
  paymentChannelList: {
    type: Array,
    default: () => [],
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  commissionRateErrors: {
    type: Array,
    default: () => [],
  },
  effectiveOnErrors: {
    type: Array,
    default: () => [],
  },
});

// Inject selected state based on name property
const selectIds = inject(
  props.name === 'RAZER GOLD'
    ? 'razerGoldSelectedIds'
    : props.name === 'RAZER GOLD PIN'
      ? 'razerGoldPinSelectedIds'
      : 'thirdPartySelectedIds',
  ref([])
);

const paymentChannelList = ref(props.paymentChannelList);

const state = reactive({
  localItems: [],
  localSelectedIds: [],
  localPaymentChannelList: [],
  filterList: [],
  isDataReady: false,
  isComponentActive: false
});

const indeterminate = computed(() => {
  if (!state.isDataReady || !state.filterList.length) return false;
  
  const availableItems = state.filterList.filter(item => !item.isSelected);
  return state.localSelectedIds.length > 0 && 
         state.localSelectedIds.length < availableItems.length;
});

const keyword = ref("");
const checkAll = ref(false);
const isExpand = inject("isExpand");

const selectedRows = ref([]);
const items = ref([]);
const effectiveDate = ref(null);
const showAll = ref(false);
const { paymentChannelList: localPaymentChannelList } =
  useLocalPaymentChannelList(items);
const { commissionTerritoryList: localCommissionTerritoryList } =
  useLocalCommissionTerritoryList(items);

const tabIndex = inject('tabIndex');
const updatePaymentChannelItems = inject('updatePaymentChannelItems');

// 注入对应的 PaymentChannelItems
const razerGoldPaymentChannelItems = inject('razerGoldPaymentChannelItems');
const razerGoldPinPaymentChannelItems = inject('razerGoldPinPaymentChannelItems');
const thirdPartyPaymentChannelItems = inject('thirdPartyPaymentChannelItems');

// 初始化本地状态
const initializeLocalState = () => {
  if (!state.isComponentActive) return;
  
  try {
    // 初始化选中ID
    state.localSelectedIds = [...(selectIds.value || [])];
    
    // 初始化支付渠道列表
    state.localPaymentChannelList = props.paymentChannelList.map(item => ({
      ...item,
      isSelected: false
    }));
    
    // 初始化表格数据
    const currentItems = props.name === 'RAZER GOLD' ? razerGoldPaymentChannelItems.value :
                        props.name === 'RAZER GOLD PIN' ? razerGoldPinPaymentChannelItems.value :
                        thirdPartyPaymentChannelItems.value;
    
    if (currentItems?.some(item => item.isDuplicated)) {
      state.localItems = [...currentItems];
    }
    
    // 更新过滤列表
    updateFilterList();
    
    state.isDataReady = true;
  } catch (error) {
    console.error('Error initializing local state:', error);
    state.isDataReady = false;
  }
};

// 更新过滤列表
const updateFilterList = () => {
  state.filterList = state.localPaymentChannelList
    .filter(item => item.name.includes(keyword.value))
    .map(item => ({
      ...item,
      isSelected: state.localItems.some(tableItem => tableItem.customId === item.customId)
    }));
};

// 处理全选
const handleCheckAll = () => {
  if (!state.isDataReady) return;
  
  nextTick(() => {
    // 获取可选的项目（未被添加到表格的项目）
    const availableItems = state.filterList.filter(item => !item.isSelected);
    
    if (indeterminate.value) {
      state.localSelectedIds = availableItems.map(item => item.customId);
      checkAll.value = false;
    } else {
      state.localSelectedIds = checkAll.value 
        ? availableItems.map(item => item.customId)
        : [];
    }
    
    // 同步到父组件
    selectIds.value = [...state.localSelectedIds];
  });
};

// 处理单个选择
const handleCheck = (item) => {
  if (!state.isDataReady || item.isSelected) return;
  
  const index = state.localSelectedIds.indexOf(item.customId);
  if (index > -1) {
    state.localSelectedIds.splice(index, 1);
  } else {
    state.localSelectedIds.push(item.customId);
  }
  
  // 更新全选状态
  const availableItems = state.filterList.filter(item => !item.isSelected);
  checkAll.value = state.localSelectedIds.length === availableItems.length;
  
  // 同步到父组件
  selectIds.value = [...state.localSelectedIds];
};

// 监听关键字变化
watch(keyword, () => {
  if (state.isDataReady) {
    updateFilterList();
  }
});

// 监听支付渠道列表变化
watch(() => paymentChannelList.value, () => {
  if (state.isDataReady) {
    state.localPaymentChannelList = paymentChannelList.value.map(item => ({
      ...item,
      isSelected: false
    }));
    updateFilterList();
  }
}, { deep: true });

// 组件挂载时初始化
onMounted(() => {
  initializeLocalState();
});

const totalSelected = computed(() => {
  if (!state.isDataReady) return 0;
  return state.localSelectedIds.filter(id => 
    !state.localItems.some(item => item.customId === id)
  ).length;
});

const handleAdd = () => {
  // 只添加未在表格中的数据
  const rows = paymentChannelList.value.filter((item) =>
    selectIds.value.includes(item.customId) && !state.localItems.some(tableItem => tableItem.customId === item.customId)
  );

  const newItems = rows.map((item, index) => {
    return {
      ...item,
      no: (state.localItems.length + index + 1),
      customId: item.customId,
      merchantCurrency: "Others",
      convertCommissionRate: item.defaultRate ? Number(item.defaultRate).toFixed(2) : "",
      convertEffectiveOn: item.effectiveOn || null,
      isEditing: false,
      minAmount: "",
      fixAmount: "",
      status: "Draft",
      isCommissionRateEditing: false,
      isEffectiveOnEditing: false,
    };
  });

  // 更新 items
  state.localItems = [...state.localItems, ...newItems];

  // 更新父组件中的数据
  updatePaymentChannelItems(state.localItems, tabIndex.value);

  // 更新选中状态
  updateFilterList();

  // 清空选择
  selectIds.value = [];
  checkAll.value = false;

  // Collapse at the same time
  isExpand.value = false;
};

const handleDateApplied = (dateTime) => {
  dateTime = dateUtil.format(dateTime, "DD-MMM-YYYY HH:mm:ss");
  selectedRows.value.forEach((item) => {
    item.convertEffectiveOn = dateTime;
    item.effectiveOn = dateTime;
    item.isEditing = false;
  });
};

const handleDateCanceled = () => { };

const handleEdit = (item) => {
  state.localItems.forEach((item) => {
    item.isCloseDatePicker = false;
    item.isEditing = false;
  });
  item.isEditing = true;
};

const handleDelete = (item) => {
  console.log("handleDelete", item);

  const customId = item.customId;
  // Add isSelected: false property to selected filterItems
  const target = state.filterList.find((item) => item.customId === customId);
  if (target) {
    target.isSelected = false;
  }

  selectIds.value = selectIds.value.filter((selectId) => selectId !== customId);

  // Delete item from items at the same time
  state.localItems = state.localItems
    .filter((item) => item.customId !== customId)
    .map((item, index) => {
      return {
        ...item,
        no: index + 1,
      };
    });

  updatePaymentChannelItems(state.localItems, tabIndex.value);
};

const handleClearAllSelected = (selectedIds) => {
  selectIds.value = [];
  checkAll.value = false;
  state.filterList.forEach((item) => {
    if (selectedIds.includes(item.customId)) {
      item.isSelected = false;
    }
  });
};

const handleBulkEdit = (data) => {
  selectedRows.value.forEach((item) => {
    if (data.commissionRate && data.commissionRate !== "0") {
      item.convertCommissionRate = data.commissionRate;
    }
    if (data.effectiveDate) {
      item.convertEffectiveOn = data.effectiveDate;
      item.effectiveOn = data.effectiveDate;
    }
  });
};

// 组件激活时的处理
onActivated(() => {
  console.log(`${props.name} component activated`);
  state.isComponentActive = true;
  nextTick(() => {
    initializeLocalState();
  });
});

// 组件停用时的处理
onDeactivated(() => {
  console.log(`${props.name} component deactivated`);
  state.isComponentActive = false;
  state.isDataReady = false;
});

// 更新父组件数据
const updateParentData = () => {
  if (!state.isComponentActive || !state.isDataReady) return;
  emit('update:paymentChannelList', state.localItems);
};

// 监听本地数据变化
watch(() => state.localItems, () => {
  if (state.isComponentActive && state.isDataReady) {
    updateParentData();
    updateFilterList();
  }
}, { deep: true });

</script>

<template>
  <div class="payment-channel-pane">
    <VExpandTransition>
      <div class="bg-#2D2D2D rounded-b-4px" v-show="isExpand">
        <div class="flex flex-col p-4 gap-4">
          <VRow>
            <VCol md="4" sm="6" cols="12">
              <AppTextField v-model="keyword" class="md:w-530px" placeholder="Search Payment Channel"
                prepend-inner-icon="mdi-magnify" />
            </VCol>
          </VRow>
          <div>
            <div class="flex items-center h-10">
              <VCheckbox :indeterminate="indeterminate" v-model="checkAll" @update:model-value="handleCheckAll" />
              <div>{{ totalSelected }} items selected</div>
            </div>
            <VDivider></VDivider>
          </div>
          <VRow class="max-h-[144px] overflow-y-auto">
            <VCol md="3" sm="6" cols="12" class="py-2!" v-for="item in state.filterList" :key="item.customId">
              <VCheckbox 
                :model-value="item.isSelected || selectIds.includes(item.customId)"
                :disabled="item.isSelected"
                :class="{ 'opacity-50 cursor-not-allowed': item.isSelected }" 
                @update:model-value="handleCheck(item)"
              >
                <template #label>
                  <div class="line-2">{{ item.paymentMethodName }}</div>
                </template>
              </VCheckbox>
            </VCol>
          </VRow>
          <div class="flex justify-end">
            <VBtn class="w-120px" :disabled="!selectIds.length" @click="handleAdd">ADD</VBtn>
          </div>
        </div>
      </div>
    </VExpandTransition>
    <div class="pt-6 bg-#1E1E1E flex flex-col">
      <PaymentChannelTable
        v-model:items="state.localItems"
        :payment-channel-list="localPaymentChannelList"
        :commission-territory-list="localCommissionTerritoryList"
        v-model:selected-rows="selectedRows"
        :commission-rate-errors="commissionRateErrors"
        :effective-on-errors="effectiveOnErrors"
        :class="{ 'max-h-[600px] overflow-y-auto': state.localItems.length > 10 && !showAll }"
        @delete="handleDelete"
        @edit="handleEdit"
        @clear-all-selected="handleClearAllSelected"
      >
        <template #bulk>
          <BulkEdit class="w-80px" @update="handleBulkEdit" />
        </template>
      </PaymentChannelTable>
      <div class="flex justify-center items-center h-52px" v-if="state.localItems.length > 10 && !showAll">
        <VBtn variant="outlined" class="w-110px" @click="showAll = true">SHOW ALL</VBtn>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.date-time-button) {
  padding: 4px 16px;
}
</style>
