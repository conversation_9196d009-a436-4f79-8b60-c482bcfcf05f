#!/usr/bin/env bash

declare -x SERVICE_NAME=gold-admin-portal-v2
declare -xr BUILD=FARGATE

declare -x ROUTE_RECORD=console-v2
declare -xr USE_ROUTE_53=0
declare -x MEMORY
declare -x CPU
declare -x SLOW_START
declare -xu TARGET_GROUP_NAME
declare -xu CONTAINER_NAME
declare -x LOADBALANCER_WAN
declare -x TAGS

deploy_override()
{
    MEMORY=1024
    CPU=512
    SLOW_START=1
    TARGET_GROUP_NAME="$DEPLOY_ENV"-TG-ECS-GOLD-ADMIN-PORTAL-V2
    CONTAINER_NAME="$DEPLOY_ENV"-EC-GOLD-ADMIN-PORTAL-V2
    TAGS='[{"key":"Project","value":"ADMIN-Portal-V2"},{"key":"Framework","value":"Node20"}]'

    case $ENV in
        zgd)
        LOADBALANCER_WAN=zgd-alb-wan-ecs
        TAGS='[{"key":"Name","value":"ZGD-EC-GOLD-ADMIN-PORTAL-V2"},{"key":"Project","value":"Gold-Admin-Portal-V2-App"},{"key":"Environment","value":"ZGD"}, {"key":"Owner","value":"Dev"},{"key":"Framework","value":"Node20"}]'
        ;;
        zgq)
        LOADBALANCER_WAN=ZGQ-ALB-WAN-OFFICE
        TAGS='[{"key":"Name","value":"ZGQ-EC-GOLD-ADMIN-PORTAL-V2"},{"key":"Project","value":"Gold-Admin-Portal-V2-App"},{"key":"Environment","value":"ZGQ"}, {"key":"Owner","value":"Dev"},{"key":"Framework","value":"Node20"}]'
        ;;
        zgs)
        LOADBALANCER_WAN=ZGS-ALB-WAN-OFFICE
        TAGS='[{"key":"Name","value":"ZGS-EC-GOLD-ADMIN-PORTAL-V2"},{"key":"Project","value":"Gold-Admin-Portal-V2-App"},{"key":"Environment","value":"ZGS"}, {"key":"Owner","value":"Dev"},{"key":"Framework","value":"Node20"}]'
        ;;
        zgp)
        LOADBALANCER_WAN=ZGP-ALB-WAN-OFFICE
        TAGS='[{"key":"Name","value":"ZGP-EC-GOLD-ADMIN-PORTAL-V2"},{"key":"Project","value":"Gold-Admin-Portal-V2-App"},{"key":"Environment","value":"ZGP"}, {"key":"Owner","value":"Dev"},{"key":"Framework","value":"Node20"}]'
        ;;
    esac
}


