#!/usr/bin/env bash

declare -x SERVICE_NAME=gold-admin-portal-v2
declare -xr BUILD=PREBUILD
declare -x JK_BUILD_NUMBER=$BUILD_NUMBER
declare -x SERVICE_NAME

deploy_override()
{
    echo "override SERVICE_NAME"
    case $ENV in
        zgd)
        SERVICE_NAME=dev
        ;;
        zgq)
        SERVICE_NAME=qa
        ;;
    esac
}

prebuild()
{
    echo "BUILD_NUMBER $JK_BUILD_NUMBER"
    npm install;
    npm run build:icons;

    case $ENV in
        zgd)
        npm run build:dev
        ;;
        zgq)
        npm run build:qa
        ;;
        zgs)
        npm run build:sandbox
        ;;
        zgp)
        npm run build:prod
        ;;
    esac
}



