export const useGroup = () => {
  const userGroupList = ref([])
  const loadData = async () => {
    const res = await $api('/api/admin-api/v1/user-group/query-all', {
      method: 'GET',
    })
    userGroupList.value = Array.isArray(res.data) 
      ? res.data.map(item => ({
          ...item,
          title: item.name,
          value: item.id
        }))
      : [];
  }
  onMounted(async () => {
    await loadData()
  })
  return { userGroupList }
}