<script setup>
import FlatPickr from "vue-flatpickr-component";
import { useTheme } from "vuetify";
import {
  VField,
  filterFieldProps,
  makeVFieldProps,
} from "vuetify/lib/components/VField/VField";
import { VInput, makeVInputProps } from "vuetify/lib/components/VInput/VInput";

import { filterInputAttrs } from "vuetify/lib/util/helpers";
import { useConfigStore } from "@/stores/configStore";

const props = defineProps({
  autofocus: Boolean,
  counter: [Boolean, Number, String],
  counterValue: Function,
  prefix: String,
  placeholder: String,
  persistentPlaceholder: Boolean,
  persistentCounter: Boolean,
  suffix: String,
  type: {
    type: String,
    default: "text",
  },
  modelModifiers: Object,
  enableSeconds: {
    type: Boolean,
    default: false,
  },
  ...makeVInputProps({
    density: "comfortable",
    hideDetails: "auto",
  }),
  ...makeVFieldProps({
    variant: "outlined",
    color: "primary",
  }),
});

const emit = defineEmits([
  "click:control",
  "mousedown:control",
  "update:focused",
  "update:modelValue",
  "click:clear",
  "close",
]);

defineOptions({
  inheritAttrs: false,
});

const configStore = useConfigStore();
const attrs = useAttrs();
const [rootAttrs, compAttrs] = filterInputAttrs(attrs);
const inputProps = ref(VInput.filterProps(props));
const fieldProps = ref(filterFieldProps(props));
const refFlatPicker = ref();
const { focused } = useFocus(refFlatPicker);
const isCalendarOpen = ref(false);
const isInlinePicker = ref(false);
const pickerRef = ref(null);
const calendarWidth = ref(0);

// Add reactive variables for storing time label texts
const timeLabels = {
  hour: "Hours",
  minute: "Minutes",
  second: "Second",
};

// Modify flat picker manipulation part
if (compAttrs.config && compAttrs.config.inline) {
  isInlinePicker.value = compAttrs.config.inline;
  Object.assign(compAttrs, { altInputClass: "inlinePicker" });
}

// Ensure component properties have higher priority than config object
const enableSeconds = computed(() => {
  // If enableSeconds is explicitly set in the config object, use it
  if (compAttrs.config && "enableSeconds" in compAttrs.config) {
    return compAttrs.config.enableSeconds;
  }
  // Otherwise use the component property
  return props.enableSeconds;
});

// flatpickr configuration
compAttrs.config = {
  ...compAttrs.config,
  // 尊重传入的enableTime配置，如果未指定则默认为true
  enableTime: compAttrs.config?.enableTime !== undefined ? compAttrs.config.enableTime : true,
  // 确保enableSeconds配置正确传递到flatpickr
  enableSeconds: true, // 始终启用秒的显示
  // 自定义属性，指示是否禁用秒编辑
  disableSecond: !enableSeconds.value,
  time_24hr: true,
  defaultHour: new Date().getHours(),
  defaultMinute: new Date().getMinutes(),
  noCalendar: compAttrs.config?.noCalendar || false,
  prevArrow:
    '<i class="tabler-chevron-left v-icon" style="font-size: 20px; height: 20px; width: 20px;"></i>',
  nextArrow:
    '<i class="tabler-chevron-right v-icon" style="font-size: 20px; height: 20px; width: 20px;"></i>',
  // 添加年份下拉选择支持，使用最近40年
  yearDropdown: true,
  yearRange: `${new Date().getFullYear() - 40}:${new Date().getFullYear() + 10}`, // 最近40年加上未来10年
  onClose: () => {
    emit("close");
  },
};

const onClear = (el) => {
  el.stopPropagation();
  nextTick(() => {
    emit("update:modelValue", "");
    emit("click:clear", el);
  });
};

const vuetifyTheme = useTheme();
const vuetifyThemesName = Object.keys(vuetifyTheme.themes.value);

// Themes class added to flat-picker component for light and dark support
const updateThemeClassInCalendar = () => {
  // Note: Flatpickr doesn't render its instance on mobile and device simulator
  if (!refFlatPicker.value.fp.calendarContainer) return;
  vuetifyThemesName.forEach((t) => {
    refFlatPicker.value.fp.calendarContainer.classList.remove(`v-theme--${t}`);
  });
  refFlatPicker.value.fp.calendarContainer.classList.add(
    `v-theme--${vuetifyTheme.global.name.value}`
  );
};

watch(() => configStore.theme, updateThemeClassInCalendar);

// Watch time picker open event
watch(isCalendarOpen, (newVal) => {
  if (newVal) {
    // Update immediately when first opened
    requestAnimationFrame(() => {
      ensureTimePickerVisible();
      nextTick(() => {
        updateCalendarWidth();
      });
    });
  }
});

// Add calendar mount event handling
const handleCalendarReady = () => {
  if (refFlatPicker.value?.fp) {
    const { fp } = refFlatPicker.value;

    // Add event when calendar opens
    fp.config.onOpen.push(() => {
      setTimeout(() => {
        ensureTimePickerVisible();
        enhanceYearDropdown(); // 添加年份下拉增强
      }, 50); // Add small delay to ensure DOM is fully updated
    });
  }
};

// 增强年份下拉选择功能
const enhanceYearDropdown = () => {
  if (!refFlatPicker.value?.fp?.calendarContainer) return;
  
  const calendar = refFlatPicker.value.fp.calendarContainer;
  const yearInput = calendar.querySelector('.numInput.cur-year');
  
  if (yearInput) {
    // 确保年份输入框可点击
    yearInput.style.cursor = 'pointer';
    yearInput.readOnly = true; // 禁止手动输入
    
    // 创建年份下拉菜单
    const currentYear = new Date().getFullYear();
    const minYear = currentYear - 40;
    const maxYear = currentYear + 10;
    
    // 移除可能存在的旧下拉菜单
    const existingDropdown = calendar.querySelector('.year-dropdown');
    if (existingDropdown) {
      existingDropdown.remove();
    }
    
    // 创建新的下拉菜单
    const yearDropdown = document.createElement('select');
    yearDropdown.className = 'flatpickr-monthDropdown-months year-dropdown';
    
    // 添加年份选项
    for (let year = maxYear; year >= minYear; year--) {
      const option = document.createElement('option');
      option.value = year;
      option.textContent = year;
      option.className = 'flatpickr-monthDropdown-month';
      yearDropdown.appendChild(option);
    }
    
    // 设置当前年份为选中状态
    yearDropdown.value = currentYear;
    
    // 替换原有的年份输入框
    const yearInputParent = yearInput.parentNode;
    yearInputParent.replaceChild(yearDropdown, yearInput);
    
    // 添加下拉菜单选择事件
    yearDropdown.addEventListener('change', (e) => {
      const selectedYear = parseInt(e.target.value);
      if (!isNaN(selectedYear) && refFlatPicker.value?.fp) {
        const date = new Date(refFlatPicker.value.fp.selectedDates[0] || new Date());
        date.setFullYear(selectedYear);
        refFlatPicker.value.fp.setDate(date);
      }
    });
  }
};

// Set up after component mounting
onMounted(() => {
  updateThemeClassInCalendar();
  updateCalendarWidth();
  window.addEventListener("resize", updateCalendarWidth);
  window.addEventListener("scroll", updateCalendarWidth);

  // Add delay to wait for flatpickr instance initialization
  setTimeout(() => {
    handleCalendarReady();
    // 确保年份下拉功能正常工作
    nextTick(() => {
      enhanceYearDropdown();
    });
  }, 100);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", updateCalendarWidth);
  window.removeEventListener("scroll", updateCalendarWidth);
});

// Process seconds before time update
const emitModelValue = (val) => {
  if (!enableSeconds.value && val) {
    // If seconds are not editable, ensure seconds are 00
    try {
      const date = new Date(val);
      if (!isNaN(date.getTime())) {
        date.setSeconds(0);
        val = date.toISOString();
      }
    } catch (e) {
      console.error("Date formatting error", e);
    }
  }
  emit("update:modelValue", val);
};

watch(
  () => props,
  () => {
    fieldProps.value = filterFieldProps(props);
    inputProps.value = VInput.filterProps(props);
  },
  {
    deep: true,
    immediate: true,
  }
);

const elementId = computed(() => {
  const _elementIdToken = fieldProps.value.id || fieldProps.value.label;

  return _elementIdToken
    ? `app-picker-field-${_elementIdToken}-${Math.random()
        .toString(36)
        .slice(2, 7)}`
    : undefined;
});

// Calculate and update calendar panel width and position
const updateCalendarWidth = () => {
  if (!pickerRef.value || !refFlatPicker.value?.fp?.calendarContainer) return;

  const inputElement = pickerRef.value.querySelector(".v-field");
  if (!inputElement) return;

  const inputRect = inputElement.getBoundingClientRect();
  const calendar = refFlatPicker.value.fp.calendarContainer;

  // Set width
  calendar.style.width = `${inputRect.width}px`;

  // Set position
  calendar.style.position = "fixed";
  
  // Add spacing with input box
  const verticalGap = 10; // Vertical spacing
  
  // Check if exceeding viewport bottom
  const calendarHeight = calendar.offsetHeight;
  const viewportHeight = window.innerHeight;
  
  if (inputRect.bottom + verticalGap + calendarHeight > viewportHeight) {
    // If exceeding viewport bottom, display above input box to avoid overlapping
    calendar.style.top = `${Math.max(0, inputRect.top - calendarHeight - verticalGap)}px`;
  } else {
    // Normal display below input box
    calendar.style.top = `${inputRect.bottom + verticalGap}px`;
  }
  
  calendar.style.left = `${inputRect.left}px`;
  calendar.style.right = "auto";
  calendar.style.transform = "none";
};

// Ensure time picker is immediately displayed
const ensureTimePickerVisible = () => {
  if (!refFlatPicker.value?.fp) return;

  const { fp } = refFlatPicker.value;
  
  // If time selection is disabled, hide time picker
  if (fp.config.enableTime === false) {
    if (fp.calendarContainer) {
      const timeContainer = fp.calendarContainer.querySelector(".flatpickr-time");
      if (timeContainer) {
        timeContainer.style.display = "none";
      }
      
      const timeLabels = fp.calendarContainer.querySelector(".time-labels");
      if (timeLabels) {
        timeLabels.style.display = "none";
      }
    }
    return;
  }

  // Below is the original time picker display logic
  // Force time picker display
  if (
    fp.calendarContainer &&
    !fp.calendarContainer
      .querySelector(".flatpickr-time")
      .classList.contains("hasTime")
  ) {
    fp.calendarContainer
      .querySelector(".flatpickr-time")
      .classList.add("hasTime");
    fp.calendarContainer.classList.add("hasTime");
  }

  // Add clock icon
  const timeContainer = fp.calendarContainer.querySelector(".flatpickr-time");
  if (timeContainer && !timeContainer.querySelector(".time-icon")) {
    const clockIcon = document.createElement("div");
    clockIcon.className = "time-icon";
    clockIcon.innerHTML = '<i class="mdi mdi-clock-time-three-outline"></i>';
    timeContainer.insertBefore(clockIcon, timeContainer.firstChild);
  }

  // Add label container
  if (
    fp.calendarContainer &&
    !fp.calendarContainer.querySelector(".time-labels")
  ) {
    const timeContainer = fp.calendarContainer.querySelector(".flatpickr-time");
    const labelsContainer = document.createElement("div");
    labelsContainer.className = "time-labels";

    // Get input fields
    const hourInput = fp.calendarContainer.querySelector(".flatpickr-hour");
    const minuteInput = fp.calendarContainer.querySelector(".flatpickr-minute");
    const secondInput = fp.calendarContainer.querySelector(".flatpickr-second");

    if (hourInput && minuteInput) {
      // Create labels
      const hourLabel = document.createElement("div");
      hourLabel.className = "time-label";
      hourLabel.textContent = "Hours";

      const minuteLabel = document.createElement("div");
      minuteLabel.className = "time-label";
      minuteLabel.textContent = "Minutes";

      // Insert labels
      labelsContainer.appendChild(hourLabel);
      labelsContainer.appendChild(minuteLabel);

      // Always add second label if second input exists
      if (secondInput) {
        const secondLabel = document.createElement("div");
        secondLabel.className = "time-label";
        secondLabel.textContent = "Second";
        labelsContainer.appendChild(secondLabel);
      }

      // Insert label container before time picker
      timeContainer.parentNode.insertBefore(labelsContainer, timeContainer);
    }
  }

  // Ensure seconds input is displayed but editable based on enableSeconds setting
  if (fp.calendarContainer) {
    // Force show seconds input
    fp.calendarContainer.querySelector(".flatpickr-time").classList.add("hasSeconds");
    
    const secondInput = fp.calendarContainer.querySelector(".flatpickr-second");
    if (secondInput) {
      // Ensure seconds input is visible
      secondInput.parentNode.style.display = "";
      
      // Ensure seconds separator is visible
      const separators = fp.calendarContainer.querySelectorAll(".flatpickr-time-separator");
      if (separators.length > 1) {
        separators[1].style.display = "";
      }
      
      if (!enableSeconds.value) {
        // When enableSeconds is false, seconds are displayed but not editable
        secondInput.disabled = true;
        secondInput.parentNode.classList.add("disabled");
        
        // Set seconds to 00
        secondInput.value = "00";
        
        // Hide up/down arrows
        const arrowUp = secondInput.parentNode.querySelector(".arrowUp");
        const arrowDown = secondInput.parentNode.querySelector(".arrowDown");
        if (arrowUp) arrowUp.style.display = "none";
        if (arrowDown) arrowDown.style.display = "none";
      } else {
        // When enableSeconds is true, seconds are editable
        secondInput.disabled = false;
        secondInput.parentNode.classList.remove("disabled");
        
        // Show up/down arrows
        const arrowUp = secondInput.parentNode.querySelector(".arrowUp");
        const arrowDown = secondInput.parentNode.querySelector(".arrowDown");
        if (arrowUp) arrowUp.style.display = "";
        if (arrowDown) arrowDown.style.display = "";
      }
    }
  }
};
</script>

<template>
  <div class="app-picker-field" ref="pickerRef">
    <!-- v-input -->
    <VLabel
      v-if="fieldProps.label"
      class="mb-1 text-body-2"
      :for="elementId"
      :text="fieldProps.label"
    />

    <VInput
      v-bind="{ ...inputProps, ...rootAttrs }"
      :model-value="modelValue"
      :hide-details="props.hideDetails"
      :class="[
        {
          'v-text-field--prefixed': props.prefix,
          'v-text-field--suffixed': props.suffix,
          'v-text-field--flush-details': ['plain', 'underlined'].includes(
            props.variant
          ),
        },
        props.class,
      ]"
      class="position-relative v-text-field"
      :style="props.style"
    >
      <template
        #default="{ id, isDirty, isValid, isDisabled, isReadonly, validate }"
      >
        <!-- v-field -->
        <VField
          v-bind="{ ...fieldProps, label: undefined }"
          :id="id.value"
          role="textbox"
          :active="focused || isDirty.value || isCalendarOpen"
          :focused="focused || isCalendarOpen"
          :dirty="isDirty.value || props.dirty"
          :error="isValid.value === false || props.error"
          :disabled="isDisabled.value"
          clearable
          clear-icon="tabler-x"
          @click:clear="onClear"
        >
          <template #default="{ props: vFieldProps }">
            <div v-bind="vFieldProps">
              <!-- flat-picker  -->
              <FlatPickr
                v-if="!isInlinePicker"
                v-bind="compAttrs"
                ref="refFlatPicker"
                :model-value="modelValue"
                :placeholder="props.placeholder"
                :readonly="isReadonly.value"
                class="flat-picker-custom-style h-100 w-100"
                :disabled="isReadonly.value"
                @on-open="isCalendarOpen = true"
                @on-close="
                  isCalendarOpen = false;
                  validate();
                  emit('close');
                "
                @update:model-value="emitModelValue"
              />

              <!-- simple input for inline prop -->
              <input
                v-if="isInlinePicker"
                :value="modelValue"
                :placeholder="props.placeholder"
                :readonly="isReadonly.value"
                class="flat-picker-custom-style h-100 w-100"
                type="text"
              />
            </div>
          </template>
        </VField>
      </template>
    </VInput>

    <!-- flat picker for inline props -->
    <FlatPickr
      v-if="isInlinePicker"
      v-bind="compAttrs"
      ref="refFlatPicker"
      :model-value="modelValue"
      @update:model-value="emitModelValue"
      @on-open="isCalendarOpen = true"
      @on-close="
        isCalendarOpen = false;
        emit('close');
      "
    />
  </div>
</template>

<style lang="scss">
@use "@styles/scss/template/mixins" as templateMixins;

/* stylelint-disable no-descending-specificity */
@use "flatpickr/dist/flatpickr.css";
@use "@styles/scss/base/mixins";

.flat-picker-custom-style {
  position: absolute;
  color: inherit;
  inline-size: 100%;
  inset: 0;
  outline: none;
  padding-block: 0;
  padding-inline: var(--v-field-padding-start);
}

$heading-color: rgba(
  var(--v-theme-on-background),
  var(--v-high-emphasis-opacity)
);
$body-color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));
$disabled-color: rgba(var(--v-theme-on-background), var(--v-disabled-opacity));

// hide the input when your picker is inline
input[altinputclass="inlinePicker"] {
  display: none;
}

.flatpickr-time input.flatpickr-hour {
  font-weight: 400;
}

.flatpickr-calendar {
  @include mixins.elevation(6);
  background-color: rgb(var(--v-theme-surface));

  &.open {
    z-index: 2401;
    display: inline-block !important;
    animation: none !important;
  }

  &.arrowTop {
    margin-top: 0;
  }

  &.arrowBottom {
    margin-top: 0;
  }

  // Remove default positioning and arrows
  &::before,
  &::after {
    display: none;
  }

  .flatpickr-rContainer {
    width: 100%;

    .flatpickr-weekdays {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 8px 16px;
      margin: 0;
      background: transparent;

      .flatpickr-weekday {
        flex: 1;
        font-size: 0.8125rem;
        font-weight: 400;
        color: rgba(var(--v-theme-on-surface), 0.7);
        text-align: center;
        text-transform: uppercase;
        padding: 0;
        margin: 0;
        background: transparent;
        height: 28px;
        line-height: 28px;
        float: none;
        width: auto;
      }
    }

    .flatpickr-days {
      width: 100%;
      padding: 0 16px;
      margin: 0;
      min-width: 100%;

      .dayContainer {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        padding: 0;
        width: 100%;
        min-width: 100%;
        max-width: 100%;
        justify-content: stretch;

        .flatpickr-day {
          margin: 0 !important;
          width: 100% !important;
        }
      }
    }
  }

  .flatpickr-day {
    color: $body-color;
    max-width: 100% !important;

    &.today {
      &:not(.selected) {
        border: none !important;
        background: rgba(var(--v-theme-primary), 0.24);
        color: rgb(var(--v-theme-primary));
      }

      &:hover {
        border: none !important;
        background: rgba(var(--v-theme-primary), 0.24);
        color: rgb(var(--v-theme-primary));
      }
    }

    &.selected,
    &.selected:hover {
      border-color: rgb(var(--v-theme-primary));
      background: rgb(var(--v-theme-primary));
      color: rgb(var(--v-theme-on-primary));

      @include templateMixins.custom-elevation(var(--v-theme-primary), "sm");
    }

    &.inRange,
    &.inRange:hover {
      border: none;
      background: rgba(
        var(--v-theme-primary),
        var(--v-activated-opacity)
      ) !important;
      box-shadow: none !important;
      color: rgb(var(--v-theme-primary));
    }

    &.startRange {
      @include templateMixins.custom-elevation(var(--v-theme-primary), "sm");
    }

    &.endRange {
      @include templateMixins.custom-elevation(var(--v-theme-primary), "sm");
    }

    &.startRange,
    &.endRange,
    &.startRange:hover,
    &.endRange:hover {
      border-color: rgb(var(--v-theme-primary));
      background: rgb(var(--v-theme-primary));
      color: rgb(var(--v-theme-on-primary));
    }

    &.selected.startRange + .endRange:not(:nth-child(7n + 1)),
    &.startRange.startRange + .endRange:not(:nth-child(7n + 1)),
    &.endRange.startRange + .endRange:not(:nth-child(7n + 1)) {
      box-shadow: -10px 0 0 rgb(var(--v-theme-primary));
    }

    &.flatpickr-disabled,
    &.prevMonthDay:not(.startRange, .inRange),
    &.nextMonthDay:not(.endRange, .inRange) {
      opacity: var(--v-disabled-opacity);
    }

    &:hover {
      border-color: transparent;
      background: rgba(var(--v-theme-on-surface), 0.06);
    }
  }

  .flatpickr-weekday {
    color: $heading-color;
    font-size: 0.8125rem;
    font-weight: 400;
    inline-size: auto !important;
    line-height: 1.25rem;
  }

  .flatpickr-days {
    inline-size: 16.875rem;
  }

  .flatpickr-months {
    .flatpickr-prev-month,
    .flatpickr-next-month {
      color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
      fill: $body-color;

      &:hover {
        color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
      }

      &:hover i,
      &:hover svg {
        fill: $body-color;
      }
    }
  }

  .flatpickr-current-month span.cur-month {
    font-weight: 300;
  }

  &.hasTime.open {
    .flatpickr-innerContainer + .flatpickr-time {
      block-size: auto;
    }

    .flatpickr-time {
      border-block-start: none;
    }

    .flatpickr-hour,
    .flatpickr-minute,
    .flatpickr-am-pm {
      font-size: 0.9375rem;
    }
  }
}

.v-theme--dark .flatpickr-calendar {
  box-shadow: 0 3px 14px 0 rgb(15 20 34 / 38%);
}

// Time picker hover & focus bg color
.flatpickr-time input:hover,
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time input:focus,
.flatpickr-time .flatpickr-am-pm:focus {
  background: transparent;
}

// Time picker
.flatpickr-time {
  display: flex;
  align-items: center;
  height: auto;
  line-height: 1;
  padding: 8px 0;
  position: relative;

  .numInputWrapper {
    flex: 1;
    height: 32px;
    position: relative;
    &:hover {
      background: rgba(var(--v-theme-on-surface), 0.04);
    }

    input {
      height: 100%;
      width: 100%;
      -webkit-appearance: textfield;
      -moz-appearance: textfield;
      appearance: textfield;
      text-align: center;
      background: #2d2d2d;
      border-radius: 4px;
      color: #ffffff;
      border: 1px solid rgba(255, 255, 255, 0.1);
      font-size: 16px;
      padding: 0;

      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &:focus {
        outline: none;
        border-color: rgb(var(--v-theme-primary));
      }

      &:disabled {
        background: #222222;
        opacity: 0.7;
        cursor: not-allowed;
      }
    }

    span {
      &.arrowUp,
      &.arrowDown {
        position: absolute;
        right: 0;
        width: 20px;
        height: 20px;
        opacity: 0.6;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          opacity: 1;
          background: rgba(var(--v-theme-on-surface), 0.1);
        }
      }

      &.arrowUp {
        top: 0;

        &::after {
          content: "";
          border-bottom: 5px solid rgb(var(--v-border-color));
          border-left: 5px solid transparent;
          border-right: 5px solid transparent;
        }
      }

      &.arrowDown {
        bottom: 0;

        &::after {
          content: "";
          border-top: 5px solid rgb(var(--v-border-color));
          border-left: 5px solid transparent;
          border-right: 5px solid transparent;
        }
      }
    }
  }

  .flatpickr-time-separator {
    width: 20px;
    text-align: center;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .time-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: 8px;

    i {
      font-size: 24px;
      color: rgba(var(--v-theme-on-surface), 0.7);
    }
  }
}

// Add space above time picker
.flatpickr-calendar.hasTime.open .flatpickr-time {
  margin-top: 0;
  padding: 8px 12px;
}

// Style when seconds are not displayed
.flatpickr-time:not(.hasSeconds) .numInputWrapper:nth-child(2) {
  margin-right: 0;
}

// Style when seconds are not editable
.flatpickr-time .numInputWrapper:nth-child(5) {
  &.disabled {
    input {
      background: #222222;
      opacity: 0.7;
      cursor: not-allowed;
    }

    span.arrowUp,
    span.arrowDown {
      display: none; // Completely hide arrows
    }
  }
}

// Time picker input box style
.flatpickr-time .numInputWrapper {
  input {
    &:disabled {
      background: #222222;
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
}

//  Added bg color for flatpickr input only as it has default readonly attribute
.flatpickr-input[readonly],
.flatpickr-input ~ .form-control[readonly],
.flatpickr-human-friendly[readonly] {
  background-color: inherit;
}

// week sections
.flatpickr-weekdays {
  margin-block: 0.375rem;
}

// Month and year section
.flatpickr-current-month {
  .flatpickr-monthDropdown-months {
    appearance: none;
  }

  .flatpickr-monthDropdown-months,
  .numInputWrapper {
    padding: 2px;
    border-radius: 4px;
    color: $heading-color;
    font-size: 0.9375rem;
    font-weight: 400;
    line-height: 1.375rem;
    transition: all 0.15s ease-out;

    span {
      display: none;
    }

    .flatpickr-monthDropdown-month {
      background-color: rgb(var(--v-theme-surface));
    }

    .numInput.cur-year {
      font-weight: 400;
    }
  }
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover {
  color: $body-color;
}

.flatpickr-months {
  padding-block: 0.75rem;
  padding-inline: 1rem;

  .flatpickr-prev-month,
  .flatpickr-next-month {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border-radius: 5rem;
    background: rgba(var(--v-theme-on-surface), var(--v-selected-opacity));
    block-size: 1.875rem;
    inline-size: 1.875rem;
    inset-block-start: 15px !important;

    &.flatpickr-disabled {
      display: inline;
      opacity: var(--v-disabled-opacity);
      pointer-events: none;
    }
  }

  .flatpickr-next-month {
    inset-inline-end: 1.05rem !important;
  }

  .flatpickr-prev-month {
    /* stylelint-disable-next-line liberty/use-logical-spec */
    right: 3.65rem;
    left: unset !important;
  }

  .flatpickr-month {
    display: flex;
    align-items: center;
    block-size: 2.125rem;

    .flatpickr-current-month {
      display: flex;
      align-items: center;
      padding: 0;
      block-size: 1.75rem;
      inset-inline-start: 0;
      text-align: start;
    }
  }
}

// Ensure calendar container is not covered by other elements
.app-picker-field {
  position: relative;
}

.flatpickr-innerContainer,
.flatpickr-rContainer {
  min-width: 100% !important;
  max-width: 100% !important;
}

// Ensure time picker is displayed
.flatpickr-calendar.hasTime .flatpickr-time {
  display: flex !important;
  visibility: visible !important;
  height: auto !important;
  margin-top: 10px;
  padding: 8px 12px; // Add left-right padding
}

// Time label styles
.time-labels {
  display: flex;
  padding: 8px 12px 0 60px;
  border-top: 1px solid rgba(var(--v-border-color), 0.15) !important;
  align-items: center;
  justify-content: left;
  gap: 20px;
}

.time-label {
  flex: 1;
  text-align: left;
  font-size: 12px;
  color: rgba(var(--v-theme-on-surface), 0.7);
}

// Remove previous pseudo-element labels
.flatpickr-time .numInputWrapper {
  &:first-child::before,
  &:nth-child(2)::before,
  &:nth-child(3)::before {
    content: none;
  }
}

// 年份下拉框样式
.flatpickr-current-month {
  .year-dropdown.flatpickr-monthDropdown-months {
    padding: 2px;
    border-radius: 4px;
    color: $heading-color;
    font-size: 0.9375rem;
    font-weight: 400;
    line-height: 1.375rem;
    transition: all 0.15s ease-out;
    min-width: 70px;
    text-align: center;
    background-color: rgb(var(--v-theme-surface));
    
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
      background-color: transparent;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 3px;
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
    
    /* 选项样式 */
    option {
      background-color: rgb(var(--v-theme-surface));
      color: $heading-color;
      padding: 4px 8px;
      
      &:hover, &:focus {
        background-color: rgba(var(--v-theme-on-surface), 0.08);
      }
    }
  }
}

/* 确保下拉列表背景色正确 */
select.flatpickr-monthDropdown-months {
  option {
    background-color: rgb(var(--v-theme-surface)) !important;
  }
}
</style>
