<script setup>
import { ref } from "vue";
import { isTextOverflow } from "@/utils/helpers";
import { dateUtil } from "@/utils/day";
import { useTable } from "@/hooks/useTable";
import { useMerchant } from "@/business/payment-partner-commission/useMerchant";
import { PAGINATION_OPTIONS as paginationOptions } from "@/utils/constants";
import { useRouter } from "vue-router";

const router = useRouter();

const headers = ref([
  { title: "No", key: "no", sortable: false },
  { title: "Merchant Name", key: "merchantName" },
  { title: "Commission Scheme Name", key: "name" },
  { title: "Actions", key: "actions", sortable: false },
]);

const { merchantList } = useMerchant();


const {
  searchQuery,
  page,
  itemsPerPage,
  items,
  total,
  isLoading,
  handlePageChange,
  handleItemsPerPageChange,
  handleSearch,
  getQueryParams,
  handleSortChange,
  loadData,
} = useTable({
  fetchData() {
    return $api("api/admin-api/v1/commission-scheme/page-query", {
      method: "POST",
      data: getQueryParams(),
    });
  },
  rowKey: "id",
  createDefaultQuery: () => ({
    orderItemList: null,
    merchantId: null,
    commissionSchemeNameLike: null,
    page: 1,
    size: 10,
  }),
});

const handleAdd = () => {
  console.log("handleAdd");
  router.push({
    path: "/payment-partner-commission/commission-scheme-managers/add",
  });
};

const handleEdit = (item) => {
  console.log("handleEdit", item);
};
</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            Commission Scheme Managers
            <VSpacer />
            <div class="d-flex align-center gap-4">
              <VBtn @click="handleAdd"> CREATE </VBtn>
            </div>
          </div>
        </VCardTitle>
      </VCardItem>

      <VCardText class="px-0">
        <VRow>
          <!-- 👉 Search Commission Scheme Name -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField
              v-model="searchQuery.commissionSchemeNameLike"
              placeholder="Search Commission Scheme Name"
              clearable
              @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Search Merchant Name -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete
              v-model="searchQuery.merchantId"
              placeholder="Select Merchant Name"
              :items="merchantList"
              clearable
              clear-icon="tabler-x"
              multiple
              filterable
              @update:model-value="handleSearch"
            >
              <template #selection="{ item, index }">
                <span v-if="index === 0">
                  {{ item.title }}
                </span>
                <span
                  v-else-if="index === 1"
                  class="text-caption align-self-center text-primary font-weight-medium ml-2"
                >
                  (+{{ searchQuery.merchantId.length - 1 }})
                </span>
              </template>
            </AppAutocomplete>
          </VCol>
        </VRow>
      </VCardText>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer
        :items="items"
        item-value="id"
        :items-length="total"
        :headers="headers"
        :loading="isLoading"
        class="text-no-wrap"
        @update:sort-by="handleSortChange"
      >
        <!-- Actions -->
        <template #[`item.actions`]="{ item }">
          <IconBtn
            @click="handleEdit(item)"
            v-tooltip="'Edit Commission Scheme Manager'"
          >
            <VIcon icon="tabler-pencil" class="hover:text-primary" />
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination
            :page="page"
            :items-per-page="itemsPerPage"
            :total-items="total"
            @update:page="handlePageChange"
          >
            <div class="d-flex gap-3">
              <AppSelect
                :model-value="itemsPerPage"
                :items="paginationOptions"
                @update:model-value="handleItemsPerPageChange"
              />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
  </section>
</template>

<style scoped></style>
