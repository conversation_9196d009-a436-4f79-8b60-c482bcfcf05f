<script setup>
import { VTreeview } from 'vuetify/labs/VTreeview'

const props = defineProps({
  items: {
    type: Array,
    required: true,
  },
  itemValue: {
    type: String,
    default: 'id',
  },
  modelValue: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  maxHeight: {
    type: [Number, String],
    default: 'auto',
  },
})

const emit = defineEmits(['update:modelValue'])

const selected = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// Set initial expanded state to all parent node IDs
const expanded = ref([])

onMounted(() => {
  // Initialize to expand all parent nodes
  const parentIds = props.items.map(item => item.id)
  expanded.value = parentIds
})

// Check if node is selected
const isSelected = (item) => {
  if (!item.children) {
    return selected.value.includes(item.id)
  }
  return item.children.every(child => selected.value.includes(child.id)) && selected.value.includes(item.id)
}

// Check if node is in indeterminate state
const isIndeterminate = (item) => {
  if (!item.children) return false

  const selectedChildrenCount = item.children.filter(child =>
    selected.value.includes(child.id)
  ).length

  if (selectedChildrenCount === item.children.length) {
    if (!selected.value.includes(item.id)) {
      selected.value = [...selected.value, item.id]
    }
    return false
  }

  return selectedChildrenCount > 0
}

// Find parent node
const findParentNode = (items, childId, parent = null) => {
  for (const item of items) {
    if (item.children) {
      if (item.children.some(child => child.id === childId)) {
        return item
      }
      const found = findParentNode(item.children, childId, item)
      if (found) return found
    }
  }
  return parent
}

// Handle selection event
const handleSelect = (item, event) => {
  event?.stopPropagation()

  const itemIds = []

  if (item.children) {
    const isItemSelected = isSelected(item)

    const collectIds = (node) => {
      itemIds.push(node.id)
      if (node.children) {
        node.children.forEach(child => collectIds(child))
      }
    }
    collectIds(item)

    if (!isItemSelected) {
      selected.value = [...new Set([...selected.value, ...itemIds])]
    } else {
      selected.value = selected.value.filter(id => !itemIds.includes(id))
    }
  } else {
    const index = selected.value.indexOf(item.id)
    if (index === -1) {
      selected.value.push(item.id)

      const parent = findParentNode(props.items, item.id)
      if (parent && parent.children.every(child => selected.value.includes(child.id))) {
        selected.value.push(parent.id)
      }
    } else {
      selected.value.splice(index, 1)

      const parent = findParentNode(props.items, item.id)
      if (parent) {
        const parentIndex = selected.value.indexOf(parent.id)
        if (parentIndex !== -1) {
          selected.value.splice(parentIndex, 1)
        }
      }
    }
  }
}

// Get all selected leaf nodes data
const getSelectedLeafNodes = () => {
  const leafNodes = []
  
  const findLeafNodes = (items) => {
    items.forEach(item => {
      if (!item.children && selected.value.includes(item.id)) {
        leafNodes.push(item)
      } else if (item.children) {
        findLeafNodes(item.children)
      }
    })
  }
  
  findLeafNodes(props.items)
  return leafNodes
}

// If you need to export this method to the parent component, you can use defineExpose
defineExpose({
  getSelectedLeafNodes
})
</script>

<template>
  <VTreeview v-model:expanded="expanded" :items="items" :item-value="itemValue" open-all
    :style="{ maxHeight: `${maxHeight}px` }" >
    <template #prepend="{ item }">
      <div class="d-flex align-center">
        <div>
          <VCheckbox :readonly="disabled" :model-value="isSelected(item)" @click.stop :indeterminate="isIndeterminate(item)" hide-details
            density="compact" @update:model-value="(e) => handleSelect(item, $event)" />
        </div>
        <span class="text-white">
          {{ item.name }}
        </span>
      </div>
    </template>
  </VTreeview>
</template>

<style lang="scss" scoped>
:deep(.v-treeview) {
  overflow-y: auto;
  flex: 1;

  .v-treeview-group {
    margin-left: 0;

    &:has(.v-selection-control--dirty) {
      background-color: #303030;

      .v-treeview-node__root {
        background-color: #303030;
      }
    }

    &:has(.v-checkbox--indeterminate) {
      background-color: #303030;

      .v-treeview-node__root {
        background-color: #303030;
      }
    }
  }

  .v-treeview-node__root {
    min-height: 32px;
    margin-left: 0;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    pointer-events: none;
  }

  .v-list-group__items {
    margin-left: 32px;
    position: relative;
  }

  .v-list-item-action {
    display: none;
  }

  &.v-list {
    background: transparent !important;
    padding: 0;

    .v-list-item {
      padding: 8px 0 !important;
    }
  }

  .v-treeview-node__toggle {
    pointer-events: auto;
  }
}

:deep(.v-selection-control) {
  justify-content: flex-end;
  flex-flow: row-reverse;
}
</style>