
/**
 * Get active role list
 * 
 * This function filters out all roles with statusId of 1 (active roles) from the given role list
 * If the input role list is empty or undefined, returns an empty array
 * 
 * @param {Array} roleList - Role list, each role should contain statusId field to indicate role status
 * @returns {Array} Returns an array containing only active roles (statusId=1), returns empty array if input is empty or invalid
 */
export const getActiveRoleList = roleList => {
  // Check if role list exists, return empty array if not
  if (!roleList) return []
  // Use filter method to get all roles with statusId=1
  return roleList.filter(item => item.statusId === 1)
}


/**
 * Provides reactive state for user detail data.
 * @param {string} id - The account ID of the user to load.
 * @returns {Object} with one property:
 *   - userData: reactive ref to user data
 */
export const useUserDetail = (id) => {
  const userData = ref({})
  const loadData = async () => {
    try { 
      const res = await $api(`/api/admin-api/v1/account?id=${id}`, {
        method: 'GET'
      })
      userData.value = res.data
    } catch (error) {
      console.error('Error loading user detail:', error)
      userData.value = {}
    }
  }
  onMounted(loadData)
  return {
    userData,
  }
}

export const getRoleList = userGroupList => {
  if (!userGroupList) return []
  const roles = []
  userGroupList.forEach(item => {
    if (item.roleList) {
      const activeRoleList = getActiveRoleList(item.roleList)
      roles.push(...activeRoleList)
    }
  })
  return roles
}

