#!/usr/bin/env bash
#shellcheck disable=SC1091
set -eu

#jenkins define
#ENV=zgd

echo ENV "$ENV"
echo "jq version $(jq --version)"
echo "aws version $(aws --version)"
pwd

declare -x DEPLOY_SCRIPT=${1-'deploy.sh'}

echo "DEPLOY_SCRIPT $DEPLOY_SCRIPT"

if [[ ! -f "config/$DEPLOY_SCRIPT" ]]; then echo missing deploy script; exit 1; fi

chmod +x "./config/$DEPLOY_SCRIPT"
source "./config/$DEPLOY_SCRIPT"

if [[ $BUILD == MESH ]];then  cp ./deploy/mesh/* ./deploy ; fi 

if [[ -d config/deploy ]];then cp ./config/deploy/* ./deploy ; fi 

chmod +x ./deploy/define.sh
source ./deploy/define.sh

deploy_override

chmod +x ./deploy/common.sh
source ./deploy/common.sh

case $BUILD in
  PREBUILD)
  prebuild
  ;;

  LAMBDA)
  chmod +x ./deploy/lambda.sh
  source ./deploy/lambda.sh
  ;;

  LAMBDA_PY)
  chmod +x ./deploy/lambda-py.sh
  source ./deploy/lambda-py.sh
  ;;

  LAMBDA_CS)
  chmod +x ./deploy/lambda-cs.sh
  source ./deploy/lambda-cs.sh
  ;;

  LAMBDA_NODE_LAYER)
  chmod +x ./deploy/lambda-node-layer.sh
  source ./deploy/lambda-node-layer.sh
  ;;

  FARGATE)
  chmod +x ./deploy/fargate.sh
  source ./deploy/fargate.sh 
  ;;

  MESH)
  chmod +x ./deploy/appmesh.sh
  source ./deploy/appmesh.sh
  ;;

esac




