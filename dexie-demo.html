<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dexie.js Add/Put Demo</title>
    <script src="https://unpkg.com/dexie@latest/dist/dexie.js"></script>
</head>
<body>
    <h1>Dexie.js 数据保存示例</h1>
    <p>请打开浏览器控制台 (F12) 查看输出。</p>
    <button onclick="addScenario()">添加新的场景配置 (自动ID)</button>
    <button onclick="addScenarioWithManualId()">添加新的场景配置 (手动指定ID)</button>
    <button onclick="updateScenario()">更新 ID 为 1 的场景配置</button>
    <button onclick="getAllScenarios()">获取所有场景配置</button>
    <button onclick="clearScenarios()">清空所有场景配置</button>
    <button onclick="deleteDatabase()">删除数据库</button>

    <script>
        // 1. 定义 Dexie 数据库和表结构
        // 这里假设您的数据库名为 'LowCodePlatformDB'，并且有一个 'scenarioConfigs' 表
        // '++id' 表示 id 是主键且自动递增。
        // 'scenarioId' 是一个索引字段，可以用于查询，但不是主键。
        const db = new Dexie("LowCodePlatformDB");
        db.version(1).stores({
            scenarioConfigs: '++id, scenarioId, createdAt, scenario, icon, color'
            // 如果 scenarioId 是唯一主键且非自动递增，则写为 'scenarioId, createdAt, ...'
            // 如果 scenarioId 是主键且自动递增，但字段名就是 scenarioId，则写为 '++scenarioId, createdAt, ...'
        });

        // 示例数据
        let scenarioCounter = 0;

        // 2. 添加新的场景配置 (使用自动递增的主键ID)
        async function addScenario() {
            try {
                scenarioCounter++;
                const newScenario = {
                    scenarioId: `scenario-${Date.now()}-${scenarioCounter}`, // 通常 scenarioId 也是唯一的，这里我们为它生成一个
                    scenario: `我的场景 ${scenarioCounter}`,
                    icon: `icon-${scenarioCounter}.png`,
                    color: `color-${scenarioCounter}`,
                    createdAt: new Date()
                };
                // 当使用 '++id' 时，不需要在数据对象中包含 'id' 属性
                const id = await db.scenarioConfigs.add(newScenario);
                console.log('成功添加场景配置，自动生成的ID:', id, '数据:', newScenario);
            } catch (error) {
                console.error('添加场景配置失败:', error);
            }
        }

        // 3. 添加新的场景配置 (手动指定主键ID，如果您的主键不是自动递增)
        // 假设您的表定义是: scenarioConfigs: 'id, scenarioId, createdAt, ...'
        async function addScenarioWithManualId() {
            try {
                const manualId = Date.now(); // 示例：手动指定一个ID
                const newScenario = {
                    id: manualId, // 手动指定ID
                    scenarioId: `manual-scenario-${manualId}`,
                    scenario: `手动场景 ${manualId}`,
                    icon: `icon-manual.png`,
                    color: `color-manual`,
                    createdAt: new Date()
                };
                // 当主键不是自动递增时，您必须提供主键的值
                const id = await db.scenarioConfigs.add(newScenario);
                console.log('成功添加场景配置 (手动ID)，ID:', id, '数据:', newScenario);
            } catch (error) {
                console.error('添加手动ID场景配置失败:', error);
            }
        }


        // 4. 更新场景配置 (使用put方法，如果存在则更新，不存在则添加)
        // put 方法需要您提供完整的数据对象，包括主键
        async function updateScenario() {
            try {
                const idToUpdate = 1; // 假设我们要更新 ID 为 1 的数据
                const existingScenario = await db.scenarioConfigs.get(idToUpdate);

                if (existingScenario) {
                    const updatedData = {
                        ...existingScenario, // 保留现有数据
                        scenario: '更新后的场景名称',
                        color: 'updated-color',
                        updatedAt: new Date() // 可以添加更新时间
                    };
                    // 使用 put 方法，如果 idToUpdate 存在则更新，否则会尝试添加新数据 (需要包含 id)
                    const id = await db.scenarioConfigs.put(updatedData);
                    console.log('成功更新场景配置，ID:', id, '更新后的数据:', updatedData);
                } else {
                    console.warn(`ID 为 ${idToUpdate} 的场景配置不存在，无法更新。`);
                    // 如果需要，可以在这里添加新的数据
                    // const newScenario = { id: idToUpdate, scenario: '新场景', ... };
                    // await db.scenarioConfigs.put(newScenario);
                }
            } catch (error) {
                console.error('更新场景配置失败:', error);
            }
        }

        // 5. 获取所有场景配置
        async function getAllScenarios() {
            try {
                const allScenarios = await db.scenarioConfigs.toArray();
                console.log('所有场景配置:', allScenarios);
            } catch (error) {
                console.error('获取所有场景配置失败:', error);
            }
        }

        // 6. 清空所有场景配置
        async function clearScenarios() {
            try {
                await db.scenarioConfigs.clear();
                console.log('已清空所有场景配置。');
            } catch (error) {
                console.error('清空场景配置失败:', error);
            }
        }

        // 7. 删除整个数据库
        async function deleteDatabase() {
            try {
                await db.delete();
                console.log('数据库已删除。页面需要刷新才能重新创建。');
            } catch (error) {
                console.error('删除数据库失败:', error);
            }
        }

        // 初始化：打开数据库连接
        db.open().then(() => {
            console.log("IndexedDB 数据库已打开。");
        }).catch(err => {
            console.error("打开数据库失败:", err);
        });

    </script>
</body>
</html> 