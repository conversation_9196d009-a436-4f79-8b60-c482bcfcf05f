// Vertical nav item badge styles

%nav-header-action {
  font-size: 0;
}

%vertical-nav-item-badge {
  z-index: 1;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  margin-inline-end: 0.5rem;
  padding-block: 0.125rem;
  padding-inline: 0.625rem;
}

// This is same as `%vertical-nav-item` except section title is excluded
%vertical-nav-item-interactive {
  // border-radius: 0.375rem;
  block-size: 3rem;
  // margin-block-end: 0.375rem;
}

%vertical-nav-items-icon-after-2nd-level {
  margin-inline: 15px 0.8125rem;
  visibility: visible;
}

// Section title
// ℹ️ Setting height will prevent jerking when text & icon is toggled
%vertical-nav-section-title {
  block-size: 1.25rem;
  font-size: 0.8125rem;
  line-height: 1.125rem;
  text-transform: uppercase;
}

// ℹ️ Icon styling for icon nested inside another nav item (2nd level)
%vertical-nav-items-nested-icon {
  color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
}
