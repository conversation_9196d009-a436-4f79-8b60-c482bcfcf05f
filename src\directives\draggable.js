// directives/draggable.js
import interact from 'interactjs'

export default {
  mounted(el, binding) {
    const options = binding.value || {}
    
    interact(el).draggable({
      // 默认选项
      inertia: true,
      modifiers: [
        interact.modifiers.snap({ targets: [interact.snappers.grid({ x: 10, y: 10 })] })
      ],
      // 合并自定义选项
      ...options
    }).on('dragmove', event => {
      // 触发 Vue 事件
      binding.instance.$emit('drag-move', {
        dx: event.dx,
        dy: event.dy,
        element: el
      })
    })
  },
  
  unmounted(el) {
    interact(el).unset()
  }
}