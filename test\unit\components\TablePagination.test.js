import { beforeEach, describe, expect, it } from 'vitest'

import TablePagination from '@/components/TablePagination.vue'
import { createVuetify } from 'vuetify'
import { mount } from '@vue/test-utils'

describe('TablePagination 组件纯净性测试', () => {
  let vuetify

  beforeEach(() => {
    vuetify = createVuetify()
  })

  it('应该是纯净的props驱动组件，不依赖低代码上下文', () => {
    const wrapper = mount(TablePagination, {
      global: {
        plugins: [vuetify],
      },
      props: {
        page: 1,
        itemsPerPage: 10,
        totalItems: 100
      }
    })

    // 验证组件能正常渲染
    expect(wrapper.exists()).toBe(true)
    
    // 验证组件使用的是props值，而不是注入的低代码上下文
    const pagination = wrapper.findComponent({ name: 'VPagination' })
    expect(pagination.props('modelValue')).toBe(1)
    expect(pagination.props('length')).toBe(10) // Math.ceil(100/10)
  })

  it('应该正确触发page更新事件', async () => {
    const wrapper = mount(TablePagination, {
      global: {
        plugins: [vuetify],
      },
      props: {
        page: 1,
        itemsPerPage: 10,
        totalItems: 100
      }
    })

    const pagination = wrapper.findComponent({ name: 'VPagination' })
    
    // 模拟页码变更
    await pagination.vm.$emit('update:model-value', 2)
    
    // 验证事件被正确触发
    expect(wrapper.emitted('update:page')).toBeTruthy()
    expect(wrapper.emitted('update:page')[0]).toEqual([2])
  })

  it('应该根据props正确计算分页长度', () => {
    const testCases = [
      { totalItems: 100, itemsPerPage: 10, expectedLength: 10 },
      { totalItems: 95, itemsPerPage: 10, expectedLength: 10 },
      { totalItems: 25, itemsPerPage: 20, expectedLength: 2 },
      { totalItems: 0, itemsPerPage: 10, expectedLength: 0 }
    ]

    testCases.forEach(({ totalItems, itemsPerPage, expectedLength }) => {
      const wrapper = mount(TablePagination, {
        global: {
          plugins: [vuetify],
        },
        props: {
          page: 1,
          itemsPerPage,
          totalItems
        }
      })

      const pagination = wrapper.findComponent({ name: 'VPagination' })
      expect(pagination.props('length')).toBe(expectedLength)
    })
  })
}) 