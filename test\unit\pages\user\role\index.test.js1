import { mount } from '@vue/test-utils'
import RoleComponent from '@/pages/user/role/index.vue'
import { ref } from 'vue'
import Vuetify, { createVuetify} from 'vuetify'

// Mocking the $api function used to fetch data
vi.mock('@/api', () => ({
  $api: vi.fn(() => Promise.resolve({ data: [] }))
}))

describe('RoleComponent', () => {
  let wrapper

  beforeEach(() => {
    // Initialize Vuetify for UI components (use createVuetify() for Vuetify 3)
    const vuetify = createVuetify()

    wrapper = mount(RoleComponent, {
      global: {
        plugins: [vuetify], // Pass vuetify here
      },
    })
  })

  it('should render the component properly', async () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('section').exists()).toBe(true)
    expect(wrapper.find('VCard').exists()).toBe(true)
  })

  it('should call loadData on initial mount', async () => {
    const loadDataSpy = vi.spyOn(wrapper.vm, 'loadData')
    await wrapper.vm.$nextTick()

    expect(loadDataSpy).toHaveBeenCalledTimes(1)
  })

  it('should handle the search query correctly', async () => {
    const searchInput = wrapper.findComponent({ name: 'AppTextField' })
    await searchInput.setValue('<EMAIL>')
    
    // Check if the searchQuery has been updated
    expect(wrapper.vm.searchQuery.email).toBe('<EMAIL>')
  })

  it('should update queryParams when page is changed', async () => {
    const initialPage = wrapper.vm.page
    wrapper.vm.page = 2
    await wrapper.vm.$nextTick()

    expect(wrapper.vm.queryParams.page).toBe(2)
  })

  it('should update queryParams when itemsPerPage is changed', async () => {
    const initialSize = wrapper.vm.itemsPerPage
    wrapper.vm.itemsPerPage = 25
    await wrapper.vm.$nextTick()

    expect(wrapper.vm.queryParams.size).toBe(25)
  })

  it('should reset queryParams when refreshData is called', async () => {
    // Mock the API response to simulate data being loaded
    wrapper.vm.queryParams.page = 2
    wrapper.vm.queryParams.size = 50

    await wrapper.vm.refreshData()
    await wrapper.vm.$nextTick()

    expect(wrapper.vm.queryParams.page).toBe(1)
    expect(wrapper.vm.queryParams.size).toBe(10)
  })

  it('should call the editRole method when edit button is clicked', async () => {
    const editSpy = vi.spyOn(wrapper.vm, 'editRole')
    const editButton = wrapper.find('[v-tooltip="Edit User"]')  // Assuming the tooltip is on the edit button
    await editButton.trigger('click')

    expect(editSpy).toHaveBeenCalled()
  })

  it('should render the correct number of rows', async () => {
    // Mocking the response of rolesData
    wrapper.vm.rolesData = { value: { records: [{ id: 1 }, { id: 2 }] } }

    await wrapper.vm.$nextTick()

    const rows = wrapper.findAll('tr')  // Find all rows in the table
    expect(rows.length).toBe(2) // Should match the number of records
  })

  it('should change the variant based on user status', () => {
    const variant = wrapper.vm.resolveUserStatusVariant(1)
    expect(variant).toBe('success')

    const variant2 = wrapper.vm.resolveUserStatusVariant(0)
    expect(variant2).toBe('secondary')

    const variant3 = wrapper.vm.resolveUserStatusVariant(2)
    expect(variant3).toBe('primary')
  })
})
