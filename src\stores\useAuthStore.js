import { clearAllCookie, useCookie } from "@/hooks/useCookie";
import { isDevelopment, treeToList } from "@/utils/helpers";
import { isEqual, uniqWith } from "lodash-es";

import { defineStore } from "pinia";
import { jwtDecode } from "jwt-decode";
import { useRouter } from "vue-router";
import { useStorage } from "@/hooks/useStorage";

/**
 * Process menu icons to ensure consistent format
 * @param {Object} menu - Menu item to process
 * @returns {Object} Processed menu item with formatted icon
 */
const processMenuIcon = (menu) => {
  // Use default icon if none provided
  if (!menu.icon || menu.icon === "NA" || menu.icon === "N/A") {
    menu.icon = { icon: "tabler-circle" };
  } else if (typeof menu.icon === "string") {
    menu.icon = {
      icon: menu.icon,
    };
  }

  // Handle submenus recursively
  if (menu.children?.length) {
    menu.children = menu.children.map((child) => processMenuIcon(child));
  }

  return menu;
};

/**
 * Convert route path to route configuration
 * @param {string} path - Original path
 * @returns {Object|null} Processed route configuration
 */
const normalizeRoutePath = (path) => {
  if (!path || path === "NA" || path === "N/A") return null;

  // Return external links directly
  if (path.startsWith("http")) return { href: path };

  // Process internal route paths
  try {
    // Remove leading and trailing slashes
    if (path.startsWith("/")) {
      
      return {
        to: {
          path
        }
      }
    } else {
      return {
        to: {
          name: path
        }
      }
    }
  } catch (error) {
    console.warn(`Warning: Error processing route path "${path}"`, error);
    return null;
  }
};

/**
 * Convert menu data into navigation tree structure
 * @param {Array} menus - Raw menu data
 * @returns {Array} Processed navigation tree
 */
const getMenuTree = (menus) => {
  if (!menus) return [];

  return menus
    .map((menu) => {
      try {
        const isHref = menu.link && menu.link.startsWith("http");
        const isPath =
          menu.link && menu.link !== "NA" && menu.link !== "N/A" && !isHref;

        // Process route path
        let routeConfig = null;
        if (isPath || isHref) {
          routeConfig = normalizeRoutePath(menu.link);
        }

        // Base menu item
        const menuItem = {
          id: menu.id,
          title: menu.name,
          icon: menu.icon,
          ...(routeConfig || {})
        };

        // Process submenus
        if (menu.children?.length) {
          const children = getMenuTree(menu.children).filter(Boolean);
          if (children.length) {
            menuItem.children = children;
          }
        }

        return menuItem;
      } catch (error) {
        console.error(`Error: Failed to process menu item "${menu.name}":`, error);
        return null;
      }
    })
    .filter(Boolean);
};

/**
 * Auth store for managing authentication state
 */
export const useAuthStore = defineStore("auth", {
  state: () => ({
    token: useCookie("accessToken").value,
    userData: useStorage("userData").value,
    menus: useStorage("menus").value || [],
    permissions: useStorage("permissions").value || [],
    defaultRoute: useStorage("defaultRoute").value || null,
    loading: false,
  }),

  getters: {
    menuTree: (state) => getMenuTree(state.menus),
  },

  actions: {
    /**
     * Check if token is valid
     * @param {string} token - JWT token
     * @returns {boolean} True if token is valid, false otherwise
     */
    isValidToken(token) {
      try {
        if (!token) return false;
        const decoded = jwtDecode(token);
        return decoded.exp > Date.now() / 1000;
      } catch (error) {
        console.error("Invalid token:", error);
        return false;
      }

    },
    /**
     * Attempt automatic login using stored credentials
     * @returns {Promise<boolean>} Login success status
     */
    async autoLogin() {
      try {
        this.loading = true;
        // Get new token
        let query = {}
        if (isDevelopment) {
          query.name = import.meta.env.VITE_USER_NAME
          query.email = import.meta.env.VITE_USER_EMAIL
        }
        const res = await $api("/api/auth/login", {
          method: "GET",
          query
        });

        const token = res.data;
        console.log('获取到的 token:', token);
        
        // Set cookie
        useCookie("accessToken").value = token;
        console.log('设置 cookie 后:', document.cookie);

        if (res.data) {
          this.setToken(res.data);
        }

        // Get and save user info
        await this.fetchUserProfile();

        // Get and save menus
        await this.fetchMenus();

        return true;
      } catch (error) {
        console.error("Auto login failed:", error.message);
        if (process.env.NODE_ENV === "development") {
          this.setToken(import.meta.env.VITE_TOKEN);
          await this.fetchUserProfile();
          await this.fetchMenus();
          return true;
        }
        return false;
      } finally {
        this.loading = false;
      }
    },

    /**
     * Fetch user's menu data from API
     */
    async fetchMenus() {
      const { data } = await $api("/api/admin-api/v1/user-profile/menu");
      this.setMenus(data);
    },

    /**
     * Fetch access permissions for a specific menu
     * @param {number} menuId - ID of menu to fetch permissions for
     * @returns {Promise<Array>} Permission data
     */
    async fetchAccessByMenuId(menuId) {
      const { data } = await $api(
        `/api/admin-api/v1/user-profile/menu/access?menuId=${menuId}`
      );
      this.setPermissions(data, menuId);
      return data;
    },

    /**
     * Find menu item by ID in menu tree
     * @param {Array} menus - Menu tree to search
     * @param {number} menuId - ID of menu to find
     * @returns {Object|null} Found menu item or null
     */
    getMenuByMenuId(menus, menuId) {
      if (!menus || menus.length === 0) {
        return null;
      }

      let foundMenu = null;

      for (const menu of menus) {
        // Check current menu
        if (menu.id === menuId) {
          foundMenu = menu;
          break;
        }

        // Recursively check submenus
        if (menu.children && menu.children.length > 0) {
          foundMenu = this.getMenuByMenuId(menu.children, menuId);
          if (foundMenu) break;
        }
      }

      return foundMenu;
    },

    /**
     * Fetch and store user profile data
     */
    async fetchUserProfile() {
      // Parse user info from token
      const token = this.token;
      const data = jwtDecode(token);
      const userData = {
        ...data,
        username: data.sub,
      };
      this.setUserData(userData);
    },

    /**
     * Set authentication token
     * @param {string} token - JWT token
     */
    setToken(token) {
      this.token = token;
      useCookie("accessToken").value = token;
    },

    /**
     * Set user profile data
     * @param {Object} data - User profile data
     */
    setUserData(data) {
      this.userData = data;
      useStorage("userData").value = data;
    },

    /**
     * Set menu data with processed icons
     * @param {Array} menus - Raw menu data
     */
    setMenus(menus) {
      // Process icons for each menu item
      const processedMenus = menus?.map((menu) => processMenuIcon(menu));
      this.menus = processedMenus;
      useStorage("menus").value = processedMenus;

      const flatMenus = treeToList(processedMenus);

      const filteredMenus = flatMenus.filter((menu) => menu.link !== "NA" && menu.link !== '');

      console.log("flatMenus", filteredMenus)

      if (filteredMenus.length > 0) {
        this.defaultRoute = filteredMenus[0].link.replace(/\//g, '-').replace(/^-/, '')
        useStorage("defaultRoute").value = this.defaultRoute
      }

    },


    /**
     * Set user permissions for a specific menu
     * @param {Array} permissions - Permission data
     * @param {number} menuId - ID of related menu
     */
    setPermissions(permissions, menuId) {
      const menu = this.getMenuByMenuId(this.menus, menuId);
      permissions = permissions.map((action) => ({
        resource: menu.menuCode,
        action
      }));

      console.log("permissions", permissions)

      // 使用lodash对permissions对象, {resource, action}去重
      this.permissions = uniqWith([...this.permissions, ...permissions], isEqual)
      useStorage("permissions").value = this.permissions;
    },

    /**
     * Clear all authentication data
     */
    clearAuth() {
      this.token = null;
      this.userData = null;
      this.permissions = null;
      useCookie("accessToken").value = null;
      useStorage("userData").value = null;
      useStorage("permissions").value = null;
      clearAllCookie();
    },
  },
});
