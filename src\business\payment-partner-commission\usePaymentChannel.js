import { isEqual, uniqWith } from "lodash-es";
import { onMounted, ref, watch } from "vue";

/**
 * 
 * @param {*} typeId 1: <PERSON>zer Gold, 2: <PERSON><PERSON> Pin, 3: Third Party
 * @returns 
 */
export function usePaymentChannel(typeId = 1) {
  const paymentChannelList = ref([]);

  const loadPaymentChannelList = async () => {
    let res = [];
    res = await $api(
      "/api/admin-api/v1/commission-scheme/payment-method-by-type",
      {
        method: "GET",
        query: {
          typeId,
        },
      }
    );

    paymentChannelList.value = res?.data?.map((item, index) => {
      return {
        ...item,
        customId: index + 1,
        isSelected: false,
        label: item.name,
        value: item.customId,
      };
    });
  };

  onMounted(() => {
    loadPaymentChannelList();
  });

  return { paymentChannelList, loadPaymentChannelList };
}

export function useLocalPaymentChannelList(selectedPaymentChanelList) {
  const paymentChannelList = ref([]);

  onMounted(() => {
    paymentChannelList.value = selectedPaymentChanelList.value.map((item) => {
      return {
        ...item,
        title: item.paymentMethodName,
        value: item.customId,
      };
    });
  });

  watch(
    () => selectedPaymentChanelList.value,
    (newVal) => {
      paymentChannelList.value = newVal.map((item) => {
        return {
          ...item,
          title: item.paymentMethodName,
          value: item.customId,
        };
      });
    },
    { immediate: true, deep: true }
  );

  return { paymentChannelList };
}

export function useLocalCommissionTerritoryList(
  selectedCommissionTerritoryList
) {
  const commissionTerritoryList = ref([]);

  onMounted(() => {
    commissionTerritoryList.value = uniqWith(selectedCommissionTerritoryList.value.map((item) => {
      return {
        title: item.territoryName,
        value: item.territoryId,
      };
    }), isEqual)
  });

  watch(
    selectedCommissionTerritoryList,
    (newVal) => {
      commissionTerritoryList.value = uniqWith(selectedCommissionTerritoryList.value.map((item) => {
        return {
          title: item.territoryName,
          value: item.territoryId,
        };
      }), isEqual)
    },
    { immediate: true, deep: true }
  );

  return { commissionTerritoryList };
}

// 复制支付渠道信息
export const mapPaymentChannelItemForDuplicate = (detailItem, index) => {
  return {
    // 支付渠道基本信息
    code: detailItem.code || '',                     // 'razergoldwallet'
    name: detailItem.paymentMethodName || '',                     // 'Razer Gold'
    paymentMethodName: detailItem.paymentMethodName || '', // 'Razer Gold Malaysia'
    paymentMethodTypeId: detailItem.paymentMethodId || '',
    
    // 区域信息
    regionId: detailItem.transactRegionId || '',             // 1
    regionName: detailItem.regionName || '',         // 'Malaysia'
    territoryId: detailItem.territoryId,       // 0
    territoryName: detailItem.commissionTerritory,   // 'Malaysia'
    
    // 默认值设置
    defaultRate: detailItem.commissionRatePercentage,       // '20.0000'
    effectiveOn: detailItem.effectiveOn,
    
    // ID和状态
    id: detailItem.id,                         // 1
    
    // 编辑状态标记
    isEditing: false,
    isPaymentChannelEditing: false,
    isMerchantCurrencyEditing: false,
    isMinAmountEditing: false,
    isFixAmountEditing: false,
    isCommissionRateEditing: false,
    isEffectiveOnEditing: false,
    
    // PaymentChannelTable 需要的额外字段
    customId: index + 1,
    isSelected: false,
    label: detailItem.name,
    value: detailItem.id,
    
    // 复制标记
    isDuplicated: true,
    status: 'Draft',
    no: index + 1,
  };
};


