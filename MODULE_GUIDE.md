# Module Development Guide

This guide uses a practical example to demonstrate how to quickly develop a new module in the project using the custom `useTable` hook.

## Directory Structure

```bash
src/pages/module-name/
└── index.vue              # Module main page component
```

## Development Steps

### 1. Create Page Component

Create an `index.vue` file in the appropriate directory. The file-based routing system will automatically generate the corresponding route.

### 2. Component Basic Structure

```vue
<script setup>
// 1. Import required components and hooks
import { ref } from "vue";
import { useTable } from "@/hooks/useTable";
import { useRouter } from "vue-router";

// 2. Initialize router
const router = useRouter();

// 3. Define table column configuration
const headers = ref([
  { title: "No", key: "no", sortable: false },
  { title: "Name", key: "name" },
  // ... other columns
  { title: "Actions", key: "actions", sortable: false },
]);

// 4. Use the useTable hook to simplify table logic
const {
  searchQuery,         // Search query parameters
  page,                // Current page number
  itemsPerPage,        // Items per page
  items,               // Table data
  total,               // Total data count
  isLoading,           // Loading state
  handlePageChange,    // Page change handler
  handleItemsPerPageChange, // Items per page change handler
  handleSearch,        // Search handler
  getQueryParams,      // Get query parameters method
  handleSortChange,    // Sort change handler
  loadData,            // Manual data loading method
} = useTable({
  fetchData() {
    return $api("api/your-endpoint", {
      method: "POST",
      data: getQueryParams(),
    });
  },
  rowKey: "id",  // Row unique identifier
  createDefaultQuery: () => ({
    // Define default query parameters
    orderItemList: null,
    keyword: null,
    page: 1,
    size: 10,
  }),
});

// 5. Define action methods
const handleAdd = () => {
  router.push({ path: "/your-module/add" });
};

const handleEdit = (item) => {
  console.log("Edit item", item);
};
</script>

<template>
  <section>
    <VCard class="mb-6">
      <!-- Card title area -->
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            Module Name
            <VSpacer />
            <div class="d-flex align-center gap-4">
              <VBtn @click="handleAdd">CREATE</VBtn>
            </div>
          </div>
        </VCardTitle>
      </VCardItem>

      <!-- Search area -->
      <VCardText class="px-0">
        <VRow>
          <!-- Search field example -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField
              v-model="searchQuery.keyword"
              placeholder="Search keyword"
              clearable
              @update:model-value="handleSearch"
            />
          </VCol>
          
          <!-- More search fields... -->
        </VRow>
      </VCardText>

      <VDivider />

      <!-- Data table -->
      <VDataTableServer
        :items="items"
        item-value="id"
        :items-length="total"
        :headers="headers"
        :loading="isLoading"
        class="text-no-wrap"
        @update:sort-by="handleSortChange"
      >
        <!-- Actions column -->
        <template #[`item.actions`]="{ item }">
          <IconBtn @click="handleEdit(item)" v-tooltip="'Edit'">
            <VIcon icon="tabler-pencil" class="hover:text-primary" />
          </IconBtn>
        </template>

        <!-- Pagination -->
        <template #bottom>
          <TablePagination
            :page="page"
            :items-per-page="itemsPerPage"
            :total-items="total"
            @update:page="handlePageChange"
          >
            <div class="d-flex gap-3">
              <AppSelect
                :model-value="itemsPerPage"
                :items="paginationOptions"
                @update:model-value="handleItemsPerPageChange"
              />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>
```

### 3. Using the useTable Hook

`useTable` is a custom hook that greatly simplifies table-related state management and data loading logic:

```javascript
const tableOptions = useTable({
  // Required: Data fetching function
  fetchData() {
    return $api("your-api-path", {
      method: "POST",
      data: getQueryParams(), // Automatically provided method
    });
  },
  
  // Optional: Row unique identifier field, defaults to 'id'
  rowKey: "id",
  
  // Optional: Create default query parameters function
  createDefaultQuery: () => ({
    keyword: null,
    page: 1,
    size: 10,
  }),
  
  // Other optional configurations...
});
```

### 4. Search and Filtering

Utilize the `searchQuery` and `handleSearch` methods provided by `useTable` to implement search:

```vue
<VCol cols="12" sm="4">
  <AppTextField
    v-model="searchQuery.keyword"
    placeholder="Search keyword"
    clearable
    @update:model-value="handleSearch"
  />
</VCol>

<VCol cols="12" sm="4">
  <AppAutocomplete
    v-model="searchQuery.categoryId"
    :items="categories"
    placeholder="Select category"
    clearable
    @update:model-value="handleSearch"
  />
</VCol>
```

### 5. Pagination and Sorting

Pagination and sorting logic is already handled in `useTable`, components only need to bind the related methods:

```vue
<!-- Sorting -->
<VDataTableServer
  @update:sort-by="handleSortChange"
  ...other attributes
>
</VDataTableServer>

<!-- Pagination -->
<TablePagination
  :page="page"
  :items-per-page="itemsPerPage"
  :total-items="total"
  @update:page="handlePageChange"
>
  <AppSelect
    :model-value="itemsPerPage"
    :items="paginationOptions"
    @update:model-value="handleItemsPerPageChange"
  />
</TablePagination>
```

## Commonly Used Components

Common components in the project:

- `VCard` - Card container
- `VDataTableServer` - Server-side data table
- `AppTextField` - Text input field
- `AppSelect` - Dropdown select
- `AppAutocomplete` - Autocomplete selection field
- `AppDateTimePicker` - Date time picker
- `TablePagination` - Table pagination component
- `IconBtn` - Icon button

## Best Practices

1. **Use Custom Hooks**
   - Prioritize using the project's custom hooks (like `useTable`) to reduce duplicate code
   - Extract complex logic into reusable hooks

2. **Table Operations**
   - Use slots to customize table content and actions
   - Use `v-tooltip` to provide operation hints

3. **Performance Optimization**
   - Avoid unnecessary data loading
   - Properly use pagination and filtering to reduce server load

4. **Error Handling**
   - Ensure API calls have appropriate error handling mechanisms
   - Provide user-friendly error messages

## Debugging Tips

1. **Vue DevTools**
   - Use Vue DevTools to inspect component state
   - Monitor reactive data changes returned by hooks

2. **Network Requests**
   - Check query parameters generated by `useTable`
   - Verify that the API return data format meets expectations

3. **Common Issues Troubleshooting**
   - Check if the fetchData function returns the correct format
   - Confirm that table-bound data properties are consistent with API returns

## Testing

```javascript
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import YourModule from './index.vue'

describe('YourModule', () => {
  test('renders correctly', async () => {
    const wrapper = mount(YourModule, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          VDataTableServer: true,
          // Other components
        }
      }
    })
    
    expect(wrapper.exists()).toBe(true)
    // More assertions...
  })
})
```

## Deployment Checklist

- [ ] Confirm API paths are correct
- [ ] Remove debug code and console.log
- [ ] Check that error handling is complete
- [ ] Verify search, sort, and pagination functions work properly
- [ ] Check table display and operations meet requirements
- [ ] Confirm code meets project standards

## Resources

- [Vue 3 Documentation](https://vuejs.org/)
- [Vuetify Documentation](https://vuetifyjs.com/)
- [Pinia Documentation](https://pinia.vuejs.org/)
- [VueUse Collection](https://vueuse.org/) 