<script setup>
import { calculateHeight } from '@/utils/helpers'
// form data
const formModel = defineModel({
  type: Object,
  required: true,
})

const props = defineProps({
  type: {
    type: String,
    default: 'add',
  },
})

const formRef = ref(null)

const isEdit = computed(() => props.type === 'edit')
const isAdd = computed(() => props.type === 'add')

// expose methods and data to parent component
defineExpose({
  form: formRef,
  validate: () => {
    return formRef.value.validate()
  },
  getParams: () => {
    const params = {
      name: formModel.value.name,
      description: formModel.value.description,
      statusId: formModel.value.statusId
    }
    if (formModel.value.id) {
      params.id = formModel.value.id
    }
    return params
  },
  resetForm: () => {
    formModel.value = {
      name: '',
      description: '',
      statusId: 1
    }
  }
})
</script>

<template>
  <VCard>
    <VRow class="d-flex">
      <VCol cols="12" md="5" xl="5" style="width: 40%;">
        <div class="form-wrapper">
          <VCardTitle>
            1. Enter User Group’s Info
          </VCardTitle>
          <VCardText>
            <VForm ref="formRef">
              <VRow>
                <VCol cols="12">
                  <AppTextField v-model="formModel.name" label="User Group Name*" placeholder="Type a User Group Name" :rules="isAdd || isEdit ? [requiredValidator, userGroupNameValidator] : []"
                  />
                </VCol>

                <VCol cols="12">
                  <AppTextField v-model="formModel.description" label="Description (Optional)" placeholder="" :rules="isAdd || isEdit ? [userGroupDescriptionValidator] : []"/>
                </VCol>

                <VCol cols="12">
                  <VSwitch v-model="formModel.statusId" label="Enabled" :false-value="0" :true-value="1"
                    color="primary" />
                </VCol>
              </VRow>
            </VForm>
          </VCardText>
        </div>
      </VCol>
    </VRow>
  </VCard>
</template>

<style lang="scss" scoped>
.form-wrapper {
  background: #1e1e1e;
  border-radius: 16px;
  height: 100%;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
}

:deep(.v-card-text) {
  padding-bottom: 0px !important;
}

:deep(.v-treeview) {
  overflow-y: auto;
  flex: 1;


  // 调整整体树形视图的样式
  .v-treeview-group {
    margin-left: 0;

    // 选中状态的背景色
    &:has(.v-selection-control--dirty) {
      background-color: #303030;

      // 确保父节点也有背景色
      .v-treeview-node__root {
        background-color: #303030;
      }
    }

    // 半选状态的背景色
    &:has(.v-checkbox--indeterminate) {
      background-color: #303030;

      .v-treeview-node__root {
        background-color: #303030;
      }
    }
  }





  // 调整根节点样式
  .v-treeview-node__root {
    min-height: 32px;
    margin-left: 0;
    padding: 4px 8px; // 添加内边距使背景色更明显
    border-radius: 4px; // 可选：添加圆角
    transition: background-color 0.2s ease; // 添加过渡效果
    pointer-events: none;
  }

  // 调整子节点容器样式
  .v-list-group__items {
    margin-left: 32px; // 增加缩进距离
    position: relative;
  }

  .v-list-item-action {
    display: none;
  }

  &.v-list {
    background: transparent !important;
    padding: 0;

    .v-list-item {
      padding: 8px 0 !important;
    }
  }

  // 只允许展开/折叠图标可点击
  .v-treeview-node__toggle {
    pointer-events: auto;
  }
}

:deep(.v-selection-control) {
  justify-content: flex-end;
  flex-flow: row-reverse;
}


:deep(.v-switch .v-label) {
  padding-inline-end: 10px;
  padding-inline-start: 0px;
}

// 确保按钮区域始终可见
.v-row:last-child {
  margin-top: auto;
  padding: 16px;
  background: var(--v-theme-surface);
  position: sticky;
  bottom: 0;
  z-index: 1;
}
</style>
