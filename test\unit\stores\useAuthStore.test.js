import { beforeEach, describe, expect, it, vi } from "vitest";
import { create<PERSON><PERSON>, setActive<PERSON><PERSON> } from "pinia";

import { useAuthStore } from "@/stores/useAuthStore";

// Use global mockCookies and mockStorage
const { mockCookies, mockStorage } = global;

// Mock jwt-decode
const jwtDecode = vi.hoisted(() => vi.fn());
vi.mock("jwt-decode", () => ({
  jwtDecode,
}));

vi.mock("@/hooks/useCookie", async () => {
  const actual = await vi.importActual("@/hooks/useCookie");
  console.log('useCookie', actual)
  return {
    ...actual,
    clearAllCookie: vi.fn(() => {
      Object.keys(mockCookies).forEach((key) => delete mockCookies[key]);
    }),
  };
});

describe("useAuthStore", () => {
  beforeEach(() => {
    // Create a fresh Pinia instance and make it active
    setActivePinia(createPinia());

    // Clear all mocks before each test
    vi.clearAllMocks();

    // Clear both cookies and storage
    Object.keys(mockCookies).forEach((key) => delete mockCookies[key]);
    Object.keys(mockStorage).forEach((key) => delete mockStorage[key]);
  });

  describe("autoLogin", () => {
    it("should auto login successfully", async () => {
      const store = useAuthStore();
      const mockToken = "mock-test-token";
      const mockUserData = {
        sub: "test-user",
        email: "<EMAIL>",
      };

      // 确保API模拟返回正确的数据结构
      global.$api
        .mockResolvedValueOnce({
          data: mockToken,
        }) // 可能需要调整返回格式
        .mockResolvedValueOnce({
          data: [
            {
              id: 1,
              name: "User Management",
              link: "NA",
              menuTypeId: 1,
              icon: "tabler-user-filled",
              accessId: null,
              parentId: 1,
              sortOrder: 1,
              statusId: 1,
              createdBy: "System",
              createdDateTime: "2025-02-19T07:26:17.826613Z",
              updatedBy: "NA",
              updatedDateTime: "2025-02-19T07:26:17.826613Z",
              children: [
                {
                  id: 2,
                  name: "Users",
                  link: "/user/list",
                  menuTypeId: 1,
                  icon: "NA",
                  accessId: 1,
                  parentId: 1,
                  sortOrder: 1,
                  statusId: 1,
                  createdBy: "System",
                  createdDateTime: "2025-02-19T07:26:17.988238Z",
                  updatedBy: "NA",
                  updatedDateTime: "2025-02-19T07:26:17.988238Z",
                  children: null,
                  menuCode: "UserList",
                  code: "NA",
                },
                {
                  id: 3,
                  name: "Roles & Permissions",
                  link: "/user/role",
                  menuTypeId: 1,
                  icon: "NA",
                  accessId: 6,
                  parentId: 1,
                  sortOrder: 2,
                  statusId: 1,
                  createdBy: "System",
                  createdDateTime: "2025-02-19T07:26:18.148889Z",
                  updatedBy: "NA",
                  updatedDateTime: "2025-03-05T03:21:13.380774Z",
                  children: null,
                  menuCode: "UserRole",
                  code: "NA",
                },
                {
                  id: 4,
                  name: "User Groups",
                  link: "/user/group",
                  menuTypeId: 1,
                  icon: "NA",
                  accessId: 11,
                  parentId: 1,
                  sortOrder: 3,
                  statusId: 1,
                  createdBy: "System",
                  createdDateTime: "2025-02-19T07:26:18.313231Z",
                  updatedBy: "NA",
                  updatedDateTime: "2025-03-05T03:21:13.627253Z",
                  children: null,
                  menuCode: "UserGroup",
                  code: "NA",
                },
              ],
              menuCode: "NA",
              code: "NA",
            },
          ],
          errorCode: "000000",
          message: "success",
          success: true,
        }); // 可能需要调整返回格式

      // Mock jwt-decode
      jwtDecode.mockReturnValue(mockUserData);

      // 添加调试信息
      console.log("开始测试 autoLogin");

      // 执行测试
      const result = await store.autoLogin();

      console.log("autoLogin 结果:", result);
      console.log("store 状态:", {
        token: store.token,
        userData: store.userData,
        menus: store.menus,
      });

      // Verify the result
      expect(result).toBe(true);
      expect(store.token).toBe(mockToken);

      expect(store.userData).toEqual({
        sub: mockUserData.sub,
        email: mockUserData.email,
        username: mockUserData.sub,
      });
      expect(Array.isArray(store.menus)).toBe(true);
      expect(store.menus.length).toBeGreaterThan(0);
      expect(store.menus[0]).toHaveProperty('id', 1);
      expect(store.menus[0]).toHaveProperty('name', 'User Management');
      expect(store.loading).toBe(false);

      // Check cookie storage
      console.log('测试中的 cookies:', mockCookies);
      // expect(mockCookies.accessToken).toBe(mockToken);

      // Check local storage
      expect(mockStorage.userData).toEqual({
        sub: mockUserData.sub,
        email: mockUserData.email,
        username: mockUserData.sub,
      });

      // Verify API calls
      expect(global.$api).toHaveBeenNthCalledWith(1, "/api/auth/login", {
        method: "GET",
      });
      expect(global.$api).toHaveBeenNthCalledWith(
        2,
        "/api/admin-api/v1/user-profile/menu"
      );
    });

    it("should handle auto login failure", async () => {
      const store = useAuthStore();

      // Mock API failure
      global.$api.mockRejectedValueOnce(new Error("Login failed"));

      // Execute autoLogin
      const result = await store.autoLogin();

      console.log("token", store.token);
      console.log("userData", store.userData);

      // Verify the result
      expect(result).toBe(false);
      expect(store.token).toBeUndefined();
      expect(store.userData).toBeNull();
      expect(store.loading).toBe(false);
    });
  });

  describe("clearAuth", () => {
    it("should clear auth data on logout", () => {
      const store = useAuthStore();

      // Set initial state
      store.token = "test-token";
      store.userData = { username: "test-user" };
      mockCookies.accessToken = "test-token";
      mockStorage.userData = { username: "test-user" };

      // Execute clearAuth
      store.clearAuth();

      // Verify state is cleared
      expect(store.token).toBeNull();
      expect(store.userData).toBeNull();
      expect(mockCookies.accessToken).toBeUndefined();
      expect(mockStorage.userData).toBeUndefined();
    });
  });
});
