export const useMainTaskType = () => {
    // Declare a reactive list to store main task types
    const mainTaskTypeList = ref([])
    // Function to load main task type data from the API
    const loadData = async () => {
        const res = await $api('api/pin-generator/v1/batch-task/main-task/type', {
            method: 'GET',
        })
        // Update mainTaskTypeList with transformed response data if res.data is an array
        mainTaskTypeList.value = Array.isArray(res.data)
            ? res.data.map(item => ({
                // Spread the original item properties
                ...item,
                title: item, // Set the display title using the name property
                value: item    // Assign the id as the value property
            })) : [];
    }
    // Execute loadData when the component is mounted
    onMounted(async () => {
        await loadData()
    })
    // Return the reactive mainTaskTypeList for use in the component
    return { mainTaskTypeList }
}

