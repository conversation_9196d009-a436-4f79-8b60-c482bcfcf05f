export const useMerchant = () => {
  const merchantList = ref([]);

  const loadMerchantList = async (name) => {
    const res = await $api("/api/admin-api/v1/commission-scheme/get-all-merchant", {
      method: "GET",
      params: {
        name,
      },
    });

    merchantList.value = res?.data?.map(item => {
      return {
        ...item,
        title: item.name,
        value: item.id,
      };
    });
  };

  onMounted(() => {
    loadMerchantList();
  });

  return { merchantList, loadMerchantList };
};
