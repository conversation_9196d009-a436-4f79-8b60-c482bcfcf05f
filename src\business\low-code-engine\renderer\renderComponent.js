import { computed, h, provide, unref } from 'vue';
import { getComponentByName, isHtmlTag, resolveExpression } from './utils';

import { p } from '@antfu/utils';
import renderSlot from './renderSlot'; // 导入 renderSlot

/**
 * 递归渲染组件
 * @param {Object} componentMeta - 组件元数据
 * @param {Object} context - 上下文（data, methods, renderSlotFn, renderComponentFn等）
 * @returns {import('vue').VNode | null} 渲染后的VNode
 */
function renderComponent(componentMeta, context) {
  if (!componentMeta || !componentMeta.type) {
    return null;
  }

  const { type, props, events, children, 'v-if': vIf, 'v-for': vFor, permission, model, slots, key } = componentMeta;
  const { data, renderSlotFn, renderComponentFn } = context; // 接收 renderSlotFn, renderComponentFn

  // 1. 处理 v-if 条件渲染
  if (vIf !== undefined) {
    const vIfResult = resolveExpression(vIf, context);
    if (!vIfResult) {
      return null;
    }
  }

  // 2. 处理权限渲染（假设 context.hasPermission 是一个权限判断函数）
  // if (permission && !context.hasPermission(permission, data)) {
  //   return null;
  // }

  // 3. 处理 v-for 列表渲染
  if (vFor) {
    const vForMatch = vFor.match(/(.*?) in (.*)/);
    if (!vForMatch) {
      console.error('v-for 表达式格式错误:', vFor);
      return null;
    }
    
    const [, itemAlias, , listExpression] = vForMatch;
    const listData = resolveExpression(listExpression.trim(), context);

    if (Array.isArray(listData)) {
      return listData.map((item, index) => {
        const newItemData = { ...data, [itemAlias.trim()]: item, '$index': index };
        const newContext = { 
          ...context, 
          data: newItemData,
          // 确保item能够在表达式解析中直接访问
          [itemAlias.trim()]: item
        };

        // 递归调用 renderComponentFn，传递新的上下文
        return renderComponentFn(componentMeta, newContext);
      });
    }
  }

  // 4. 处理 props 和 key
  const componentProps = {};
  let rawTextContent = null; // 用于存储直接文本内容

  // 处理 key 属性（用于强制重新渲染）
  if (key) {
    componentProps.key = resolveExpression(key, {...context, ...data});
  }
  if (props) {
    for (const propKey in props) {
      const propValue = props[propKey];
      
      // 对于 text 属性，无论是 HTML 标签还是组件，都设置为 rawTextContent
      if (propKey === 'innerText' || propKey === 'textContent' || propKey === 'text') {
        const resolvedText = resolveExpression(propValue, context);
        rawTextContent = resolvedText;
      } else {
        // 通用的响应式数据绑定处理
        // 如果 prop 值是形如 "{{dataKey}}" 的表达式，直接绑定响应式数据
        if (typeof propValue === 'string' && 
            propValue.startsWith('{{') && 
            propValue.endsWith('}}')) {
          
          const expression = propValue.slice(2, -2).trim();
          
          // 检查是否是简单的数据属性访问（如 "page", "totalUsers" 等）
          if (!expression.includes('.') && !expression.includes('(') && 
              data.hasOwnProperty(expression)) {
            
            // 直接绑定响应式数据，保持响应性
            componentProps[propKey] = data[expression];
          } else {
            // 复杂表达式使用常规解析
            componentProps[propKey] = resolveExpression(propValue, context);
          }
        } else {
          componentProps[propKey] = resolveExpression(propValue, context);
        }
      }
    }
  }

  // 5. 处理 v-model
  let modelUpdateHandlers = [];
  if (model) {
    const modelValue = resolveExpression(model, context);
    componentProps.modelValue = modelValue;
    
    const match = model.match(/^\{\{(.*)\}\}$/);
    const modelKey = match ? match[1].trim() : model;
    // v-model 数据回写逻辑始终第一个
    modelUpdateHandlers.push((newValue) => {
      if (context.dataManager && typeof context.dataManager.set === 'function') {
        context.dataManager.set(data, modelKey, newValue);
      } else {
        let target = data;
        const path = modelKey.split('.');
        for (let i = 0; i < path.length - 1; i++) {
          target = target[path[i]];
          if (!target) return;
        }
        target[path[path.length - 1]] = newValue;
      }
    });
  }

  // 6. 处理事件events
  const componentEvents = {};
  if (events) {
    for (const eventName in events) {
      const actions = events[eventName];
      // 合并 update:modelValue 事件
      if (eventName === 'update:modelValue' && model) {
        actions.forEach(actionMeta => {
          modelUpdateHandlers.push((...args) => {
            if (context[actionMeta.action]) {
              const eventArgsContext = {};
              if (args.length > 0) {
                if (args.length === 2 && typeof args[1] === 'object' && args[1] !== null && 'item' in args[1]) {
                  Object.assign(eventArgsContext, args[1]);
                } else if (args.length === 1 && typeof args[0] === 'object' && args[0] !== null && 'item' in args[0]) {
                  Object.assign(eventArgsContext, args[0]);
                } else {
                  if (args[0] !== undefined) eventArgsContext.$event = args[0];
                  if (args[1] !== undefined) eventArgsContext.$arg1 = args[1];
                }
              }
              const actionContext = { 
                ...context, 
                ...data, 
                ...eventArgsContext,
                // 确保插槽上下文中的 item 能传递到事件处理中
                ...(context.item && { item: context.item })
              };
              const resolvedArgs = actionMeta.params ? actionMeta.params.map(p => {
                // 特殊处理：如果参数名是 'item' 且上下文中有 item 对象，直接传递对象
                if (p === 'item' && actionContext.item && typeof actionContext.item === 'object') {
                  return actionContext.item;
                }
                // 其他情况使用表达式解析
                return resolveExpression(p, actionContext);
              }) : args;
              context[actionMeta.action](...resolvedArgs);
            } else {
              console.warn(`未找到方法: ${actionMeta.action}`);
            }
          });
        });
      } else {
        // 其他事件正常处理
        componentEvents[`on${eventName.charAt(0).toUpperCase()}${eventName.slice(1)}`] = (...args) => {
          actions.forEach(actionMeta => {
          if (context[actionMeta.action]) {
            // 对于 click 事件，添加阻止事件冒泡的逻辑
            if (eventName === 'click' && args[0] && typeof args[0].stopPropagation === 'function') {
              args[0].stopPropagation();
              args[0].preventDefault();
            }
            
            const eventArgsContext = {};
              if (args.length > 0) {
                if (args.length === 2 && typeof args[1] === 'object' && args[1] !== null && 'item' in args[1]) {
                  Object.assign(eventArgsContext, args[1]);
                } else if (args.length === 1 && typeof args[0] === 'object' && args[0] !== null && 'item' in args[0]) {
                  Object.assign(eventArgsContext, args[0]);
                } else {
                    if (args[0] !== undefined) eventArgsContext.$event = args[0];
                    if (args[1] !== undefined) eventArgsContext.$arg1 = args[1];
                }
            }
            
            // 重要修复：确保插槽上下文中的 item 能传递到事件处理中
            const actionContext = { 
              ...context, 
              ...data, 
              ...eventArgsContext,
              // 如果当前上下文中有 item，确保它能被访问到
              ...(context.item && { item: context.item })
            };
            
            const resolvedArgs = actionMeta.params ? actionMeta.params.map(p => {
              // 特殊处理：如果参数名是 'item' 且上下文中有 item 对象，直接传递对象
              if (p === 'item' && actionContext.item && typeof actionContext.item === 'object') {
                return actionContext.item;
              }
              // 其他情况使用表达式解析
              return resolveExpression(p, actionContext);
            }) : args;
            
            context[actionMeta.action](...resolvedArgs);
          } else {
            console.warn(`未找到方法: ${actionMeta.action}`);
          }
        });
      };
    }
    }
  }
  // 合并后的 update:modelValue 事件赋值
  if (modelUpdateHandlers.length === 1) {
    componentProps['onUpdate:modelValue'] = modelUpdateHandlers[0];
  } else if (modelUpdateHandlers.length > 1) {
    componentProps['onUpdate:modelValue'] = function (...args) {
      modelUpdateHandlers.forEach(fn => fn(...args));
    };
  }

  // 7. 处理 children (递归渲染)
  let finalChildren = null; // 用于存储最终的子VNode或文本

  if (children && children.length > 0) {
    finalChildren = children.map(childMeta => renderComponentFn(childMeta, context)); // 使用 renderComponentFn
  }

  // 结合直接文本内容 (rawTextContent) 和子VNode (finalChildren)
  if (rawTextContent !== null && rawTextContent !== undefined) {
      if (finalChildren) {
          // 如果已有子VNode，则将文本内容添加到子VNode数组的开头
          if (Array.isArray(finalChildren)) {
              finalChildren = [rawTextContent, ...finalChildren];
          } else {
              // 如果 finalChildren 是单个VNode，则转换为数组并添加文本
              finalChildren = [rawTextContent, finalChildren];
          }
      } else {
          // 如果没有其他子VNode，则 finalChildren 就是文本内容
          finalChildren = rawTextContent;
      }
  }

  // 8. 处理插槽 (具名插槽)
  const componentSlots = {};
  if (slots) {
    for (const slotName in slots) {
      const slotContentMeta = slots[slotName];
      componentSlots[slotName] = (slotProps) => {
        // 尝试解除 proxy
        let rawItem = slotProps?.item;
        if (rawItem && typeof rawItem === 'object') {
          // 尝试JSON转换解除proxy
          try {
            const jsonStr = JSON.stringify(rawItem);
            const plainItem = JSON.parse(jsonStr);
            rawItem = plainItem;
          } catch (e) {
            // JSON转换失败，继续使用原对象
          }
        }
        
        const slotContext = { 
          ...context, 
          data: { ...data, ...slotProps },
          slotProps: slotProps, // 确保插槽参数可以在表达式解析中访问
          // 直接将item提升到顶层，方便表达式访问
          item: rawItem
        };
        
        return renderSlotFn(slotContentMeta, slotContext);
      };
    }
  }

  const Component = getComponentByName(type, context);

  // h函数第三个参数是 childrenVNodes 或 slots
  // 如果有具名插槽，则第三个参数是 slots 对象
  // 如果没有具名插槽，但有 childrenVNodes，则第三个参数是 childrenVNodes
  if (Object.keys(componentSlots).length > 0) {
    return h(Component, { ...componentProps, ...componentEvents }, componentSlots);
  } else if (finalChildren) {
    return h(Component, { ...componentProps, ...componentEvents }, {default: () => finalChildren}); // 使用 finalChildren
  } else {
    return h(Component, { ...componentProps, ...componentEvents }); // 没有具名插槽，也没有 childrenVNodes，则返回组件对象
  }
}

export default renderComponent;

