#!/usr/bin/env bash

declare -xr SERVICE_NAME=layer-secret
declare -xr  BUILD=LAMBDA
declare -x TAGS
declare -x LAMBDA_LAYER
declare -x LAMBDA_MEMORY

deploy_override()
{
    local -xu environment=$ENV
    TAGS="{\"Project\":\"Gold-App\",\"Environment\":\"$environment\"}"
    LAMBDA_LAYER="arn:aws:lambda:us-east-1:177933569100:layer:AWS-Parameters-and-Secrets-Lambda-Extension:11"

    LAMBDA_MEMORY=256
    if [[ $ENV == 'zgd' ]]; then
        echo "do nothing"
        LAMBDA_LAYER="arn:aws:lambda:ap-southeast-1:044395824272:layer:AWS-Parameters-and-Secrets-Lambda-Extension:11"
    fi
}



