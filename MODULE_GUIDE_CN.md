# 模块开发指南

本指南以实际案例为例，介绍如何在项目中使用自定义钩子`useTable`快速开发一个新模块。

## 目录结构

```bash
src/pages/module-name/
└── index.vue              # 模块主页面组件
```

## 模块开发步骤

### 1. 创建页面组件

在适当的目录下创建`index.vue`文件。基于文件的路由系统会自动生成对应路由。

### 2. 组件基本结构

```vue
<script setup>
// 1. 导入所需组件和钩子
import { ref } from "vue";
import { useTable } from "@/hooks/useTable";
import { useRouter } from "vue-router";

// 2. 初始化路由
const router = useRouter();

// 3. 定义表格列配置
const headers = ref([
  { title: "序号", key: "no", sortable: false },
  { title: "名称", key: "name" },
  // ... 其他列配置
  { title: "操作", key: "actions", sortable: false },
]);

// 4. 使用useTable钩子简化表格逻辑
const {
  searchQuery,         // 搜索查询参数
  page,                // 当前页码
  itemsPerPage,        // 每页条数
  items,               // 表格数据
  total,               // 总数据量
  isLoading,           // 加载状态
  handlePageChange,    // 页码变更处理
  handleItemsPerPageChange, // 每页条数变更处理
  handleSearch,        // 搜索处理
  getQueryParams,      // 获取查询参数
  handleSortChange,    // 排序变更处理
  loadData,            // 手动加载数据方法
} = useTable({
  fetchData() {
    return $api("api/your-endpoint", {
      method: "POST",
      data: getQueryParams(),
    });
  },
  rowKey: "id",  // 行唯一标识
  createDefaultQuery: () => ({
    // 定义默认查询参数
    orderItemList: null,
    keyword: null,
    page: 1,
    size: 10,
  }),
});

// 5. 定义操作方法
const handleAdd = () => {
  router.push({ path: "/your-module/add" });
};

const handleEdit = (item) => {
  console.log("编辑项目", item);
};
</script>

<template>
  <section>
    <VCard class="mb-6">
      <!-- 卡片标题区域 -->
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            模块名称
            <VSpacer />
            <div class="d-flex align-center gap-4">
              <VBtn @click="handleAdd">新增</VBtn>
            </div>
          </div>
        </VCardTitle>
      </VCardItem>

      <!-- 搜索区域 -->
      <VCardText class="px-0">
        <VRow>
          <!-- 搜索字段示例 -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField
              v-model="searchQuery.keyword"
              placeholder="搜索关键词"
              clearable
              @update:model-value="handleSearch"
            />
          </VCol>
          
          <!-- 更多搜索字段... -->
        </VRow>
      </VCardText>

      <VDivider />

      <!-- 数据表格 -->
      <VDataTableServer
        :items="items"
        item-value="id"
        :items-length="total"
        :headers="headers"
        :loading="isLoading"
        class="text-no-wrap"
        @update:sort-by="handleSortChange"
      >
        <!-- 操作列 -->
        <template #[`item.actions`]="{ item }">
          <IconBtn @click="handleEdit(item)" v-tooltip="'编辑'">
            <VIcon icon="tabler-pencil" class="hover:text-primary" />
          </IconBtn>
        </template>

        <!-- 分页 -->
        <template #bottom>
          <TablePagination
            :page="page"
            :items-per-page="itemsPerPage"
            :total-items="total"
            @update:page="handlePageChange"
          >
            <div class="d-flex gap-3">
              <AppSelect
                :model-value="itemsPerPage"
                :items="paginationOptions"
                @update:model-value="handleItemsPerPageChange"
              />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>
```

### 3. useTable钩子的使用

`useTable`是一个自定义钩子，极大简化了表格相关的状态管理和数据加载逻辑：

```javascript
const tableOptions = useTable({
  // 必选：数据获取函数
  fetchData() {
    return $api("你的API路径", {
      method: "POST",
      data: getQueryParams(), // 自动提供的方法
    });
  },
  
  // 可选：行唯一标识字段，默认为'id'
  rowKey: "id",
  
  // 可选：创建默认查询参数的函数
  createDefaultQuery: () => ({
    keyword: null,
    page: 1,
    size: 10,
  }),
  
  // 其他可选配置...
});
```

### 4. 搜索和过滤

利用`useTable`提供的`searchQuery`和`handleSearch`方法实现搜索：

```vue
<VCol cols="12" sm="4">
  <AppTextField
    v-model="searchQuery.keyword"
    placeholder="搜索关键词"
    clearable
    @update:model-value="handleSearch"
  />
</VCol>

<VCol cols="12" sm="4">
  <AppAutocomplete
    v-model="searchQuery.categoryId"
    :items="categories"
    placeholder="选择分类"
    clearable
    @update:model-value="handleSearch"
  />
</VCol>
```

### 5. 分页和排序

分页和排序逻辑已经在`useTable`中处理，组件中只需绑定相关方法：

```vue
<!-- 排序 -->
<VDataTableServer
  @update:sort-by="handleSortChange"
  ...其他属性
>
</VDataTableServer>

<!-- 分页 -->
<TablePagination
  :page="page"
  :items-per-page="itemsPerPage"
  :total-items="total"
  @update:page="handlePageChange"
>
  <AppSelect
    :model-value="itemsPerPage"
    :items="paginationOptions"
    @update:model-value="handleItemsPerPageChange"
  />
</TablePagination>
```

## 常用组件

项目中常用的组件：

- `VCard` - 卡片容器
- `VDataTableServer` - 服务端数据表格
- `AppTextField` - 文本输入框
- `AppSelect` - 下拉选择框
- `AppAutocomplete` - 自动完成选择框
- `AppDateTimePicker` - 日期时间选择器
- `TablePagination` - 表格分页组件
- `IconBtn` - 图标按钮

## 最佳实践

1. **使用自定义钩子**
   - 优先使用项目提供的自定义钩子（如`useTable`）减少重复代码
   - 将复杂逻辑抽取为可复用的钩子

2. **表格操作**
   - 使用插槽自定义表格内容和操作
   - 使用`v-tooltip`提供操作提示

3. **性能优化**
   - 避免不必要的数据加载
   - 合理使用分页和过滤减轻服务器负担

4. **错误处理**
   - 确保API调用有适当的错误处理机制
   - 为用户提供友好的错误提示

## 调试技巧

1. **Vue DevTools**
   - 使用Vue DevTools检查组件状态
   - 监控钩子返回的响应式数据变化

2. **网络请求**
   - 检查`useTable`生成的查询参数
   - 验证API返回的数据格式是否符合预期

3. **常见问题排查**
   - 检查fetchData函数返回格式是否正确
   - 确认表格绑定的数据属性与API返回一致

## 测试

```javascript
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import YourModule from './index.vue'

describe('YourModule', () => {
  test('renders correctly', async () => {
    const wrapper = mount(YourModule, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          VDataTableServer: true,
          // 其他组件
        }
      }
    })
    
    expect(wrapper.exists()).toBe(true)
    // 更多断言...
  })
})
```

## 部署检查清单

- [ ] 确认API路径正确
- [ ] 移除调试代码和console.log
- [ ] 检查错误处理是否完善
- [ ] 验证搜索、排序、分页功能是否正常
- [ ] 检查表格展示和操作是否符合需求
- [ ] 确认代码规范符合项目要求

## 相关资源

- [Vue 3 文档](https://vuejs.org/)
- [Vuetify 文档](https://vuetifyjs.com/)
- [Pinia 文档](https://pinia.vuejs.org/)
- [VueUse 工具集](https://vueuse.org/) 