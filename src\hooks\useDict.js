import { ref, shallowRef, readonly } from 'vue'

const dictCache = new Map()

export function useDict(code) {
  if (!dictCache.has(code)) {
    const items = shallowRef([])
    const loading = ref(false)
    const error = ref(null)

    const fetchData = async () => {
      if (loading.value) return

      loading.value = true
      error.value = null

      try {
        const response = await $api(
          '/api/admin-api/v1/dict/dict-item/dict-code?dictCode=' + code,
          {
            method: 'GET',
          }
        )

        items.value = response.data || []
      } catch (err) {
        error.value = err
        console.error(`Error fetching dict items for ${code}:`, err)
      } finally {
        loading.value = false
      }
    }

    fetchData()

    dictCache.set(code, {
      items: readonly(items),
      loading: readonly(loading),
      error: readonly(error),
      refresh: fetchData,
    })
  }

  return dictCache.get(code)
}

export function clearDictCache(code) {
  if (code) {
    dictCache.delete(code)
  } else {
    dictCache.clear()
  }
}
