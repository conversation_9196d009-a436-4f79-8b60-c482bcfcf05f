import { pathToFileURL } from 'url';

// Windows path fix for ESM loader
export async function resolve(specifier, context, defaultResolve) {
  // Handle Windows absolute paths (C:/, D:/, etc.)
  if (typeof specifier === 'string' && specifier.match(/^[a-zA-Z]:[/\\]/)) {
    specifier = pathToFileURL(specifier).href;
  }

  return defaultResolve(specifier, context);
}

export async function load(url, context, defaultLoad) {
  return defaultLoad(url, context);
}
