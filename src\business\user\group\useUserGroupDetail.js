/**
 * Provides reactive state for user group detail data.
 *
 * @returns {Object} with one property:
 *   - userGroupData: reactive ref to user group data
 */
export const useUserGroupDetail = (id) => {
  const userGroupData = ref({})
  const loadData = async () => {
    try { 
      const res = await $api(`/api/admin-api/v1/user-group/${id}`, {
        method: 'GET'
      })
      userGroupData.value = res.data
    } catch (error) {
      console.error('Error loading user group detail:', error)
      userGroupData.value = {}
    }
  }
  onMounted(loadData)
  return {
    userGroupData,
  }
}

