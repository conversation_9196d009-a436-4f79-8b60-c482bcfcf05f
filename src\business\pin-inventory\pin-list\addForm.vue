<script setup>
import { ref, computed } from 'vue'
import AppTextField from '@/components/AppTextField.vue'
import AppAutocomplete from '@/components/AppAutocomplete.vue'
import AppDateTimePicker from '@/components/AppDateTimePicker.vue'
import { initialFormState } from '@/business/pin-inventory/pin-list/useSubmitPINListRequest.js'
import {
  usePINCardTypes,
  useProductNames,
  usePINStatuses,
  useRedemptionCurrencies,
  useRedemptionRegions,
  useRedemptionSources,
  useTimezones,
} from '@/business/pin-inventory/pin-list/usePINListRequestsConfig.js'
import dateUtil from '@/utils/day'

const formModel = defineModel({
  type: Object,
  required: true,
})

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
})

// Emit events for parent component actions
const emit = defineEmits(['submit', 'cancel'])

const formRef = ref(null)

const productNameOptions = useProductNames()

const pinStatusOptions = usePINStatuses()

const redemptionFromOptions = useRedemptionSources()

const redemptionRegionOptions = useRedemptionRegions()

const redemptionCurrencyOptions = useRedemptionCurrencies()

const pinCardTypeOptions = usePINCardTypes()

const timezoneOptions = useTimezones()

const errors = ref({})

const requiredValidator = (v) => !!v || 'This field is required'
const minLengthValidator = (v) =>
  (v && v.length >= 15) || 'Batch Name must be least 15 characters long'

// Handle submit action
const handleSubmit = () => {
  emit('submit')
}

// Handle cancel action
const handleCancel = () => {
  emit('cancel')
}

onMounted(() => {
  const localUTCOffset = dateUtil.getLocalTimezoneUTCOffset()
  const localTimezone = timezoneOptions.find(
    (options) => options.value === localUTCOffset
  )

  if (localTimezone) {
    formModel.value.utcOffset = localTimezone.value
  } else {
    message.error('Timezone not found')
  }
})

defineExpose({
  form: formRef,
  validate: () => {
    if (!formRef.value) return { valid: false, errors: {} }

    errors.value = {}
    let isValid = true

    if (!formModel.value.batchName) {
      errors.value = 'Batch Name is required'
      isValid = false
    } else if (formModel.value.batchName.length < 15) {
      errors.value = 'Batch Name must be least 15 characters long'
      isValid = false
    } else if (!formModel.value.utcOffset) {
      errors.value = 'Timezone is required'
      isValid = false
    }

    return { valid: isValid, errors: errors.value }
  },
  getParams: () => {
    const params = {
      batchName: formModel.value.batchName,
      productId: formModel.value.productId,
      razerGoldPINStatus: formModel.value.razerGoldPINStatus,
      redemptionFromId: formModel.value.redemptionFromId,
      redemptionRegionId: formModel.value.redemptionRegionId,
      redemptionCurrencyCode: formModel.value.redemptionCurrencyCode,
      pinCardTypeId: formModel.value.pinCardTypeId,
      utcOffset: formModel.value.utcOffset,
    }

    // Process activationPeriod start
    if (formModel.value.activationPeriodStart) {
      params.activationPeriodStart = dateUtil.formatWithUTCOffset(
        formModel.value.activationPeriodStart,
        formModel.value.utcOffset
      )
    }

    // Process activationPeriod end
    if (formModel.value.activationPeriodEnd) {
      params.activationPeriodEnd = dateUtil.formatWithUTCOffset(
        formModel.value.activationPeriodEnd,
        formModel.value.utcOffset
      )
    }

    // Process redemptionPeriod start
    if (formModel.value.redemptionPeriodStart) {
      params.redemptionPeriodStart = dateUtil.formatWithUTCOffset(
        formModel.value.redemptionPeriodStart,
        formModel.value.utcOffset
      )
    }

    // Process redemptionPeriod end
    if (formModel.value.redemptionPeriodEnd) {
      params.redemptionPeriodEnd = dateUtil.formatWithUTCOffset(
        formModel.value.redemptionPeriodEnd,
        formModel.value.utcOffset
      )
    }

    return params
  },
  resetForm: () => {
    formModel.value = structuredClone(initialFormState)
    // Ensure new date fields and timezone are reset
    formModel.value.activationPeriodStart = null
    formModel.value.activationPeriodEnd = null
    formModel.value.redemptionPeriodStart = null
    formModel.value.redemptionPeriodEnd = null
    formModel.value.utcOffset = null
  },
})
</script>

<template>
  <VCol cols="12" md="5" style="width: 40%">
    <VCard class="form-card">
      <VCardTitle class="form-card-title">
        Create New RG PIN list Request
      </VCardTitle>

      <div class="form-wrapper">
        <VCardTitle> 1. Enter RG PIN List Request Info</VCardTitle>
        <VCardText>
          <VForm ref="formRef">
            <VRow class="mt-1">
              <VCol cols="12">
                <AppTextField
                  v-model="formModel.batchName"
                  label="Batch Name*"
                  placeholder="XX_yyyymmddhhmmss"
                  :error-messages="errors.batchName"
                  :rules="[requiredValidator, minLengthValidator]"
                  clearable
                />
              </VCol>

              <VCol cols="12">
                <AppAutocomplete
                  v-model="formModel.productId"
                  label="Product Name"
                  placeholder="Select Product Name"
                  :items="productNameOptions"
                  clearable
                />
              </VCol>

              <VCol cols="12">
                <AppAutocomplete
                  v-model="formModel.razerGoldPINStatus"
                  label="Razer Gold PIN Status"
                  placeholder="Select Razer Gold PIN Status"
                  :items="pinStatusOptions"
                  clearable
                />
              </VCol>

              <VCol cols="12">
                <AppAutocomplete
                  v-model="formModel.redemptionFromId"
                  label="Redemption From"
                  placeholder="Select Redemption From"
                  :items="redemptionFromOptions"
                  clearable
                />
              </VCol>

              <VCol cols="12">
                <AppAutocomplete
                  v-model="formModel.redemptionRegionId"
                  label="Redemption Region"
                  placeholder="Select Redemption Region"
                  :items="redemptionRegionOptions"
                  clearable
                />
              </VCol>

              <VCol cols="12">
                <AppAutocomplete
                  v-model="formModel.redemptionCurrencyCode"
                  label="Redemption Currency"
                  placeholder="Select Redemption Currency"
                  :items="redemptionCurrencyOptions"
                  clearable
                />
              </VCol>

              <VCol cols="12">
                <AppAutocomplete
                  v-model="formModel.pinCardTypeId"
                  label="PIN Card Type"
                  placeholder="Select PIN Card Type"
                  :items="pinCardTypeOptions"
                  clearable
                />
              </VCol>

              <!-- Activation Period -->
              <VCol cols="12">
                <VLabel class="mb-1 text-body-2">Activation Period</VLabel>
                <div class="d-flex align-center">
                  <div class="flex-grow-1">
                    <AppDateTimePicker
                      v-model="formModel.activationPeriodStart"
                      placeholder="Activated From"
                      :config="{
                        enableTime: true,
                        dateFormat: 'Y-m-d H:i:S',
                        disable: [
                          function (date) {
                            if (formModel.activationPeriodEnd) {
                              return (
                                date >= new Date(formModel.activationPeriodEnd)
                              )
                            }
                            return false
                          },
                        ],
                      }"
                      prepend-inner-icon="mdi-calendar-clock"
                      clearable
                    />
                  </div>
                  <div class="mx-1">
                    <VIcon icon="mdi-arrow-right-thin" size="x-small" />
                  </div>
                  <div class="flex-grow-1">
                    <AppDateTimePicker
                      v-model="formModel.activationPeriodEnd"
                      placeholder="Activated Till"
                      :config="{
                        enableTime: true,
                        dateFormat: 'Y-m-d H:i:S',
                        disable: [
                          function (date) {
                            if (formModel.activationPeriodStart) {
                              return (
                                date <=
                                new Date(formModel.activationPeriodStart)
                              )
                            }
                            return false
                          },
                        ],
                      }"
                      prepend-inner-icon="mdi-calendar-clock"
                      clearable
                    />
                  </div>
                </div>
              </VCol>

              <!-- Redemption Period -->
              <VCol cols="12">
                <VLabel class="mb-1 text-body-2">Redemption Period</VLabel>
                <div class="d-flex align-center">
                  <div class="flex-grow-1">
                    <AppDateTimePicker
                      v-model="formModel.redemptionPeriodStart"
                      placeholder="Redeem From"
                      :config="{
                        enableTime: true,
                        dateFormat: 'Y-m-d H:i:S',
                        disable: [
                          function (date) {
                            if (formModel.redemptionPeriodEnd) {
                              return (
                                date >= new Date(formModel.redemptionPeriodEnd)
                              )
                            }
                            return false
                          },
                        ],
                      }"
                      prepend-inner-icon="mdi-calendar-clock"
                      clearable
                    />
                  </div>
                  <div class="mx-1">
                    <VIcon icon="mdi-arrow-right-thin" size="x-small" />
                  </div>
                  <div class="flex-grow-1">
                    <AppDateTimePicker
                      v-model="formModel.redemptionPeriodEnd"
                      placeholder="Redeem Till"
                      :config="{
                        enableTime: true,
                        dateFormat: 'Y-m-d H:i:S',
                        disable: [
                          function (date) {
                            if (formModel.redemptionPeriodStart) {
                              return (
                                date <=
                                new Date(formModel.redemptionPeriodStart)
                              )
                            }
                            return false
                          },
                        ],
                      }"
                      prepend-inner-icon="mdi-calendar-clock"
                      clearable
                    />
                  </div>
                </div>
              </VCol>

              <VCol cols="12">
                <AppAutocomplete
                  v-model="formModel.utcOffset"
                  label="Activation & Redemption Timezone"
                  placeholder="Select Timezone"
                  :items="timezoneOptions"
                  clearable
                />
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </div>

      <!-- Action buttons -->
      <div class="d-flex justify-end gap-4 px-4 pt-4 pb-4">
        <VBtn
          variant="tonal"
          color="secondary"
          @click="handleCancel"
          :disabled="props.loading"
        >
          CANCEL
        </VBtn>
        <VBtn color="primary" @click="handleSubmit" :loading="props.loading">
          SUBMIT
        </VBtn>
      </div>
    </VCard>
  </VCol>
</template>

<style lang="scss" scoped>
.form-card {
  background: transparent;
  box-shadow: none;
}

.form-card-title {
  padding-bottom: 16px;
  font-weight: 600;
}

.form-wrapper {
  background: #1e1e1e;
  border-radius: 16px;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  max-height: 70vh;
  margin-bottom: 16px;
}

:deep(.v-field__input) {
  color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
}

:deep(.v-card-text) {
  overflow-y: auto;
}

.v-row {
  row-gap: 8px;
}

:deep(.v-col) {
  padding-top: 4px;
  padding-bottom: 4px;
}
</style>
