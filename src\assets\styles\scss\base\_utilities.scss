@use "@configured-variables" as variables;
@use "@layouts/styles/mixins" as layoutsMixins;

/* 👉 Demo spacers */

/* TODO: Use vuetify SCSS variable here; */
$card-spacer-content: 16px;

.demo-space-x {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-block-start: -$card-spacer-content;

  & > * {
    margin-block-start: $card-spacer-content;
    margin-inline-end: $card-spacer-content;
  }
}

.demo-space-y {
  & > * {
    margin-block-end: $card-spacer-content;

    &:last-child {
      margin-block-end: 0;
    }
  }
}

// 👉 Card match height
.match-height.v-row {
  .v-card {
    block-size: 100%;
  }
}

// 👉 Whitespace
.whitespace-no-wrap {
  white-space: nowrap;
}

// 👉 Colors

/*
  ℹ️ Vuetify is applying `.text-white` class to badge icon but don't provide its styles
  Moreover, we also use this class in some places

  ℹ️ In vuetify 2 with `$color-pack: false` SCSS var config this class was getting generated but this is not the case in v3

  ℹ️ We also need !important to get correct color in badge icon
*/
.text-white {
  color: #fff !important;
}

.text-white-variant {
  color: rgba(211, 212, 220);
}

.text-link {
  &:not(:hover) {
    color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
  }
}

.text-link {
  &:not(:hover) {
    color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
  }
}

.bg-var-theme-background {
  background-color: rgba(var(--v-theme-on-background), var(--v-hover-opacity)) !important;
}

.bg-global-primary {
  background-color: rgb(var(--v-global-theme-primary)) !important;
  color: rgb(var(--v-theme-on-primary)) !important;
}

// [/^bg-light-(\w+)$/, ([, w]) => ({ backgroundColor: `rgba(var(--v-theme-${w}), var(--v-activated-opacity))` })],
@each $color-name in variables.$theme-colors-name {
  .bg-light-#{$color-name} {
    background-color: rgba(var(--v-theme-#{$color-name}), var(--v-activated-opacity)) !important;
  }
}

// 👉 clamp text
.clamp-text {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
}

.custom-badge {
  .v-badge__badge {
    border-radius: 6px !important;
    block-size: 12px !important;
    inline-size: 12px !important;
  }
}

.leading-normal {
  line-height: normal !important;
}

// 👉 for rtl only
.flip-in-rtl {
  @include layoutsMixins.rtl {
    transform: scaleX(-1);
  }
}

// 👉 Carousel
.carousel-delimiter-top-end {
  .v-carousel__controls {
    justify-content: end;
    block-size: 40px;
    inset-block-start: 0;
    padding-inline: 1rem;

    .v-btn--icon.v-btn--density-default {
      block-size: calc(var(--v-btn-height) + -10px);
      inline-size: calc(var(--v-btn-height) + -8px);

      &.v-btn--active {
        color: #fff;
      }

      .v-btn__overlay {
        opacity: 0;
      }

      .v-ripple__container {
        display: none;
      }

      .v-btn__content {
        .v-icon {
          block-size: 8px !important;
          font-size: 8px !important;
          inline-size: 8px !important;
        }
      }
    }
  }

  @each $color-name in variables.$theme-colors-name {

    &.dots-active-#{$color-name} {
      .v-carousel__controls {
        .v-btn--active {
          color: rgb(var(--v-theme-#{$color-name})) !important;
        }
      }
    }
  }
}

.v-timeline-item {
  .app-timeline-title {
    color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
    font-size: 16px;
    font-weight: 500;
    line-height: 1.3125rem;
  }

  .app-timeline-meta {
    color: rgba(var(--v-theme-on-surface), var(--v-disabled-opacity));
    font-size: 12px;
    line-height: 0.875rem;
  }

  .app-timeline-text {
    color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
    font-size: 14px;
    line-height: 1.25rem;
  }

  .timeline-chip {
    border-radius: 6px;
    background: rgba(var(--v-theme-on-surface), var(--v-hover-opacity));
    padding-block: 5px;
    padding-inline: 10px;
  }
}
