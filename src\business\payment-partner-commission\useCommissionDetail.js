export const useCommissionDetail = (commissionSchemeId) => {
  const commissionDetail = ref({});
  const commissionId = ref(commissionSchemeId);

  const getCommissionDetail = async () => {
    if (isEmpty(commissionId.value)) return;
    const res = await $api(`/api/admin-api/v1/commission-scheme/detail`, {
      method: "GET",
      query: {
        commissionSchemeId: commissionId.value, 
      },
    });
    commissionDetail.value = res.data
  };

  const updateCommissionId = async (newCommissionId) => {
    commissionId.value = newCommissionId;
    await getCommissionDetail();
  };

  onMounted(() => {
    getCommissionDetail();
  });

  return {
    commissionDetail,
    updateCommissionId,
  };
};
