<script setup>
import { useRouter } from 'vue-router'
import UserGroupForm from '@/business/user/group/form.vue'
definePage({
  meta: {
    navActiveLink: 'user-group',
    breadcrumb: 'Create New User Group',
  },
})

const router = useRouter()
const userGroupFormRef = ref(null)
const isSaving = ref(false)

// form data
const formModel = ref({
  name: '',
  description: '',
  statusId: 1
})

const handleCancel = () => {
  userGroupFormRef.value?.resetForm()
  router.back()
}

const handleSave = async () => {
  if (isSaving.value) return
  // validate form
  const { valid } = await userGroupFormRef.value?.validate()

  if (!valid) return
  try {
    isSaving.value = true

    const params = userGroupFormRef.value?.getParams()
   
    await $api('api/admin-api/v1/user-group', {
    method: 'POST',
    body: params
    })
    message.success('User Group created successfully')
    router.back()
  } finally {
    isSaving.value = false
  }
}
</script>

<template>
  <VCard>
    <VCardItem class="pb-4 px-0">
      <VCardTitle>
        Create New User Group
      </VCardTitle>
    </VCardItem>
    
    <UserGroupForm
      ref="userGroupFormRef"
      v-model="formModel"
      type="add"
    />
    
    <VRow class="d-flex mt-3">
      <VCol cols="5" class="d-flex justify-end">
        <VBtn variant="outlined" color="default" @click="handleCancel">
          CANCEL
        </VBtn>
        <VBtn color="primary" class="ml-2" style="width: 120px;" data-testid="save-button" @click="handleSave">
          SAVE
        </VBtn>
      </VCol>
    </VRow>
  </VCard>
</template>
