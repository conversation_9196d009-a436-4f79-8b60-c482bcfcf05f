<template>
  <div class="preview-container">
    <!-- 预览工具栏 -->
    <VAppBar class="preview-toolbar" elevation="1">
      <VToolbarTitle class="d-flex align-center">
        <VIcon class="mr-2" color="info">mdi-eye</VIcon>
        Page Preview
        <VChip 
          v-if="currentPage"
          size="small" 
          class="ml-4"
          :color="currentPage.active ? 'success' : 'warning'"
        >
          {{ currentPage.pageName }}
        </VChip>
      </VToolbarTitle>
      
      <VSpacer />
      
      <!-- 设备模式切换 -->
      <VBtnToggle
        v-model="deviceMode"
        variant="outlined"
        density="compact"
        class="mr-4"
      >
        <VBtn icon value="desktop">
          <VIcon>mdi-monitor</VIcon>
        </VBtn>
        <VBtn icon value="tablet">
          <VIcon>mdi-tablet</VIcon>
        </VBtn>
        <VBtn icon value="mobile">
          <VIcon>mdi-cellphone</VIcon>
        </VBtn>
      </VBtnToggle>
      
      <!-- 预览模式切换 -->
      <VBtnToggle
        v-model="previewMode"
        variant="outlined"
        density="compact"
        class="mr-4"
      >
        <VBtn value="preview">
          <VIcon class="mr-1">mdi-eye</VIcon>
          Preview
        </VBtn>
        <VBtn value="code">
          <VIcon class="mr-1">mdi-code-json</VIcon>
          Code
        </VBtn>
      </VBtnToggle>
      
      <!-- 操作按钮 -->
      <VBtn 
        color="primary" 
        @click="editPage"
        class="mr-2"
      >
        <VIcon class="mr-2">mdi-pencil</VIcon>
        Edit
      </VBtn>
      
      <VBtn 
        variant="outlined" 
        @click="refreshPreview"
        :loading="loading"
      >
        <VIcon class="mr-2">mdi-refresh</VIcon>
        Refresh
      </VBtn>
    </VAppBar>

    <!-- 预览内容区 -->
    <div class="preview-content">
      <!-- 页面预览模式 -->
      <div v-if="previewMode === 'preview'" class="preview-viewport">
        <div 
          class="device-frame"
          :class="`device-${deviceMode}`"
        >
          <div class="device-screen">
            <!-- 渲染预览页面 -->
            <div v-if="currentPageMeta" class="rendered-page">
              <LowCodeRenderer
                :meta="currentPageMeta"
                :preview-mode="true"
                @component-click="handleComponentClick"
              />
            </div>
            
            <!-- 空状态 -->
            <div v-else class="empty-preview">
              <VIcon size="64" color="grey-lighten-1" class="mb-4">
                mdi-file-document-outline
              </VIcon>
              <h3 class="text-h6 mb-2">No Page to Preview</h3>
              <p class="text-body-2 text-grey-darken-1 mb-4">
                Select a page to preview or create a new one
              </p>
              <VBtn color="primary" @click="$router.push('/low-code-engine/editor')">
                Go to Editor
              </VBtn>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 代码查看模式 -->
      <div v-else class="code-view">
        <VCard>
          <VCardTitle class="d-flex justify-space-between align-center">
            <span>Page Configuration</span>
            <VBtn
              size="small"
              variant="outlined"
              @click="copyCode"
            >
              <VIcon class="mr-1">mdi-content-copy</VIcon>
              Copy
            </VBtn>
          </VCardTitle>
          
          <VCardText>
            <pre class="code-block">{{ formattedPageMeta }}</pre>
          </VCardText>
        </VCard>
      </div>
    </div>

    <!-- 组件信息抽屉 -->
    <VNavigationDrawer
      v-model="componentInfoDrawer"
      location="right"
      temporary
      width="400"
    >
      <VToolbar density="compact">
        <VToolbarTitle>Component Info</VToolbarTitle>
        <VBtn icon @click="componentInfoDrawer = false">
          <VIcon>mdi-close</VIcon>
        </VBtn>
      </VToolbar>
      
      <VContainer v-if="selectedComponent">
        <h4 class="text-h6 mb-3">{{ selectedComponent.type }}</h4>
        
        <VExpansionPanels>
          <VExpansionPanel title="Properties">
            <VExpansionPanelText>
              <pre class="component-props">{{ JSON.stringify(selectedComponent.props, null, 2) }}</pre>
            </VExpansionPanelText>
          </VExpansionPanel>
          
          <VExpansionPanel title="Events" v-if="selectedComponent.events">
            <VExpansionPanelText>
              <pre class="component-props">{{ JSON.stringify(selectedComponent.events, null, 2) }}</pre>
            </VExpansionPanelText>
          </VExpansionPanel>
          
          <VExpansionPanel title="Children" v-if="selectedComponent.children?.length">
            <VExpansionPanelText>
              <div>{{ selectedComponent.children.length }} child components</div>
            </VExpansionPanelText>
          </VExpansionPanel>
        </VExpansionPanels>
      </VContainer>
    </VNavigationDrawer>

    <!-- 页面选择对话框 -->
    <VDialog v-model="pageSelectDialog" max-width="600">
      <VCard>
        <VCardTitle>Select Page to Preview</VCardTitle>
        
        <VCardText>
          <VList>
            <VListItem
              v-for="page in availablePages"
              :key="page.pageName"
              @click="loadPreviewPage(page.pageName)"
            >
              <VListItemTitle>{{ page.pageName }}</VListItemTitle>
              <VListItemSubtitle>
                {{ page.active ? 'Active' : 'Inactive' }} • 
                Created: {{ formatDate(page.createdDateTime) }}
              </VListItemSubtitle>
              
              <template #append>
                <VChip 
                  size="small"
                  :color="page.active ? 'success' : 'warning'"
                >
                  {{ page.active ? 'Active' : 'Inactive' }}
                </VChip>
              </template>
            </VListItem>
          </VList>
        </VCardText>
        
        <VCardActions>
          <VSpacer />
          <VBtn @click="pageSelectDialog = false">Cancel</VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useLowCodeAPI } from '@/business/low-code-engine/api/useLowCodeAPI'
import LowCodeRenderer from '@/business/low-code-engine/renderer'

definePage({
  alias: '/low-code-engine/preview',
  name: 'low-code-engine-preview',
  meta: {
    layout: 'blank',
    public: true,
  },
})

// ===== 状态 =====
const router = useRouter()
const route = useRoute()
const { queryPage, listActivePages } = useLowCodeAPI()

const loading = ref(false)
const deviceMode = ref('desktop')
const previewMode = ref('preview')
const componentInfoDrawer = ref(false)
const pageSelectDialog = ref(false)

const currentPage = ref(null)
const currentPageMeta = ref(null)
const selectedComponent = ref(null)
const availablePages = ref([])

// ===== 计算属性 =====
const formattedPageMeta = computed(() => {
  if (!currentPageMeta.value) return ''
  return JSON.stringify(currentPageMeta.value, null, 2)
})

// ===== 生命周期 =====
onMounted(async () => {
  await loadAvailablePages()
  
  // 检查URL参数中的页面名称
  const pageName = route.query.page
  if (pageName) {
    await loadPreviewPage(pageName)
  } else {
    // 如果没有指定页面，显示页面选择对话框
    pageSelectDialog.value = true
  }
})

// 监听路由变化
watch(() => route.query.page, async (newPageName) => {
  if (newPageName) {
    await loadPreviewPage(newPageName)
  }
})

// ===== 方法 =====

/**
 * 加载可用页面列表
 */
async function loadAvailablePages() {
  try {
    const response = await listActivePages()
    availablePages.value = response.data || []
  } catch (error) {
    console.error('加载页面列表失败:', error)
  }
}

/**
 * 加载预览页面
 */
async function loadPreviewPage(pageName) {
  if (!pageName) return
  
  loading.value = true
  try {
    const response = await queryPage(pageName)
    currentPage.value = response.data
    
    if (response.data.layout) {
      currentPageMeta.value = JSON.parse(response.data.layout)
    } else {
      // 如果没有布局数据，生成默认预览内容
      currentPageMeta.value = generateDefaultPreview(pageName)
    }
    
    pageSelectDialog.value = false
  } catch (error) {
    console.error('加载预览页面失败:', error)
    currentPageMeta.value = generateErrorPreview(error.message)
  } finally {
    loading.value = false
  }
}

/**
 * 生成默认预览内容
 */
function generateDefaultPreview(pageName) {
  return {
    type: 'page',
    name: pageName,
    title: pageName,
    components: [
      {
        type: 'VCard',
        props: {
          class: 'ma-4'
        },
        children: [
          {
            type: 'VCardTitle',
            props: {},
            children: [`Preview: ${pageName}`]
          },
          {
            type: 'VCardText',
            props: {},
            children: [
              {
                type: 'VAlert',
                props: {
                  type: 'info',
                  class: 'mb-4'
                },
                children: ['This page has no layout configuration yet. Go to the editor to design this page.']
              },
              {
                type: 'VBtn',
                props: {
                  color: 'primary',
                  onClick: () => editPage()
                },
                children: ['Edit This Page']
              }
            ]
          }
        ]
      }
    ]
  }
}

/**
 * 生成错误预览内容
 */
function generateErrorPreview(errorMessage) {
  return {
    type: 'page',
    name: 'error',
    title: 'Preview Error',
    components: [
      {
        type: 'VAlert',
        props: {
          type: 'error',
          class: 'ma-4'
        },
        children: [`Failed to load page: ${errorMessage}`]
      }
    ]
  }
}

/**
 * 刷新预览
 */
async function refreshPreview() {
  if (currentPage.value) {
    await loadPreviewPage(currentPage.value.pageName)
  }
}

/**
 * 编辑页面
 */
function editPage() {
  if (currentPage.value) {
    router.push(`/low-code-engine/editor?page=${currentPage.value.pageName}`)
  } else {
    router.push('/low-code-engine/editor')
  }
}

/**
 * 处理组件点击
 */
function handleComponentClick(component) {
  selectedComponent.value = component
  componentInfoDrawer.value = true
}

/**
 * 复制代码
 */
async function copyCode() {
  try {
    await navigator.clipboard.writeText(formattedPageMeta.value)
    // 这里可以添加成功提示
    console.log('代码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
  }
}

/**
 * 格式化日期
 */
function formatDate(dateString) {
  if (!dateString) return 'Unknown'
  
  try {
    return new Date(dateString).toLocaleDateString()
  } catch {
    return dateString
  }
}
</script>

<style scoped>
.preview-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  flex-shrink: 0;
}

.preview-content {
  flex: 1;
  overflow: hidden;
  background: #f5f5f5;
}

.preview-viewport {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.device-frame {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.device-desktop {
  width: 100%;
  max-width: 1200px;
  height: calc(100vh - 140px);
}

.device-tablet {
  width: 768px;
  height: 1024px;
  max-height: calc(100vh - 140px);
}

.device-mobile {
  width: 375px;
  height: 667px;
  max-height: calc(100vh - 140px);
}

.device-screen {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.rendered-page {
  min-height: 100%;
  background: white;
}

.empty-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 40px;
}

.code-view {
  height: 100%;
  padding: 20px;
}

.code-block {
  background: #1e1e1e;
  color: #d4d4d4;
  padding: 20px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
  max-height: calc(100vh - 300px);
}

.component-props {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-desktop,
  .device-tablet {
    width: 100%;
    height: calc(100vh - 140px);
  }
  
  .device-mobile {
    width: 100%;
    max-width: 375px;
    height: calc(100vh - 140px);
  }
}

/* 预览模式下隐藏一些交互元素 */
:deep(.rendered-page .v-btn--variant-elevated) {
  pointer-events: none;
}

:deep(.rendered-page .v-input) {
  pointer-events: none;
}

/* 高亮点击的组件 */
:deep(.rendered-page .component-clickable) {
  cursor: pointer;
  transition: all 0.2s ease;
}

:deep(.rendered-page .component-clickable:hover) {
  outline: 2px solid rgb(var(--v-theme-primary));
  outline-offset: 2px;
}
</style>
