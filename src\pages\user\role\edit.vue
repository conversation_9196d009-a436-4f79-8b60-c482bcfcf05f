<script setup>
import RoleForm from '@/business/user/role/form.vue'
import { $api } from '@/utils/request';
import { treeToList } from '@/utils/helpers';
import { watch } from 'vue';
import { useRouter, useRoute } from 'vue-router'

definePage({
  meta: {
    navActiveLink: 'user-role',
    breadcrumb: 'Edit Role & Assign Permissions',
  },
})


const { userGroupList } = useGroup()
const route = useRoute();
const router = useRouter();
const roleId = route.query.id;
const statusId = ref(null)
const isSaving = ref(false)
const formRef = ref(null)

// form data
const formModel = reactive({
  id: roleId,
  name: '',
  userGroupId: '',
  description: '',
  assignPermission: [],
  statusId: 1
})

const handleCancel = () => {
  formRef.value?.resetForm()
  router.back()
}

const handleSave = async () => {
  try {
    // validate form
    const { valid, errors } = await formRef.value?.validate()
    if (!valid) return;
    if (isSaving.value) return;
    const params = formRef.value.getParams()
    const res = await $api('/api/admin-api/v1/role', {
      method: 'PUT',
      body: params
    })
    message.success('Update role successfully')
    router.back()
  } finally {
    isSaving.value = false
  }
}



const getRoleDetail = async () => {
  const res = await $api(`/api/admin-api/v1/role/${roleId}`, {
    method: 'GET',
  })
  const data = res.data
  formModel.name = data.name
  formModel.description = data.description
  formModel.userGroupId = data.userGroupId
  formModel.statusId = data.statusId

  userGroupList.value.forEach(item => {
    if (item.id === data.userGroupId) {
      formModel.userGroupName = item.name;
      formModel.userGroupIdList = item.name;
    }
  });
}

watch([roleId, userGroupList], () => {
  getRoleDetail()

}, { immediate: true })
</script>

<template>
  <VCard>
    <VCardItem class="pb-4 px-0">
      <VCardTitle>
        Edit Role & Assign Permissions
      </VCardTitle>
    </VCardItem>

    <RoleForm ref="formRef" v-model="formModel" type="edit" />

    <VRow class="d-flex mt-3">
      <VCol cols="12" class="d-flex justify-end">
        <VBtn variant="outlined" color="default" @click="handleCancel">
          CANCEL
        </VBtn>
        <VBtn color="primary" class="ml-2" style="width: 120px;" @click="handleSave">
          SAVE
        </VBtn>
      </VCol>
    </VRow>
  </VCard>
</template>
