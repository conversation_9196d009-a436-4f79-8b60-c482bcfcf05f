<script setup>
import { ref, watch, nextTick, onBeforeUnmount, onMounted } from "vue";
import AppDateTimePicker from "./AppDateTimePicker.vue";

const props = defineProps({
  modelValue: [String, Date],
  enableSeconds: {
    type: Boolean,
    default: false,
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  buttonText: {
    type: String,
    default: "APPLY EFFECTIVE DATE & TIME",
  },
});

const emit = defineEmits(["update:modelValue", "apply", "cancel"]);

// Save temporarily selected date time
const tempValue = ref(props.modelValue || new Date());
// Whether date picker is shown
const isPickerOpen = ref(false);
// Date picker reference
const pickerRef = ref(null);
// 跟踪组件是否已卸载
const isComponentMounted = ref(false);

// 监听属性变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (isComponentMounted.value) {
      tempValue.value = newVal || new Date();
    }
  }
);

// 组件挂载时设置标志
onMounted(() => {
  isComponentMounted.value = true;
});

// 组件卸载时清理
onBeforeUnmount(() => {
  isComponentMounted.value = false;
  window.removeEventListener("scroll", updatePickerPosition, true);
  window.removeEventListener("resize", updatePickerPosition);
});

// Handle apply button click
const handleApply = () => {
  emit("update:modelValue", tempValue.value);
  emit("apply", tempValue.value);
  isPickerOpen.value = false;
};

// Handle cancel button click
const handleCancel = () => {
  tempValue.value = props.modelValue || new Date();
  emit("cancel");
  isPickerOpen.value = false;
};

// Date selection changes but not immediately submitted
const handleDateChange = (val) => {
  tempValue.value = val;
};

// Add function to update picker position
const updatePickerPosition = () => {
  if (!isComponentMounted.value || !isPickerOpen.value) return;

  try {
    const button = document.querySelector(".date-time-button");
    const pickerContainer = document.querySelector(".picker-container");

    if (!button || !pickerContainer) return;

    const buttonRect = button.getBoundingClientRect();

    // Set picker position, following button
    pickerContainer.style.top = `${buttonRect.bottom + 5}px`;
    pickerContainer.style.left = `${buttonRect.left}px`;

    // Check if it will exceed right boundary
    const rightEdge = buttonRect.left + pickerContainer.offsetWidth;
    if (rightEdge > window.innerWidth) {
      pickerContainer.style.left = `${
        window.innerWidth - pickerContainer.offsetWidth - 10
      }px`;
    }

    // Check if it will exceed bottom boundary
    const bottomEdge = buttonRect.bottom + 5 + pickerContainer.offsetHeight;
    if (bottomEdge > window.innerHeight) {
      pickerContainer.style.top = `${
        buttonRect.top - pickerContainer.offsetHeight - 5
      }px`;
    }
  } catch (error) {
    console.warn('Error in updatePickerPosition:', error);
  }
};

// Toggle date picker display state
const togglePicker = () => {
  if (!isComponentMounted.value) return;
  
  isPickerOpen.value = !isPickerOpen.value;

  if (isPickerOpen.value) {
    // Add scroll and resize event listeners
    window.addEventListener("scroll", updatePickerPosition, true);
    window.addEventListener("resize", updatePickerPosition);

    nextTick(() => {
      if (!isComponentMounted.value) return;

      const pickerElement = pickerRef.value?.$el;
      if (!pickerElement) return;

      try {
        updatePickerPosition();

        const calendar = pickerElement.querySelector(".flatpickr-calendar");
        if (calendar) {
          calendar.style.display = "block";
          calendar.style.visibility = "visible";
          calendar.style.opacity = "1";
          calendar.style.zIndex = "10000";

          // 使用 requestAnimationFrame 确保DOM更新完成后再执行
          if (isComponentMounted.value) {
            requestAnimationFrame(() => {
              if (isComponentMounted.value) {
                enhanceYearDropdown(calendar);
              }
            });
          }

          // Ensure time picker is visible and enhanced
          const timePicker = pickerElement.querySelector(".flatpickr-time");
          if (timePicker) {
            timePicker.style.display = "flex";
            timePicker.style.visibility = "visible";
            timePicker.style.opacity = "1";

            // Add clock icon
            if (!timePicker.querySelector(".time-icon")) {
              const clockIcon = document.createElement("div");
              clockIcon.className = "time-icon";
              clockIcon.innerHTML =
                '<i class="mdi mdi-clock-time-three-outline"></i>';
              timePicker.insertBefore(clockIcon, timePicker.firstChild);
            }

            // Ensure time input boxes are visible
            const timeInputs = timePicker.querySelectorAll(".numInputWrapper");
            timeInputs.forEach((input) => {
              input.style.display = "inline-block";
              input.style.visibility = "visible";
            });

            // Handle second input box - disable seconds control
            if (!props.enableSeconds) {
              // Delay execution to ensure DOM is fully rendered
              setTimeout(() => {
                // Try multiple possible selectors
                const secondInputWrappers = [
                  timePicker.querySelector(".numInputWrapper:nth-child(5)"),
                  timePicker.querySelector(".numInputWrapper:last-child"),
                  timePicker.querySelectorAll(".numInputWrapper")[2], // Third input box (if exists)
                  calendar.querySelector(".flatpickr-second")?.parentNode, // Direct class search
                ].filter(Boolean);

                // Process each possible second input box found
                secondInputWrappers.forEach((wrapper) => {
                  console.log("Found second input wrapper:", wrapper);

                  // Try to find input box
                  const secondInput =
                    wrapper.querySelector("input") ||
                    wrapper.querySelector(".flatpickr-second");

                  if (secondInput) {
                    // Set to 00 and disable
                    secondInput.value = "00";
                    secondInput.disabled = true;
                    secondInput.readOnly = true;
                    secondInput.style.opacity = "0.6";
                    secondInput.style.background = "#222222";
                    secondInput.style.cursor = "not-allowed";

                    // Add forced styles
                    secondInput.setAttribute(
                      "style",
                      "background: #222222 !important; " +
                        "opacity: 0.6 !important; " +
                        "cursor: not-allowed !important;"
                    );
                  }

                  // Hide arrows - try multiple selectors
                  const arrows = [
                    ...wrapper.querySelectorAll(".arrowUp, .arrowDown"),
                    ...wrapper.querySelectorAll('span[class*="arrow"]'),
                  ];

                  arrows.forEach((arrow) => {
                    arrow.style.display = "none";
                    arrow.setAttribute("style", "display: none !important;");
                  });

                  // Add disabled style class
                  wrapper.classList.add("disabled-seconds");

                  // Force apply inline styles
                  wrapper.setAttribute("style", "opacity: 0.6 !important;");
                });
              }, 200); // Increase delay to ensure DOM is loaded
            }
          }

          // Ensure action buttons are visible
          const actionsContainer = document.querySelector(
            ".actions-container.bottom-actions"
          );
          if (actionsContainer) {
            actionsContainer.style.display = "flex";
            actionsContainer.style.visibility = "visible";
            actionsContainer.style.opacity = "1";
          }
        }
      } catch (error) {
        console.warn('Error in togglePicker:', error);
      }
    });
  } else {
    window.removeEventListener("scroll", updatePickerPosition, true);
    window.removeEventListener("resize", updatePickerPosition);
  }
};

// 增强年份下拉选择功能
const enhanceYearDropdown = (calendar) => {
  if (!calendar || !isComponentMounted.value) return;
  
  // 防止重复增强
  if (calendar.querySelector('.year-dropdown')) return;
  
  const yearInput = calendar.querySelector('.numInput.cur-year');
  if (!yearInput) return;

  try {
    // 确保年份输入框可点击
    yearInput.style.cursor = 'pointer';
    yearInput.readOnly = true; // 禁止手动输入
    
    // 创建年份下拉菜单
    const currentYear = new Date().getFullYear();
    const minYear = currentYear - 40;
    const maxYear = currentYear + 10;
    
    // 创建新的下拉菜单
    const yearDropdown = document.createElement('select');
    yearDropdown.className = 'flatpickr-monthDropdown-months year-dropdown';
    
    // 添加年份选项
    for (let year = maxYear; year >= minYear; year--) {
      const option = document.createElement('option');
      option.value = year;
      option.textContent = year;
      option.className = 'flatpickr-monthDropdown-month';
      yearDropdown.appendChild(option);
    }
    
    // 设置当前年份为选中状态
    const fp = calendar._flatpickr;
    if (!fp) return;

    const selectedDate = fp.selectedDates?.[0];
    yearDropdown.value = selectedDate ? selectedDate.getFullYear() : currentYear;
    
    // 替换原有的年份输入框
    const yearInputParent = yearInput.parentNode;
    if (!yearInputParent) return;

    yearInputParent.replaceChild(yearDropdown, yearInput);
    
    // 添加下拉菜单选择事件，使用防抖处理
    let updateTimeout;
    const handleYearChange = (e) => {
      if (!isComponentMounted.value) {
        yearDropdown.removeEventListener('change', handleYearChange);
        return;
      }

      const selectedYear = parseInt(e.target.value);
      if (isNaN(selectedYear) || !fp) return;
      
      // 清除之前的定时器
      clearTimeout(updateTimeout);
      
      // 延迟执行更新
      updateTimeout = setTimeout(() => {
        try {
          if (!isComponentMounted.value) return;
          const currentDate = fp.selectedDates[0] || new Date();
          const newDate = new Date(currentDate);
          newDate.setFullYear(selectedYear);
          fp.setDate(newDate, true);
        } catch (error) {
          console.warn('Failed to update year:', error);
        }
      }, 0);
    };

    yearDropdown.addEventListener('change', handleYearChange);
  } catch (error) {
    console.warn('Error in enhanceYearDropdown:', error);
  }
};
</script>

<template>
  <div class="app-date-time-picker-with-actions">
    <button @click="togglePicker" class="date-time-button">
      <i class="mdi mdi-calendar-clock button-icon"></i>
      {{ buttonText }}
    </button>

    <Teleport to="body">
      <VOverlay
        v-model="isPickerOpen"
        scroll-strategy="close"
        :scrim="true"
        persistent
      >
        <div class="picker-container">
          <AppDateTimePicker
            ref="pickerRef"
            v-model="tempValue"
            :enableSeconds="enableSeconds"
            :config="{
              ...config,
              inline: true,
              static: true,
              enableTime: true,
              time_24hr: true,
              defaultDate: tempValue || new Date(),
              enableSeconds: props.enableSeconds,
              appendTo: undefined,
              clickOpens: true,
              yearDropdown: true,
              yearRange: `${new Date().getFullYear() - 40}:${new Date().getFullYear() + 10}`,
            }"
            @update:modelValue="handleDateChange"
            class="custom-date-picker"
          />

          <div class="actions-container bottom-actions">
            <div class="action-buttons">
              <button class="cancel-button" @click="handleCancel">
                CANCEL
              </button>
              <button class="apply-button text-primary" @click="handleApply">
                APPLY
              </button>
            </div>
          </div>
        </div>
      </VOverlay>
    </Teleport>
  </div>
</template>

<style scoped>
.app-date-time-picker-with-actions {
  position: relative;
  isolation: isolate;
}
:deep(.flatpickr-wrapper) {
  width: 100%;
}

.date-time-button {
  display: flex;
  align-items: center;
  background-color: #42424a;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.date-time-button:hover {
  background-color: #515158;
}

.button-icon {
  margin-right: 8px;
  font-size: 18px;
}

.picker-container {
  position: relative;
  background-color: #1e1e1e;
  border-radius: 4px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  width: 328px !important;
  display: block !important;
  overflow: visible !important;
}

.actions-container.bottom-actions {
  display: flex !important;
  padding: 12px 16px !important;
  border-top: 1px solid rgba(255, 255, 255, 0.08) !important;
  background-color: #1e1e1e !important;
  width: 100% !important;
  position: relative !important;
  z-index: 101 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.cancel-button,
.apply-button {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  min-width: 80px !important;
  text-align: center !important;
}

/* Custom date picker styles */
:deep(.custom-date-picker) {
  border-radius: 4px 4px 0 0;
}

/* Adjust calendar styles */
:deep(.flatpickr-calendar) {
  position: static !important;
  visibility: visible !important;
  opacity: 1 !important;
  display: block !important;
  background-color: #1e1e1e !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  border: none !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  min-height: 300px !important;
  z-index: 10001 !important;
  transform: none !important;
}

/* Force show calendar related elements */
:deep(.flatpickr-rContainer),
:deep(.flatpickr-innerContainer),
:deep(.flatpickr-days) {
  display: block !important;
  width: 100% !important;
  min-width: 100% !important;
  overflow: visible !important;
}

/* Force visible month and calendar selector */
:deep(.flatpickr-month) {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 增强年份和月份下拉选择样式 */
:deep(.flatpickr-current-month .flatpickr-monthDropdown-months) {
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  background-color: transparent !important;
  border: none !important;
  cursor: pointer !important;
  padding: 2px 8px !important;
  border-radius: 4px !important;
  color: rgba(255, 255, 255, 0.87) !important;
  font-size: 0.9375rem !important;
  font-weight: 400 !important;
  transition: background-color 0.2s !important;
  min-width: 80px !important;
  text-align: center !important;
  margin: 0 2px !important;
}

:deep(.flatpickr-current-month .flatpickr-monthDropdown-months:hover) {
  background-color: rgba(255, 255, 255, 0.08) !important;
}

:deep(.flatpickr-current-month select.flatpickr-monthDropdown-months option) {
  background-color: #1e1e1e !important;
  color: rgba(255, 255, 255, 0.87) !important;
  padding: 8px 12px !important;
  text-align: left !important;
}

:deep(.flatpickr-current-month select.flatpickr-monthDropdown-months option:hover) {
  background-color: rgba(255, 255, 255, 0.08) !important;
}

/* 调整月份容器的样式 */
:deep(.flatpickr-months .flatpickr-month) {
  padding: 0.75rem 0.5rem !important;
}

:deep(.flatpickr-current-month) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4px !important;
}

:deep(.flatpickr-current-month > div) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 年份下拉框样式 */
:deep(.flatpickr-current-month .year-dropdown.flatpickr-monthDropdown-months) {
  padding: 2px 4px !important;
  border-radius: 4px !important;
  color: rgba(255, 255, 255, 0.87) !important;
  font-size: 0.9375rem !important;
  font-weight: 400 !important;
  line-height: 1.375rem !important;
  transition: all 0.15s ease-out !important;
  min-width: 80px !important;
  text-align: center !important;
  background-color: #1e1e1e !important;
}

/* 自定义滚动条样式 */
:deep(.flatpickr-current-month .year-dropdown.flatpickr-monthDropdown-months::-webkit-scrollbar) {
  width: 6px !important;
  background-color: transparent !important;
}

:deep(.flatpickr-current-month .year-dropdown.flatpickr-monthDropdown-months::-webkit-scrollbar-track) {
  background: transparent !important;
  border-radius: 3px !important;
}

:deep(.flatpickr-current-month .year-dropdown.flatpickr-monthDropdown-months::-webkit-scrollbar-thumb) {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 3px !important;
}

:deep(.flatpickr-current-month .year-dropdown.flatpickr-monthDropdown-months::-webkit-scrollbar-thumb:hover) {
  background: rgba(255, 255, 255, 0.3) !important;
}

/* 选项样式 */
:deep(.flatpickr-current-month .year-dropdown.flatpickr-monthDropdown-months option) {
  background-color: #1e1e1e !important;
  color: rgba(255, 255, 255, 0.87) !important;
  padding: 4px 12px !important;
}

/* 确保下拉列表背景色正确 */
:deep(select.year-dropdown.flatpickr-monthDropdown-months option) {
  background-color: #1e1e1e !important;
}

/* 移除之前的年份相关样式 */
:deep(.flatpickr-current-month .numInput.cur-year) {
  display: none !important;
}

/* Ensure no hidden parent elements */
:deep(.custom-date-picker) {
  display: block !important;
}

:deep(.custom-date-picker > div) {
  display: block !important;
}

/* Fix weekday title layout */
:deep(.flatpickr-weekdays) {
  width: 100% !important;
  display: flex !important;
  justify-content: space-between !important;
}

:deep(.flatpickr-weekday) {
  flex: 1 !important;
  text-align: center !important;
  font-size: 12px !important;
  font-weight: normal !important;
}

/* Ensure time picker is visible */
:deep(.flatpickr-time) {
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: auto !important;
  line-height: normal !important;
  max-height: 50px !important;
  border-top: 0px solid rgba(255, 255, 255, 0.08) !important;
  visibility: visible !important;
  opacity: 1 !important;
  padding: 0 12px 0 0 !important;
  margin-top: 0 !important;
  margin-bottom: 10px !important;
  z-index: 10001 !important;
  padding-left: 0 !important;
}

/* Ensure calendar bottom displays and positions correctly */
:deep(.flatpickr-innerContainer) {
  margin-bottom: 8px !important;
}

/* Ensure time picker input box styles are consistent */
:deep(.flatpickr-time .numInputWrapper input) {
  height: 32px !important;
  background: #2d2d2d !important;
  border-radius: 4px !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  font-size: 16px !important;
}

/* Add time labels, consistent with AppDateTimePicker */
:deep(.time-labels) {
  display: flex !important;
  padding: 8px 12px 0 50px !important;
  border-top: 1px solid rgba(255, 255, 255, 0.08) !important;
  align-items: center !important;
  justify-content: left !important;
  gap: 20px !important;
  z-index: 10001 !important;
}

:deep(.time-label) {
  flex: 1 !important;
  text-align: left !important;
  font-size: 12px !important;
  color: rgba(255, 255, 255, 0.7) !important;
}

/* Hide input box when expanded */
:deep(.custom-date-picker .v-field) {
  display: none !important;
}

:deep(.custom-date-picker .v-input) {
  display: none !important;
}

:deep(.custom-date-picker .v-input__control) {
  display: none !important;
}

:deep(.custom-date-picker input.flatpickr-input) {
  display: none !important;
}

/* Add global styles to ensure picker is always on top */
:global(.flatpickr-calendar.open) {
  z-index: 10002 !important;
}

/* Clock icon styles */
:deep(.time-icon) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-right: 8px;
}

:deep(.time-icon i) {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.7);
}

/* Adjust time picker margins, ensure icon aligns correctly */
:deep(.flatpickr-time .numInputWrapper:first-child) {
  margin-left: 0 !important;
}

/* Disabled seconds control styles */
:deep(.disabled-seconds) {
  opacity: 0.6 !important;
}

:deep(.disabled-seconds input) {
  background: #222222 !important;
  cursor: not-allowed !important;
}

:deep(.disabled-seconds .arrowUp),
:deep(.disabled-seconds .arrowDown) {
  display: none !important;
}
</style>
