<script setup>
import { VTreeview } from "vuetify/labs/VTreeview";
import AppTree from "@/components/AppTree.vue";
import PermissionList from "@/business/user/role/PermissionList.vue";
import { computed } from "vue";
import { useGroup } from "@/hooks/useGroup";
import { requiredValidator } from "@/utils/validators";
import { useHeight } from "@/hooks/useHeight";

const { userGroupList } = useGroup();

// form data
const formModel = defineModel({
  type: Object,
  required: true,
});

const props = defineProps({
  type: {
    type: String,
    default: "add",
  },
});

const selected = ref([]);
const treeRef = ref(null);
const permissionListRef = ref(null);
const formRef = ref(null);
const isView = computed(() => props.type === "view");
const isEdit = computed(() => props.type === "edit");
const isAdd = computed(() => props.type === "add");

// height calculation logic
const cardRef = ref(null);
const { height: treeviewHeight } = useHeight(cardRef);

// expose methods and data to parent component
defineExpose({
  validate: () => {
    return formRef.value.validate();
  },
  getParams: () => {
    const params = {
      name: formModel.value.name,
      userGroupId: formModel.value.userGroupId,
      description: formModel.value.description,
      statusId: formModel.value.statusId,
      temporaryAccessIdList: permissionListRef.value.getParams().accessIdList,
    };
    if (formModel.value.id) {
      params.id = formModel.value.id;
    }
    return params;
  },
  resetForm: () => {
    formModel.value = {
      name: "",
      userGroupId: "",
      description: "",
      assignPermission: [],
      statusId: 1,
    };
  },
});
const handleSelectUserGroup = function (id) {
  formModel.value.userGroupId = id;
};
</script>

<template>
  <VCard ref="cardRef">
    <VRow class="d-flex">
      <VCol cols="12" md="5" xl="5" style="width: 40%">
        <div class="form-wrapper">
          <VCardTitle> 1. Enter Role’s Info </VCardTitle>
          <VCardText>
            <VForm ref="formRef">
              <VRow>
                <VCol cols="12">
                  <AppAutocomplete
                    v-model="formModel.userGroupIdList"
                    placeholder="Select User Group"
                    label="User Group*"
                    :items="userGroupList"
                    clearable
                    clear-icon="tabler-x"
                    @update:model-value="handleSelectUserGroup"
                    :rules="isAdd || isEdit ? [requiredValidator] : []"
                  />
                </VCol>

                <VCol cols="12">
                  <AppTextField
                    v-model="formModel.name"
                    label="Role Name*"
                    placeholder="Type a role"
                    :rules="isAdd || isEdit ? [requiredValidator] : []"
                  />
                </VCol>

                <VCol cols="12">
                  <AppTextField
                    v-model="formModel.description"
                    label="Description"
                  />
                </VCol>
                <VCol cols="12">
                  <VSwitch
                    v-model="formModel.statusId"
                    label="Enabled"
                    :false-value="0"
                    :true-value="1"
                    color="primary"
                    :readonly="isView"
                  />
                </VCol>
              </VRow>
            </VForm>
          </VCardText>
        </div>
      </VCol>
      <VCol cols="12" md="7" xl="7" style="width: 60%">
        <div class="form-wrapper">
          <VCardTitle> 2. Assign Permissions </VCardTitle>
          <VCardText
            :style="{ maxHeight: treeviewHeight + 'px', overflow: 'auto' }"
          >
            <PermissionList
              ref="permissionListRef"
              v-model="formModel.assignPermission"
            />
          </VCardText>
        </div>
      </VCol>
    </VRow>
  </VCard>
</template>

<style lang="scss" scoped>
.form-wrapper {
  background: #1e1e1e;
  border-radius: 16px;
  height: 100%;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
}

:deep(.v-card-text) {
  padding-bottom: 0px !important;
}

:deep(.v-selection-control) {
  justify-content: flex-end;
  flex-flow: row-reverse;
}

:deep(.v-switch .v-label) {
  padding-inline-end: 10px;
  padding-inline-start: 0px;
}

// ensure button can view in viewport
.v-row:last-child {
  margin-top: auto;
  padding: 16px;
  background: var(--v-theme-surface);
  position: sticky;
  bottom: 0;
  z-index: 1;
}
</style>
