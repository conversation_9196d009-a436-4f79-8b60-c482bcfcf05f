<script setup>
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { PAGINATION_OPTIONS as paginationOptions } from "@/utils/constants";



definePage({
  alias: '/audit-log/user-activities',
})



// 👉 create default query
const createDefaultQuery = () => {
  return {
    accountEmail: "",
    moduleName: "",
    createDateTime: "",
    page: 1,
    size: 10,
    sortBy: null,
    orderBy: null,
  };
};

// 👉 Store
const searchQuery = ref(createDefaultQuery());
const isLoading = ref(false);
const tableData = ref(null);

// Data table options
const itemsPerPage = ref(10);
const page = ref(1);

// 👉 update options
const updateOptions = (options) => {
  const sortBy = options[0]?.key;
  const orderBy = options[0]?.order;
  searchQuery.value.sortBy = sortBy;
  searchQuery.value.orderBy = orderBy;
  searchQuery.value.page = 1;
  page.value = 1;
  loadData();
};

// 👉 Table Headers
const headers = [
  {
    title: "No",
    key: "no",
    sortable: false,
    width: 70,
  },
  {
    title: "Email",
    key: "accountEmail",
  },
  {
    title: "Module",
    key: "moduleName",
  },
  {
    title: "Feature",
    key: "feature",
  },
  {
    title: "Action",
    key: "actionCatalog",
  },
  {
    title: "Audit Content",
    key: "auditContent",
    sortable: false,
  },
  {
    title: "Created On",
    key: "createDateTime",
  },
  {
    title: "Client Ip",
    key: "clientIp",
    sortable: false,
  },
  {
    title: "Client Agent",
    key: "clientAgent",
    sortable: false,
  },
];

// 👉 reset query params
const resetQueryParams = () => {
  searchQuery.value = createDefaultQuery();
};

// 👉 get query params
const getQueryParams = () => {
  const params = {};
  params.page = searchQuery.value.page;
  params.size = searchQuery.value.size;
  params.accountEmail = searchQuery.value.accountEmail;
  params.moduleName = searchQuery.value.moduleName;

  if (searchQuery.value.sortBy && searchQuery.value.orderBy) {
    params.orderItemList = [
      {
        column: searchQuery.value.sortBy || "",
        asc: searchQuery.value.orderBy === "asc",
      },
    ];
  } else {
    params.orderItemList = null;
  }

   // Process creation time
   if (searchQuery.value.createDateTime) {
    const [start, end] = searchQuery.value.createDateTime.split('to')
    if (start) {
      params.createDateTimeStart = dateUtil.format(start, 'YYYY-MM-DDTHH:mm:ss.SSSZ')
      params.createDateTimeEnd = dateUtil.format(start, 'YYYY-MM-DDT23:59:59.SSSZ')
    }
    if (end) {
      params.createDateTimeEnd = dateUtil.format(end, 'YYYY-MM-DDT23:59:59.SSSZ')
    }
  }

  return params;
};

// 👉 load data
const loadData = async () => {
  try {
    isLoading.value = true;
    tableData.value = [];
    const params = getQueryParams();
    const res = await $api("/api/audit-log/v1/audit-log/page-query", {
      method: "POST",

      body: params,
    });
    tableData.value = res.data;
  } catch (error) {
    console.error("Error loading data:", error);
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  loadData();
});

const handleSearch = () => {
  page.value = 1;
  searchQuery.value.page = 1;
  loadData();
};

const handlePageChange = (newPage) => {
  page.value = newPage;
  searchQuery.value.page = newPage;
  loadData();
};

const handleItemsPerPageChange = (newItemsPerPage) => {
  newItemsPerPage = parseInt(newItemsPerPage, 10);
  itemsPerPage.value = newItemsPerPage;
  searchQuery.value.size = newItemsPerPage;
  page.value = 1;
  searchQuery.value.page = 1;
  loadData();
};

// 👉 computed
const auditLogs = computed(() =>
  tableData.value?.records?.map((item, index) => ({
    ...item,
    no: (page.value - 1) * itemsPerPage.value + index + 1,
  }))
);

const totalAuditLogs = computed(() => tableData.value?.total || 0);

const router = useRouter();



// 👉 check if text overflows
const isTextOverflow = (text) => {
  const tempElement = document.createElement('div')
  tempElement.style.visibility = 'hidden'
  tempElement.style.position = 'absolute'
  tempElement.style.width = '365px'
  tempElement.style.whiteSpace = 'nowrap'
  tempElement.textContent = text
  document.body.appendChild(tempElement)
  const isOverflowing = tempElement.scrollWidth > tempElement.clientWidth
  document.body.removeChild(tempElement)
  return isOverflowing
}

const expanded = ref([])

const isExpanded = (item) => {
  return expanded.value.includes(item.id)
}

</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            User Activities
            <VSpacer />
          </div>
        </VCardTitle>
      </VCardItem>

      <VCardText class="px-0">
        <VRow>
          <!-- 👉 Search Email -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField
              v-model="searchQuery.accountEmail"
              placeholder="Search Email"
              clearable
              @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Search Module -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField
              v-model="searchQuery.moduleName"
              placeholder="Search Module"
              clearable
              @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Select Created On -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppDateTimePicker
              v-model="searchQuery.createDateTime"
              :config="{
                mode: 'range',
                enableTime: false,
                dateFormat: 'Y-m-d',
              }"
              placeholder="Select Created On"
              clearable
              @update:model-value="handleSearch"
            />
          </VCol>
        </VRow>
      </VCardText>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer
        :items="auditLogs"
        item-value="id"
        :items-length="totalAuditLogs"
        :headers="headers"
        :loading="isLoading"
        class="text-no-wrap"
        @update:sortBy="updateOptions"
        @click:row="handleRowClick"
        expand-on-click
        v-model:expanded="expanded"
      >
        <!-- Expanded-row -->
        <template #expanded-row="{ item }">
          <tr class="expanded-row">
            <td :colspan="5"></td>
            <td :colspan="1">
              <div class="expanded-content">
                <highlightjs
                  language="json"
                  :code="formatJson(item.auditContent)"
                />    
              </div>
            </td>
            <td :colspan="2"></td>
            <td :colspan="2">
              <!-- {{ item.clientAgent }} -->
            </td>
          </tr>
        </template>        
        
        <!-- Audit Content -->
        <template #[`item.auditContent`]="{ item }">
          <div class="d-flex">
            <div v-if="isTextOverflow(item.auditContent)" class="w-365">
              <VTooltip>
                <template #activator="{ props }">
                  <div class="text-truncate" v-bind="props">
                    {{ item.auditContent }}
                  </div>
                </template>
                <div>
                  {{ item.auditContent }}
                </div>
              </VTooltip>
            </div>
            <div v-else class="text-truncate w-365">
              {{ item.auditContent }}
            </div>
            <VIcon :icon="isExpanded(item) ? 'tabler-chevron-up' : 'tabler-chevron-down'"
            size="20" class="ms-2" color="grey-400" />
          </div>
        </template>

        <!-- Client Agent -->
        <template #[`item.clientAgent`]="{ item }">
          <div v-if="isTextOverflow(item.clientAgent)" class="w-300">
            <VTooltip>
              <template #activator="{ props }">
                <div class="text-truncate" v-bind="props">
                  {{ item.clientAgent }}
                </div>
              </template>
              <div>
                {{ item.clientAgent }}
              </div>
            </VTooltip>
          </div>
          <div v-else class="text-truncate w-300">
            {{ item.clientAgent }}
            <VIcon :icon="isExpanded(item) ? 'tabler-chevron-up' : 'tabler-chevron-down'"
              size="20" class="ms-2" color="grey-400" />
          </div>
        </template>

        <!-- Created Date & Time -->
        <template #[`item.createDateTime`]="{ item }">
          {{ dateUtil.format(item.createDateTime) }}
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination
            :page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalAuditLogs"
            @update:page="handlePageChange"
          >
            <div class="d-flex gap-3">
              <AppSelect
                :model-value="itemsPerPage"
                :items="paginationOptions"
                @update:model-value="handleItemsPerPageChange"
              />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
    <!-- 👉 Add New Audit Log -->
  </section>
</template>


<style lang="scss" scoped>
.expanded-content :deep(.hljs) {
  background: transparent;
}
</style>

