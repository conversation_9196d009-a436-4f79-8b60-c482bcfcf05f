<template>
  <VDialog :model-value="modelValue" max-width="600" @update:model-value="$emit('update:modelValue', $event)">
    <VCard class="dialog-card pt-2 pb-6">
      <VCardTitle>
        <div class="d-flex justify-space-between align-center">
          <span class="text-h5">{{ title }}</span>
          <VBtn icon variant="text" size="small" @click="closeDialog">
            <VIcon>mdi-close</VIcon>
          </VBtn>
        </div>
      </VCardTitle>

      <VCardText class="dialog-content">
        <VForm @submit.prevent="handleSubmit" size="large" ref="form">
          <AppTextField v-model="localFormData.fieldName" label="Field Name" placeholder="e.g.: username"
            :rules="[requiredValidator]" density="compact" variant="outlined" class="mb-6"
            :readonly="mode === 'edit'" />

          <AppTextField v-model="localFormData.displayName" label="Display Name" placeholder="e.g.: Username"
            :rules="[requiredValidator]" density="compact" variant="outlined" class="mb-6" />

          <AppSelect v-model="localFormData.fieldType" label="Field Type" :items="fieldTypes" density="compact"
            @update:model-value="handleFieldTypeChange" variant="outlined" class="mb-6" />

          <VRadioGroup v-model="localFormData.source" label="Source"
            v-if="localFormData.fieldType === FieldTypes.SELECT" inline color="primary" density="compact" class="mb-6">
            <VRadio :value="SourceTypes.LOCAL" label="LOCAL" />
            <VRadio :value="SourceTypes.APP_ENUMS" label="APP" />
            <VRadio :value="SourceTypes.DICT_TABLE" label="DICT" />
          </VRadioGroup>

          <div class="mb-6">
            <VAlert type="error" v-if="typeError" density="compact" variant="outlined">
              {{ typeError }}
            </VAlert>
          </div>

          <div
            v-if="localFormData.fieldType === FieldTypes.SELECT && localFormData.source === SourceTypes.LOCAL && localFormData.items.length > 0"
            class="mb-6">
            <VRow v-for="(item, index) in localFormData.items" :key="index" class="mt-0!">
              <VCol cols="5">
                <AppTextField v-model="item.label" label="Option Label" density="compact" variant="outlined"
                  placeholder="e.g.: Label"
                  :rules="[required]" :error-messages="item.labelError" />
              </VCol>
              <VCol cols="5">
                <AppTextField v-model="item.value" label="Option Value" density="compact" variant="outlined"
                placeholder="e.g.: value"
                  :rules="[required]" :error-messages="item.valueError" />
              </VCol>
              <VCol cols="1">
                <VBtn icon variant="text" class="mt-6" size="small" @click="removeOption(index)">
                  <VIcon color="error">mdi-delete</VIcon>
                </VBtn>
              </VCol>
            </VRow>
          </div>

          <VBtn variant="outlined" size="small" color="default"
            v-if="localFormData.fieldType === FieldTypes.SELECT && localFormData.source === SourceTypes.LOCAL"
            @click="handleAddOption" class="w-100% mb-6 flex align-center">
            <VIcon>mdi-plus</VIcon>
            Add Option
          </VBtn>

          <AppAutocomplete
            v-if="localFormData.fieldType === FieldTypes.SELECT && [SourceTypes.APP_ENUMS, SourceTypes.DICT_TABLE].includes(localFormData.source)"
            v-model="localFormData.type" label="Enum Type" :items="sourceTypeOptions" placeholder="Select Enum Type"
            density="compact" variant="outlined" class="mb-6 mt-4" :rules="[required]" />

          <AppTextField v-model="localFormData.defaultValue" label="Default Value (Optional)"
            placeholder="Enter default value" density="compact" variant="outlined" class="mb-6" />

          <VRow>
            <VCol cols="6">
              <VSwitch v-model="localFormData.required" label="Required" color="primary" density="compact"
                class="flex-1" />
            </VCol>
            <VCol cols="6">
              <VSwitch v-model="localFormData.queryable" label="Queryable" color="primary" density="compact"
                class="flex-1" />
            </VCol>

          </VRow>
        </VForm>
      </VCardText>

      <VCardActions class="px-6 flex gap-2">
        <VBtn variant="outlined" @click="$emit('update:modelValue', false)" class="w-120px">Cancel</VBtn>
        <VBtn color="primary" variant="flat" @click="handleSubmit" :loading="loading" class="w-120px">
          {{ submitButtonText }}
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useLowCodeAPI } from '../api/useLowCodeAPI'
import { requiredValidator } from '@/utils/validators'
import { FieldTypes, SourceTypes } from '../constants'

const props = defineProps({
  modelValue: Boolean,
  mode: { // 'add' or 'edit'
    type: String,
    required: true
  },
  formData: {
    type: Object,
    required: true
  },
  fieldTypes: {
    type: Array,
    required: true
  },
  loading: Boolean
})

const emit = defineEmits(['update:modelValue', 'submit'])

const { getSelectMetadata } = useLowCodeAPI()
const originSelectMetadata = ref({})

const sourceTypeOptions = computed(() => {
  if (localFormData.source === SourceTypes.APP_ENUMS) {
    return originSelectMetadata.value.APP_ENUMS.map(item => ({
      title: item,
      value: item
    }))
  } else if (localFormData.source === SourceTypes.DICT_TABLE) {
    return originSelectMetadata.value.DICT_TABLE.map(item => ({
      title: item,
      value: item
    }))
  }
  return []
})

const getSourceTypeOptions = async () => {
  const res = await getSelectMetadata()
  console.log('getSelectMetadata', res)
  originSelectMetadata.value = res.data
}

// Use a local copy of formData to avoid direct mutation of props
const localFormData = reactive({ ...props.formData })

watch(() => props.formData, (newVal) => {
  Object.assign(localFormData, newVal)
}, { deep: true })

const title = computed(() => props.mode === 'add' ? 'Add New Field' : 'Edit Field')
const submitButtonText = computed(() => props.mode === 'add' ? 'Add Field' : 'Update Field')

const closeDialog = () => {
  emit('update:modelValue', false)
}

const form = ref(null);
const typeError = ref('');

function handleFieldTypeChange(value) {
  console.log('handleFieldTypeChange', value)
  if (value === FieldTypes.SELECT) {
    // 如果字段类型为SELECT，则清空选项
    localFormData.items = []
    localFormData.source = SourceTypes.LOCAL
    localFormData.type = ''
  } else {
    localFormData.source = ''
    localFormData.type = ''
  }
}

function handleAddOption() {
  if (localFormData.fieldType === FieldTypes.SELECT && localFormData.source === SourceTypes.LOCAL) {
    typeError.value = '';
    localFormData.items.push({
      label: '',
      value: '',
      labelError: '',
      valueError: ''
    })
  }
}

  function removeOption(index) {
    localFormData.items.splice(index, 1)
  }

  function handleSubmit() {
    typeError.value = ''; // Clear previous error
    localFormData.items.forEach(item => {
      item.labelError = '';
      item.valueError = '';
    });

    form.value.validate().then(({ valid }) => {
      if (valid) {
        if (localFormData.fieldType === FieldTypes.SELECT && localFormData.source === SourceTypes.LOCAL) {
          let hasError = false;
          if (localFormData.items.length === 0) {
            hasError = true;
            typeError.value = 'Please add at least one option';
            return;
          }
          localFormData.items.forEach(item => {
            if (!item.label) {
              item.labelError = 'This label is required';
              hasError = true;
            }
            if (!item.value) {
              item.valueError = 'This value is required';
              hasError = true;
            }
          });
          if (hasError) return;
        } else if (localFormData.fieldType === FieldTypes.SELECT && [SourceTypes.APP_ENUMS, SourceTypes.DICT_TABLE].includes(localFormData.source)) {
          if (!localFormData.type) {
            typeError.value = 'This field is required';
            return;
          }
        }
        emit('submit', localFormData)
      }
    })
  }

  watch(() => props.modelValue, (newVal) => {
    if (newVal) { // Only run when dialog is opened
      if (props.mode === 'add') {
        // Reset form data when opening in add mode
        Object.assign(localFormData, {
          fieldName: '',
          displayName: '',
          fieldType: FieldTypes.TEXT,
          defaultValue: '',
          required: false,
          queryable: false,
          items: [], // Initialize items as an empty array
          type: ''
        })
      } else if (props.mode === 'edit') {
        // When in edit mode, ensure localFormData is synced with props.formData
        Object.assign(localFormData, props.formData)
        // Ensure items array exists when in edit mode
        if (!localFormData.items) {
          localFormData.items = []
        }
        // Ensure error properties for items exist when in edit mode
        localFormData.items.forEach(item => {
          if (!('labelError' in item)) item.labelError = '';
          if (!('valueError' in item)) item.valueError = '';
        });
      }

      // Clear type error when dialog opens
      typeError.value = '';
    }
  })

  // Watch for changes in item label/value to clear errors
  watch(() => localFormData.items, (newItems) => {
    newItems.forEach(item => {
      if (item.label) item.labelError = '';
      if (item.value) item.valueError = '';
    });
  }, { deep: true });

  // Watch for changes in localFormData.type to clear error
  watch(() => localFormData.type, (newType) => {
    if (newType) {
      typeError.value = '';
    }
  });

  watch(() => localFormData.source, (newSource) => {
    if ([SourceTypes.APP_ENUMS, SourceTypes.DICT_TABLE].includes(newSource)) {
      getSourceTypeOptions();
    } else {
      localFormData.type = ''; // Clear type if source is not APP_ENUMS or DICT_TABLE
      typeError.value = ''; // Clear type error if source changes
    }
  });
</script>

<style scoped></style>