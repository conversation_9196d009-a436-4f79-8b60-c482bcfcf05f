<script setup>
import { VNode<PERSON>enderer } from '@layouts/components/VNodeRenderer'
import { layoutConfig } from '@layouts'
</script>

<template>
  <div class="ml-3 d-flex align-center gap-5 ">
    <VNodeRenderer :nodes="layoutConfig.app.logo" class="w-54px h-43px d-none d-sm-block"/>
  
    <Transition name="vertical-nav-app-title">
      <h1 class="app-logo-title text-20 font-weight-medium d-none d-sm-block">
        Admin Console
      </h1>
    </Transition>
  </div>
</template>