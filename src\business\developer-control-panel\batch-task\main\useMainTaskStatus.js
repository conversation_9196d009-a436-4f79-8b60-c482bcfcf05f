export const useMainTaskStatus = () => {
    const mainTaskStatusList = ref([])

    const loadData = async () => {
        const res = await $api('api/pin-generator/v1/batch-task/main-task/status', {
            method: 'GET',
        })
        mainTaskStatusList.value = Array.isArray(res.data)
            ? res.data.map(item => ({
                ...item,
                title: item,
                value: item
            })) : [];
    }

    onMounted(async () => {
        await loadData()
    })

    return { mainTaskStatusList }
}