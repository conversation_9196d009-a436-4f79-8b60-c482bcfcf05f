<script setup>
import navItems from '@/config/navigation'
import { themeConfig } from '@themeConfig'

// Components
import Footer from '@/layouts/components/Footer.vue'
import NavBarNotifications from '@/layouts/components/NavBarNotifications.vue'
import NavSearchBar from '@/layouts/components/NavSearchBar.vue'
import NavbarShortcuts from '@/layouts/components/NavbarShortcuts.vue'
import NavbarThemeSwitcher from '@/layouts/components/NavbarThemeSwitcher.vue'
import UserProfile from '@/layouts/components/UserProfile.vue'
import NavBarI18n from '@/components/I18n.vue'
import NavTitle from '@/layouts/components/NavTitle.vue'
import Breadcrumb from '@/components/Breadcrumb.vue'
import { isDevelopment } from '@/utils/helpers'
import TimeZone from '@/layouts/components/TimeZone.vue'
// @layouts plugin
import { VerticalNavLayout } from '@layouts'

</script>

<template>
  <VerticalNavLayout :nav-items="navItems">
    <!-- 👉 navbar -->
    <template #navbar="{ toggleVerticalOverlayNavActive }">
      <div class="d-flex h-100 align-center">
        <IconBtn
          id="vertical-nav-toggle-btn"
          class="ms-n3 d-lg-none"
          @click="toggleVerticalOverlayNavActive(true)"
        >
          <VIcon
            size="26"
            icon="tabler-menu-2"
          />
        </IconBtn>

        <NavSearchBar v-if="themeConfig.app.showSearchBar" class="ms-lg-n3" />
        <NavTitle />

        <VSpacer />

        <NavBarI18n
          v-if="themeConfig.app.i18n.enable && themeConfig.app.i18n.langConfig?.length"
          :languages="themeConfig.app.i18n.langConfig"
        />
        <NavbarThemeSwitcher v-if="themeConfig.app.showThemeSwitcher" />
        <NavbarShortcuts v-if="themeConfig.app.showShortcuts" />
        <TimeZone />
        <NavBarNotifications v-if="themeConfig.app.showNotifications" class="me-1" />
        <UserProfile />
      </div>
    </template>

    <!-- 👉 Pages -->
    <v-main>
      <v-container fluid>
        <Breadcrumb />
        <router-view />
      </v-container>
    </v-main>

    <!-- 👉 Footer -->
    <template #footer>
      <Footer />
    </template>

    <!-- 👉 Customizer -->
    <TheCustomizer v-if="themeConfig.app.showCustomizer && isDevelopment" />
  </VerticalNavLayout>
</template>


<style scoped>
:deep(.v-container){
  padding: 0;
}
</style>
