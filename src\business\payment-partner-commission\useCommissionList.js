export const useCommissionList = (merchantId) => {

  const commissionList = ref([]);
  const merchantIdRef = ref(merchantId);

  const loadCommissionList = async () => {
    if (!merchantIdRef.value) {
      return;
    }
    const res = await $api("/api/admin-api/v1/commission-scheme/query-by-merchant", {
      method: "GET",
      params: {
        merchantId: merchantIdRef.value,
      }
    });
    commissionList.value = res?.data?.map(item => {
      return {
        ...item,
        title: item.name,
        value: item.id,
      };
    });
  };


  // Use watchEffect to monitor changes in reactive data
  watchEffect(() => {
    loadCommissionList();
  });

  // Provide a method to update merchantId
  const updateMerchantId = (newMerchantId) => {
    merchantIdRef.value = newMerchantId;
  };

  return {
    commissionList,
    updateMerchantId,
  };
};

