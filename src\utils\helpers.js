// 👉 IsEmpty
export const isEmpty = value => {
  if (value === null || value === undefined || value === '')
    return true
  
  return !!(Array.isArray(value) && value.length === 0)
}

// 👉 IsNullOrUndefined
export const isNullOrUndefined = value => {
  return value === null || value === undefined
}

// 👉 IsEmptyArray
export const isEmptyArray = arr => {
  return Array.isArray(arr) && arr.length === 0
}

// 👉 IsObject
export const isObject = obj => obj !== null && !!obj && typeof obj === 'object' && !Array.isArray(obj)

// 👉 IsArray
export const isArray = arr => Array.isArray(arr)

// 👉 IsToday
export const isToday = date => {
  const today = new Date()
  
  return (date.getDate() === today.getDate()
        && date.getMonth() === today.getMonth()
        && date.getFullYear() === today.getFullYear())
}

// 👉 debounce
export const debounce = (func, delay) => {
  let timeout
  return function(...args) {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(this, args), delay)
  }
}

// 👉 isPlainObject
export const isPlainObject = obj => {
  return Object.prototype.toString.call(obj) === '[object Object]'
}

// Convert list to tree structure
export const listToTree = (list, idKey = 'id', parentIdKey = 'parentId', childrenKey = 'children') => {
  const map = new Map()
  const tree = []

  // First pass: Create mapping for all nodes, preserving original data properties
  list.forEach(item => {
    map.set(item[idKey], { ...item, [childrenKey]: [] })
  })

  // Second pass: Build tree structure
  list.forEach(item => {
    const id = item[idKey]
    const parentId = item[parentIdKey]
    const node = map.get(id)

    if (parentId && map.has(parentId)) {
      // If parent exists, add current node to parent's children
      const parent = map.get(parentId)
      parent[childrenKey].push(node)
    } else {
      // If no parent or parent doesn't exist, add as root node
      tree.push(node)
    }
  })

  return tree
}

export const treeToList = (tree, childrenKey = 'children') => {
  const result = []

  const traverse = (node) => {
    const { [childrenKey]: children, ...rest } = node
    result.push(rest)

    if (children && children.length) {
      children.forEach(traverse)
    }
  }

  tree.forEach(traverse)
  return result
}

// 👉 isDev
export const isDev = import.meta.env.MODE === 'dev'

// 👉 isQa
export const isQa = import.meta.env.MODE === 'qa'

// 👉 isProd
export const isProd = import.meta.env.MODE === 'prod'

// Optional: Add development environment detection
export const isDevelopment = import.meta.env.DEV // This is Vite's built-in environment variable
export const isProduction = import.meta.env.PROD // This is Vite's built-in environment variable

// 👉 isTextOverflow
export const isTextOverflow = (text, maxWidth = 300) => {
  const tempElement = document.createElement('div')
  tempElement.style.visibility = 'hidden'
  tempElement.style.position = 'absolute'
  tempElement.style.width = `${maxWidth}px`
  tempElement.style.whiteSpace = 'nowrap'
  tempElement.textContent = text
  document.body.appendChild(tempElement)
  
  const isOverflowing = tempElement.scrollWidth > tempElement.clientWidth
  document.body.removeChild(tempElement)
  
  return isOverflowing
}

// 👉 calculateHeight
export const calculateHeight = (elementRef) => {
  if (!elementRef.value) return 0

  const cardRect = elementRef.value.$el.getBoundingClientRect()
  const cardHeaderHeight = 68
  const bottomPadding = 180
  const availableHeight = document.documentElement.clientHeight - cardRect.top - cardHeaderHeight - bottomPadding
  
  return availableHeight
}


export function formatJson(jsonString) {
  const parsed = JSON.parse(jsonString);
  const formatted = JSON.stringify(parsed, null, 2);
  return JSON.stringify(parsed, null, 2);
}
