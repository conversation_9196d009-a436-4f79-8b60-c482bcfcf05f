<script setup>
import { ref, computed, watch, nextTick } from "vue";
import dateUtil from "@/utils/day";
import bigUtil from "@/utils/bigNumber";

// Add component name
defineOptions({
  name: "PaymentChannelTable",
});

const props = defineProps({
  paymentChannelList: {
    type: Array,
    default: () => [],
    required: true,
  },
  commissionTerritoryList: {
    type: Array,
    default: () => [],
    required: true,
  },
  commissionRateErrors: {
    type: Array,
    default: () => [],
  },
  effectiveOnErrors: {
    type: Array,
    default: () => [],
  },
  duplicateErrors: {
    type: Array,
    default: () => [],
  }
});

const items = defineModel("items", { required: true });

const emit = defineEmits([
  "update:selectedRows",
  "update:items",
  "delete",
  "edit",
  "bulkEdit",
]);

const headers = [
  {
    title: "", // checkbox column
    key: "data-table-select",
    sortable: false,
    fixed: true,
    width: 50,
  },
  { title: "No", key: "no", sortable: false },
  { title: "Payment Channel", key: "paymentChannel", sortable: false },
  { title: "Merchant Currency", key: "merchantCurrency", sortable: false },
  { title: "Minimum Amount", key: "minAmount", sortable: false },
  { title: "Commission Rate", key: "commissionRate", sortable: false },
  { title: "Fix Amount", key: "fixAmount", sortable: false },
  {
    title: "Commission Territory",
    key: "commissionTerritory",
    sortable: false,
  },
  { title: "Effective On", key: "effectiveOn", sortable: false },
  { title: "Effective Till", key: "effectiveTill", sortable: false },
  { title: "Status", key: "status", sortable: false },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    width: 120,
    fixed: true,
  },
];

const selectedRows = defineModel("selectedRows", { required: true });
const searchQuery = ref({
  paymentChannel: null,
  commissionTerritory: null,
});

const filterItems = ref([]);

watch(
  [() => searchQuery.value, () => props.items],
  ([newVal, newList]) => {
    if (newVal.paymentChannel && newVal.commissionTerritory) {
      filterItems.value = props.items.filter((item) => {
        return (
          item.paymentMethodName === newVal.paymentChannel &&
          item.territoryName === newVal.commissionTerritory
        );
      });
      return;
    }
    if (newVal.paymentChannel) {
      filterItems.value = props.items.filter((item) => {
        return item.paymentMethodName === newVal.paymentChannel;
      });
      return;
    }
    if (newVal.commissionTerritory) {
      filterItems.value = filterItems.value.filter((item) => {
        return item.territoryName === newVal.commissionTerritory;
      });
      return;
    }

    return (filterItems.value = props.items);
  },
  { immediate: true, deep: true }
);

const handleSelectChange = (value) => {
  emit("update:selectedRows", value);
};

const handleDoubleClickEdit = (item, e, type = "isEditing") => {
  // Set edit state first
  item[type] = true;
  item.isCloseDatePicker = false;

  // Use nextTick to ensure focus after DOM update
  nextTick(() => {
    // Find input element in table row directly, not relying on data-focus attribute
    const cell = e.target.closest('[data-focus="true"]');
    if (cell) {
      // Find cell and input element that just became editable
      const input = cell.querySelector("input");
      const field = cell.querySelector(".v-field__input");
      const datepicker = cell.querySelector(".flatpickr-input");

      if (input) {
        input.focus();
      } else if (field) {
        field.focus();
      } else if (datepicker) {
        datepicker.focus();
      }
    } else {
      console.log("Input element not found in table row");
    }
  });
};

const handleCommissionRateChange = (item, value) => {
  item.convertCommissionRate = value;
  // Clear error state when value is valid
  if (value && value >= 0 && value <= 100) {
    const index = items.value.indexOf(item);
    if (props.commissionRateErrors.includes(index)) {
      props.commissionRateErrors.splice(props.commissionRateErrors.indexOf(index), 1);
    }
  }
};

const handleCommissionRateBlur = (item) => {
  item.isCommissionRateEditing = false;
  if (isEmpty(item.convertCommissionRate)) {
    item.convertCommissionRate = item.defaultRate
  }
  if (item.convertCommissionRate > 100) {
    item.convertCommissionRate = 100;
  }
  item.defaultRate = item.convertCommissionRate
};

const handleEffectiveDateChange = (item, value) => {
  item.convertEffectiveOn = value;
  // Clear error state when value is valid
  if (value) {
    const index = items.value.indexOf(item);
    if (props.effectiveOnErrors.includes(index)) {
      props.effectiveOnErrors.splice(props.effectiveOnErrors.indexOf(index), 1);
    }
  }
};

const handleDatePickerClose = (item) => {
  item.isCloseDatePicker = true;
  // Only trigger save and exit edit mode when date picker panel closes
  if (item.convertEffectiveOn) {
    // Short delay to ensure UI updates completely
    nextTick(() => {
      item.isEditing = false;
      item.isEffectiveOnEditing = false;
      item.isCommissionRateEditing = false;
      item.effectiveOn = item.convertEffectiveOn;
    });
  }
};

const handleEdit = (item) => {
  emit("edit", item);
};

const handleDelete = (item) => {
  emit("delete", item);
};

const clearAllSelected = () => {
  const selectedIds = selectedRows.value.map((item) => item.id);

  items.value = items.value
    .filter((item) => {
      return !selectedIds.includes(item.id);
    })
    .map((item, index) => {
      return {
        ...item,
        no: index + 1,
      };
    });

  emit("clearAllSelected", selectedIds);

  selectedRows.value = [];
};
</script>

<template>
  <div class="relative">
    <div class="payment-channel-table">
      <div class="flex items-center" v-if="items.length">
        <div>Filter By:</div>
        <div class="ml-4 w-280px!">
          <AppAutocomplete
            placeholder="Select Payment Channel"
            :items="paymentChannelList"
            v-model="searchQuery.paymentChannel"
            clearable
            clear-icon="tabler-x"
            filterable
          >
          </AppAutocomplete>
        </div>
        <div class="ml-6 w-280px!">
          <AppAutocomplete
            placeholder="Select Commission Territory"
            :items="commissionTerritoryList"
            v-model="searchQuery.commissionTerritory"
            clearable
            clear-icon="tabler-x"
            filterable
          >
          </AppAutocomplete>
        </div>
      </div>
      <div class="d-flex align-center gap-4 mt-4 h-10" v-if="selectedRows.length">
        <div>
          <span
            class="font-weight-medium"
            :class="[selectedRows.length && 'text-primary']"
            >{{ selectedRows.length }}</span
          >
          items selected
          <VIcon
            size="20"
            icon="mdi-delete"
            @click="clearAllSelected"
            class="hover:text-error"
            v-tooltip="'Delete'"
          />
        </div>
        <VDivider vertical color="#999" class="my-auto h-4!" />
        <slot name="bulk"> </slot>
      </div>
      <div class="mt-2">
        <VDataTableVirtual
          :headers="headers"
          :items="filterItems"
          show-select
          :item-value="(item) => item"
          v-model:model-value="selectedRows"
        >
          <!-- Payment Channel -->
          <template #header.effectiveOn="{ item }">
            <div class="flex-center normal-case">
              <div>Effective On</div>
              <div
                class="ml-10px text-16px mdi-information-outline"
                v-tooltip="'The settlement report is based on UTC +8'"
              />
            </div>
          </template>

          <!-- Custom Payment Channel slot -->
          <template #item.paymentChannel="{ item, index }">
            <slot
              name="payment-channel"
              :item="item"
              :index="index"
              :is-editing="item.isEditing || item.isCommissionRateEditing"
            >
              <div
                class="line-clamp-2 w-200px"
                @dblclick="handleDoubleClickEdit(item, $event, 'isPaymentChannelEditing')"
                data-focus="true"
              >
                {{ item.paymentMethodName }}
              </div>
            </slot>
          </template>

          <!-- Custom Merchant Currency slot -->
          <template #item.merchantCurrency="{ item, index }">
            <slot
              name="merchant-currency"
              :item="item"
              :index="index"
              :is-editing="item.isEditing || item.isCommissionRateEditing"
              @dblclick="handleDoubleClickEdit(item, $event, 'isMerchantCurrencyEditing')"
            >
              <div class="line-clamp-2">
                {{ item.merchantCurrency }}
              </div>
            </slot>
          </template>

          <!-- Custom Minimum Amount slot -->
          <template #item.minAmount="{ item, index }">
            <slot
              name="min-amount"
              :item="item"
              :index="index"
              :is-editing="item.isEditing || item.isCommissionRateEditing"
              @dblclick="handleDoubleClickEdit(item, $event, 'isMinAmountEditing')"
            >
              <div class="line-clamp-2">
                {{ item.minAmount }}
              </div>
            </slot>
          </template>

          <!-- Commission Rate -->
          <template #item.commissionRate="{ item, index }">
            <div 
              class="flex-center min-w-100px" 
              data-focus="true" 
              data-commission-rate
              :class="{ 'border-error border-1px hover:border-none': commissionRateErrors.includes(index) && !(item.isEditing || item.isCommissionRateEditing) }"
            >
              <AppTextField
                v-show="item.isEditing || item.isCommissionRateEditing"
                :model-value="item.convertCommissionRate"
                min="0"
                max="100"
                decimal-places="2"
                type="number"
                class="bg-#000000"
                placeholder="Commission Rate"
                :error="commissionRateErrors.includes(index)"
                @update:model-value="handleCommissionRateChange(item, $event)"
                @blur="handleCommissionRateBlur(item)"
              >
                <template #append-inner>%</template>
              </AppTextField>
              <div
                v-show="!(item.isEditing || item.isCommissionRateEditing)"
                class="w-100% px-4 h-38px rounded hover:border-primary flex justify-end align-center"
                @dblclick="handleDoubleClickEdit(item, $event, 'isCommissionRateEditing')"
              >
                {{ item.convertCommissionRate ? `${item.convertCommissionRate}%` : "-" }}
              </div>
            </div>
          </template>

          <!-- Custom Fix Amount slot -->
          <template #item.fixAmount="{ item, index }">
            <slot
              name="fix-amount"
              :item="item"
              :index="index"
              :is-editing="item.isEditing || item.isCommissionRateEditing"
              @dblclick="handleDoubleClickEdit(item, $event, 'isFixAmountEditing')"
            >
              <div class="line-clamp-2">
                {{ item.fixAmount }}
              </div>
            </slot>
          </template>

          <!-- Commission Territory -->
          <template #item.commissionTerritory="{ item }">
            <div class="line-clamp-2">
              {{ item.territoryName }}
            </div>
          </template>

          <!-- Effective On(UTC +8) -->
          <template #item.effectiveOn="{ item, index }">
            <div 
              class="flex-center min-w-280px" 
              data-focus="true"
              :class="{ 'border-error border-1px hover:border-none': effectiveOnErrors.includes(index) && !(item.isEditing || item.isEffectiveOnEditing) }"
            >
              <AppDateTimePicker
                v-model="item.convertEffectiveOn"
                placeholder="Select Effective On"
                @update:model-value="(value) => handleEffectiveDateChange(item, value)"
                @close="() => handleDatePickerClose(item)"
                :config="{
                  enableTime: true,
                  dateFormat: 'd M Y H:i:S',
                  time_24hr: true,
                }"
                v-show="item.isEditing || item.isEffectiveOnEditing"
                class="w-280px bg-#000000"
                prepend-inner-icon="mdi-calendar-clock-outline"
                :error="effectiveOnErrors.includes(index)"
              ></AppDateTimePicker>
              <div
                v-show="!(item.isEditing || item.isEffectiveOnEditing)"
                class="w-100% px-4 h-38px rounded hover:border-primary flex align-center justify-center"
                @dblclick="handleDoubleClickEdit(item, $event, 'isEffectiveOnEditing')"
              >
                {{ item.effectiveOn ? dateUtil.format(item.effectiveOn, "DD-MMM-YYYY HH:mm:ss") : "-" }}
              </div>
            </div>
          </template>

          <!-- Status -->
          <template #item.status="{ item }">
            <div class="flex-center gap-1">
              <span class="mdi-dots-circle text-14px relative top-[-1px]"></span>
              <span>{{ item.status }}</span>
            </div>
          </template>

          <!-- Actions -->
          <template #item.actions="{ item }">
            <div class="flex-center w-70px">
              <IconBtn v-tooltip="'Edit'" @click="handleEdit(item)">
                <VIcon
                  icon="mdi-pencil"
                  :class="{
                    'text-#767676': item.isEditing,
                    'hover:text-primary': !item.isEditing,
                  }"
                />
              </IconBtn>
              <IconBtn v-tooltip="'Delete'">
                <VIcon
                  icon="mdi-delete"
                  class="hover:text-error"
                  @click="handleDelete(item)"
                />
              </IconBtn>
              <slot name="actions" :item="item"> </slot>
            </div>
          </template>
        </VDataTableVirtual>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.payment-channel-table {
  :deep(.v-table) {
    .v-table__wrapper {
      overflow-x: auto;

      table {
        min-width: 100%;
      }

      thead, tbody {
        .v-data-table-column--fixed {
          position: sticky !important;
          z-index: 1;
          background-color: #000000 !important;

          &:first-child {
            left: 0;
            z-index: 2;
          }

          &:last-child {
            right: 0;
            z-index: 2;
          }
        }
      }

      tbody {
        background-color: #1e1e1e !important;

        .v-data-table__tr:hover {
          background-color: #131212 !important;

          .v-data-table-column--fixed {
            background-color: #131212 !important;
          }
        }

        .v-data-table-column--fixed {
          background-color: #1e1e1e !important;
        }
      }
    }
  }
}

.text-error {
  color: rgb(var(--v-theme-error));
}

.bg-error-bg {
  background-color: rgba(var(--v-theme-error), 0.12);
  border-radius: 4px;
}
</style>
