<template>
  <div class="properties-panel">
    <div class="panel-header">
      <div class="panel-title">
        <VIcon :icon="getPanelIcon()" size="16" class="mr-2" />
        <span>{{ getPanelTitle() }}</span>
      </div>
      <div class="panel-actions">
        <VBtn v-if="selectedComponent" icon size="small" variant="text" @click="clearSelection" title="返回页面属性">
          <VIcon icon="mdi-arrow-left" size="16" />
        </VBtn>
      </div>
    </div>

    <div class="panel-content">
      <!-- 页面属性 -->
      <div v-if="!selectedComponent" class="page-properties">
        <div class="property-section">
          <div class="section-header">
            <h5 class="section-title border-b bg-#222222">
              <VIcon icon="mdi-information-outline" size="14" class="mr-2" />
              Page Settings
            </h5>
          </div>
          <div class="section-content">
            <div class="field-item">
              <label class="field-label">Page Module</label>
              <div class="field-input">
                <AppTextField :model-value="pageConfig.module" placeholder="Input Page Module" density="compact" disabled
                  variant="outlined" hide-details @update:model-value="updatePageConfig('module', $event)" />
              </div>
            </div>
            <div class="field-item">
              <label class="field-label">Page Title</label>
              <div class="field-input">
                <AppTextField :model-value="pageConfig.name" placeholder="Input Page Name" density="compact"
                  variant="outlined" hide-details @update:model-value="updatePageConfig('title', $event)" />
              </div>
            </div>
            <div class="field-item">
              <label class="field-label">Page Path</label>
              <div class="field-input">
                <AppTextField :model-value="pageConfig.path" placeholder="Input Page Path" density="compact"
                  variant="outlined" hide-details @update:model-value="updatePageConfig('path', $event)" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 组件属性 -->
      <div v-else class="component-properties">
        <!-- 动态生成的属性配置 -->
        <div class="properties-sections">
          <div v-for="section in getComponentSections()" :key="section.key" class="property-section">
            <div class="section-header">
              <h5 class="section-title">
                <VIcon v-if="section.icon" :icon="section.icon" size="14" class="mr-2" />
                {{ section.title }}
              </h5>
            </div>

            <div class="section-content">
              <div v-for="field in section.fields" :key="field.key" class="field-item">
                <!-- 文本输入 -->
                <template v-if="field.type === 'text'">
                  <label class="field-label">{{ field.label }}</label>
                  <div class="field-input">
                    <AppTextField 
                      :model-value="getFieldValue(field)"
                      :placeholder="field.placeholder"
                      :readonly="field.readonly"
                      density="compact" 
                      variant="outlined" 
                      hide-details
                      @update:model-value="updateField(field.key, $event)" 
                    />
                  </div>
                </template>

                <!-- 数字输入 -->
                <template v-else-if="field.type === 'number'">
                  <label class="field-label">{{ field.label }}</label>
                  <div class="field-input">
                    <AppTextField :model-value="getFieldValue(field)" :placeholder="field.placeholder" type="number"
                      density="compact" variant="outlined" hide-details
                      @update:model-value="updateField(field.key, $event)" />
                  </div>
                </template>

                <!-- 选择器 -->
                <template v-else-if="field.type === 'select'">
                  <label class="field-label">{{ field.label }}</label>
                  <div class="field-input">
                    <AppSelect :model-value="getFieldValue(field)" :items="field.options" density="compact"
                      variant="outlined" hide-details @update:model-value="updateField(field.key, $event)" />
                  </div>
                </template>

                <!-- 开关 -->
                <template v-else-if="field.type === 'switch'">
                  <label class="field-label">{{ field.label }}</label>
                  <div class="field-input">
                    <VSwitch :model-value="getFieldValue(field)" density="compact" hide-details
                      @update:model-value="updateField(field.key, $event)" />
                  </div>
                </template>

                <!-- 列表编辑 -->
                <div v-else-if="field.type === 'list'" class="list-field">
                  <div class="list-header">
                    <label class="list-label">{{ field.label }}</label>
                    <VBtn v-if="field.addable" icon size="x-small" variant="text" @click="addListItem(field.key)"
                      title="Add Item">
                      <VIcon icon="mdi-plus" size="14" />
                    </VBtn>
                  </div>

                  <div class="list-items">
                    <div v-for="(item, index) in getFieldValue(field) || []" :key="index" class="list-item">
                      <div class="item-content">
                        <template v-for="subField in field.itemFields" :key="subField.key">
                          <div v-if="subField.type === 'text'" class="item-field">
                            <AppTextField 
                              :model-value="item[subField.key]" 
                              :placeholder="subField.label"
                              :readonly="subField.readonly"
                              density="compact" 
                              variant="outlined" 
                              hide-details
                              @update:model-value="updateListItem(field.key, index, subField.key, $event)" 
                            />
                          </div>
                          <div v-else-if="subField.type === 'select'" class="item-field">
                            <AppSelect :model-value="item[subField.key]" :items="subField.options"
                              :placeholder="subField.label" density="compact" variant="outlined" hide-details
                              @update:model-value="updateListItem(field.key, index, subField.key, $event)" />
                          </div>
                        </template>
                      </div>
                      <VBtn v-if="field.removable" icon size="x-small" variant="text" color="error"
                        @click="removeListItem(field.key, index)" title="Remove Item">
                        <VIcon icon="mdi-delete" size="14" />
                      </VBtn>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, nextTick, ref } from 'vue'

// Props
const props = defineProps({
  pageConfig: Object,
  selectedComponent: Object
})

// Emits
const emit = defineEmits([
  'update-page-config',
  'component-update'
])



// 计算属性
const componentType = computed(() => {
  return props.selectedComponent?.componentName || props.selectedComponent?.type
})

// 强制刷新标记
const forceRefresh = ref(0)

// Panel Header Logic
function getPanelIcon() {
  if (props.selectedComponent) {
    return getComponentIcon()
  }
  return 'mdi-file-document'
}

function getPanelTitle() {
  if (props.selectedComponent) {
    return `${getComponentDisplayName()} Properties`
  }
  return 'Page Properties'
}

// Component Icon Mapping
function getComponentIcon() {
  const iconMap = {
    'TitleBar': 'mdi-card-text-outline',
    'FilterPanel': 'mdi-filter-variant',
    'DataTable': 'mdi-table',
    'VBtn': 'mdi-button-cursor',
    'VTextField': 'mdi-form-textbox',
    'VCard': 'mdi-card',
    'VDataTableServer': 'mdi-table',
    'VSelect': 'mdi-form-select',
    'VSwitch': 'mdi-toggle-switch',
    'VIcon': 'mdi-star',
    'VDivider': 'mdi-minus',
    'TablePagination': 'mdi-page-next',
    'TablePaginationLowCode': 'mdi-page-next-outline'
  }
  return iconMap[componentType.value] || 'mdi-puzzle'
}

function getComponentDisplayName() {
  return props.selectedComponent?.componentName || componentType.value
}

// 动态生成组件属性配置
function getComponentSections() {
  if (!props.selectedComponent) return []


  if (props.selectedComponent.propertyConfig) {
    return props.selectedComponent.propertyConfig.sections
  }

  return []
}

// 获取字段值
function getFieldValue(field) {
  if (!props.selectedComponent) return field.defaultValue || ''

  const { key, defaultValue } = field

  console.log('🔍 getFieldValue: 获取字段', key, '默认值:', defaultValue)

  // 组件特定字段获取器
  const fieldGetters = {
    title: () => getTitleBarText(),
    buttons: () => getTitleBarButtons(),
    filters: () => getFilterPanelFilters(),
    columns: () => {
      // 确保响应页面配置变化
      const result = getDataTableColumns()
      console.log('🔍 fieldGetters.columns 被调用，返回:', result)
      return result
    },
    fields: () => getFormFieldsList(),
    form: () => getFormFields(),
    options: () => getDataTableOptions(),
    name: () => props.selectedComponent.name,
    class: () => props.selectedComponent.props?.class,
    width: () => props.selectedComponent.style?.width,
    height: () => props.selectedComponent.style?.height,
    backgroundColor: () => props.selectedComponent.style?.backgroundColor,
    showSelect: () => props.selectedComponent.props?.showSelect,
    loading: () => props.selectedComponent.props?.loading,
    itemsPerPage: () => props.selectedComponent.props?.itemsPerPage,
    hideLabels: () => props.selectedComponent.props?.hideLabels
  }

  const getter = fieldGetters[key]
  console.log("getter", getter)
  const result = getter ? (getter() ?? defaultValue) : defaultValue

  console.log('🔍 getFieldValue: 字段', key, '的结果:', result)
  return result
}

// 辅助函数：发送带历史信息的组件更新事件
function emitComponentUpdateWithHistory(updatedComponent) {
  // 保存旧状态（深拷贝）
  const oldComponent = JSON.parse(JSON.stringify(props.selectedComponent))
  
  emit('component-update', props.selectedComponent.id, {
    oldComponent,
    newComponent: updatedComponent,
    isPropertyUpdate: true
  })
}

// 更新字段值
function updateField(key, value) {
  if (!props.selectedComponent) return

  // 组件特定字段更新器
  const fieldUpdaters = {
    title: (value) => nextTick(() => updateTitleBarText(value)),
    buttons: (value) => updateTitleBarButtons(value),
    name: (value) => updateComponentProperty('name', value),
    class: (value) => updateComponentProp('class', value),
    width: (value) => updateComponentStyle('width', value),
    height: (value) => updateComponentStyle('height', value),
    backgroundColor: (value) => updateComponentStyle('backgroundColor', value),
    showSelect: (value) => updateComponentProp('showSelect', value),
    loading: (value) => updateComponentProp('loading', value),
    itemsPerPage: (value) => updateComponentProp('itemsPerPage', value),
    hideLabels: (value) => updateComponentProp('hideLabels', value)
  }

  const updater = fieldUpdaters[key]
  if (updater) {
    updater(value)
  }
}

// TitleBar 相关方法
function getTitleBarText() {
  // 优先从页面配置中获取标题
  if (props.pageConfig?.title) {
    console.log('📖 从页面配置获取标题:', props.pageConfig.title)
    return props.pageConfig.title
  }

  // 递归查找组件中的标题文本
  function findTitleInComponent(component) {
    if (component.type === 'div' && component.props?.text) {
      return component.props.text
    }

    if (component.children) {
      for (const child of component.children) {
        const result = findTitleInComponent(child)
        if (result) return result
      }
    }

    return null
  }

  const text = findTitleInComponent(props.selectedComponent) || ''
  console.log('📖 从组件结构获取标题:', text)
  return text
}
function updateTitleBarText(text) {
  console.log('🔄 更新标题文本:', text)

  // 保存旧状态（深拷贝）
  const oldComponent = JSON.parse(JSON.stringify(props.selectedComponent))

  // 递归更新组件中的标题文本
  function updateTitleInComponent(component) {
    if (component.type === 'div' && component.props?.text !== undefined) {
      console.log('📝 更新组件中的标题文本:', text)
      return { ...component, props: { ...component.props, text } }
    }

    if (component.children) {
      return {
        ...component,
        children: component.children.map(updateTitleInComponent)
      }
    }

    return component
  }

  // 1. 更新组件结构
  const updatedComponent = updateTitleInComponent(props.selectedComponent)

  // 2. 发送组件更新事件（包含历史信息）
  emitComponentUpdateWithHistory(updatedComponent)

  // 3. 同时更新页面配置中的标题
  if (props.pageConfig && props.pageConfig.title !== text) {
    console.log('📄 更新页面配置中的标题:', text)
    emit('update:page-config', 'title', text)
  }
}

function getTitleBarButtons() {
  // 递归查找所有 VBtn 组件
  const buttons = []

  function findButtons(component, path = []) {
    if (component.type === 'VBtn') {
      const button = {
        text: component.props?.text || '',
        variant: component.props?.variant || 'flat',
        // 添加唯一标识符，用于精确匹配
        _id: component.id || `btn_${path.join('_')}`,
        _path: [...path] // 保存路径信息
      }
      console.log('🔍 找到按钮:', button)
      buttons.push(button)
    }

    // 递归查找子组件
    if (component.children) {
      component.children.forEach((child, index) =>
        findButtons(child, [...path, index])
      )
    }

    // 递归查找插槽
    if (component.slots) {
      Object.entries(component.slots).forEach(([slotName, slotContent]) => {
        if (Array.isArray(slotContent)) {
          slotContent.forEach((item, index) =>
            findButtons(item, [...path, 'slots', slotName, index])
          )
        } else if (slotContent) {
          findButtons(slotContent, [...path, 'slots', slotName])
        }
      })
    }
  }

  findButtons(props.selectedComponent)
  console.log('📋 获取到的按钮列表:', buttons)
  return buttons
}

function updateButtonProperty(buttonIndex, property, value) {
  console.log('🔧 更新按钮属性:', buttonIndex, property, value)

  // 获取当前按钮列表，用于精确匹配
  const currentButtons = getTitleBarButtons()
  const targetButton = currentButtons[buttonIndex]

  if (!targetButton) {
    console.warn('⚠️ 未找到要更新的按钮，索引:', buttonIndex)
    return
  }

  let buttonUpdated = false

  // 递归查找并更新指定的按钮
  function updateButtonInComponent(component, path = []) {
    if (component.type === 'VBtn' && !buttonUpdated) {
      // 使用与删除相同的匹配逻辑
      const componentId = component.id || `btn_${path.join('_')}`
      const isTargetButton =
        (targetButton._id && componentId === targetButton._id) ||
        (targetButton._path && JSON.stringify(path) === JSON.stringify(targetButton._path)) ||
        (component.props?.text === targetButton.text &&
         component.props?.variant === targetButton.variant)

      if (isTargetButton) {
        console.log('📝 更新按钮组件:', property, value, '路径:', path)
        buttonUpdated = true
        return {
          ...component,
          props: {
            ...component.props,
            [property]: value
          }
        }
      }
    }

    // 递归更新子组件
    if (component.children) {
      const updatedChildren = component.children.map((child, childIndex) =>
        updateButtonInComponent(child, [...path, childIndex])
      )

      return {
        ...component,
        children: updatedChildren
      }
    }

    // 递归更新插槽
    if (component.slots) {
      const updatedSlots = {}
      Object.entries(component.slots).forEach(([slotName, slotContent]) => {
        if (Array.isArray(slotContent)) {
          updatedSlots[slotName] = slotContent.map((child, childIndex) =>
            updateButtonInComponent(child, [...path, 'slots', slotName, childIndex])
          )
        } else if (slotContent) {
          updatedSlots[slotName] = updateButtonInComponent(slotContent, [...path, 'slots', slotName])
        } else {
          updatedSlots[slotName] = slotContent
        }
      })
      return {
        ...component,
        slots: updatedSlots
      }
    }

    return component
  }

  const updatedComponent = updateButtonInComponent(props.selectedComponent)

  if (buttonUpdated) {
    emitComponentUpdateWithHistory(updatedComponent)
    console.log('✅ 按钮属性更新成功')
  } else {
    console.warn('⚠️ 未找到要更新的按钮:', targetButton)
  }
}

function updateFilterProperty(filterIndex, property, value) {
  console.log('🔧 更新筛选字段属性:', filterIndex, property, value)

  // 获取当前筛选字段列表，用于确定要更新的字段
  const currentFilters = getFieldValue({ key: 'filters' }) || []
  const filterToUpdate = currentFilters[filterIndex]

  if (!filterToUpdate) {
    console.error('🔧 找不到要更新的筛选字段:', filterIndex)
    return
  }

  console.log('🔧 要更新的筛选字段:', filterToUpdate)

  // 收集所有筛选字段组件，然后直接通过索引访问
  const filterComponents = []

  function collectFilterComponents(component) {
    if (component.type === 'VCol' && component.children) {
      const filterComponent = component.children[0]
      if (filterComponent && (filterComponent.model || filterComponent.type === 'AppTextField' || filterComponent.type === 'VTextField')) {
        filterComponents.push({ parent: component, filter: filterComponent })
      }
    }

    if (component.children) {
      component.children.forEach(collectFilterComponents)
    }
  }

  // 先收集所有筛选字段组件
  collectFilterComponents(props.selectedComponent)
  console.log('🔧 收集到的筛选字段组件数量:', filterComponents.length)

  // 直接通过索引获取要更新的组件
  if (filterIndex >= 0 && filterIndex < filterComponents.length) {
    const targetComponent = filterComponents[filterIndex]
    console.log('🔧 找到目标筛选字段组件:', targetComponent)

    // 更新组件结构
    function updateFilterInComponent(component) {
      if (component === targetComponent.parent) {
        console.log('📝 更新筛选字段:', property, value)

        const filterComponent = targetComponent.filter

        // 根据属性类型更新不同的配置
        if (property === 'field') {
          // 更新字段名，需要同时更新 model 绑定
          const modelValue = value ? `{{searchQuery.${value}}}` : `{{searchQuery.temp_${Date.now()}}}`
          const placeholderText = value ? `Search ${value}` : '请输入字段名'

          return {
            ...component,
            children: [{
              ...filterComponent,
              model: modelValue,
              props: {
                ...filterComponent.props,
                placeholder: placeholderText
              }
            }]
          }
        } else if (property === 'label') {
          // 更新显示标签
          return {
            ...component,
            children: [{
              ...filterComponent,
              props: {
                ...filterComponent.props,
                label: value
              }
            }]
          }
        } else if (property === 'type') {
          // 更新控件类型
          let newComponentType = 'VTextField'
          let newProps = { ...filterComponent.props }

          switch (value) {
            case 'select':
              newComponentType = 'VSelect'
              newProps = {
                ...newProps,
                items: '[]',
                clearable: true
              }
              break
            case 'date':
              newComponentType = 'VDatePicker'
              newProps = {
                ...newProps,
                clearable: true
              }
              break
            default:
              newComponentType = 'VTextField'
              newProps = {
                ...newProps,
                clearable: true
              }
          }

          return {
            ...component,
            children: [{
              ...filterComponent,
              type: newComponentType,
              props: newProps
            }]
          }
        }
      }

      // 递归更新子组件
      if (component.children) {
        const updatedChildren = component.children.map(child => updateFilterInComponent(child))
        return {
          ...component,
          children: updatedChildren
        }
      }

      return component
    }

    const updatedComponent = updateFilterInComponent(props.selectedComponent)
    emitComponentUpdateWithHistory(updatedComponent)

    // 同时更新页面配置中的筛选字段列表
    const updatedFilters = currentFilters.map((filter, i) =>
      i === filterIndex ? { ...filter, [property]: value } : filter
    )
    console.log('📄 更新页面配置中的筛选字段列表:', updatedFilters)

    // 实际更新页面配置
    if (props.pageConfig) {
      const updatedPageConfig = {
        ...props.pageConfig,
        filters: updatedFilters
      }
      emit('update:page-config', updatedPageConfig)
    }
  } else {
    console.error('🔧 筛选字段索引超出范围:', filterIndex, '总数:', filterComponents.length)
  }
}

function updateTitleBarButtons(buttons) {
  // 保存旧状态（深拷贝）
  const oldComponent = JSON.parse(JSON.stringify(props.selectedComponent))

  // 递归更新所有 VBtn 组件
  function updateButtons(component) {
    if (component.type === 'VBtn') {
      // 找到对应的按钮配置
      const buttonIndex = buttons.findIndex(btn =>
        btn.text === (component.props?.text || '') &&
        btn.variant === (component.props?.variant || 'flat')
      )

      if (buttonIndex !== -1) {
        const buttonConfig = buttons[buttonIndex]
        return {
          ...component,
          props: {
            ...component.props,
            text: buttonConfig.text,
            variant: buttonConfig.variant
          }
        }
      }
    }

    // 递归更新子组件
    if (component.children) {
      return {
        ...component,
        children: component.children.map(updateButtons)
      }
    }

    // 递归更新插槽
    if (component.slots) {
      const updatedSlots = {}
      Object.keys(component.slots).forEach(slotName => {
        const slotContent = component.slots[slotName]
        if (Array.isArray(slotContent)) {
          updatedSlots[slotName] = slotContent.map(updateButtons)
        } else if (slotContent) {
          updatedSlots[slotName] = updateButtons(slotContent)
        } else {
          updatedSlots[slotName] = slotContent
        }
      })
      return {
        ...component,
        slots: updatedSlots
      }
    }

    return component
  }

  const updatedComponent = updateButtons(props.selectedComponent)
  emitComponentUpdateWithHistory(updatedComponent)
}

// 列表操作方法
function addListItem(fieldKey) {
  if (fieldKey === 'buttons') {
    // 为按钮添加，需要创建新的 VBtn 组件
    const newButton = {
      type: 'VBtn',
      props: { text: 'New Button', variant: 'flat' },
      events: { click: [{ action: 'customAction' }] }
    }

    // 递归查找合适的位置添加按钮
    function addButtonToComponent(component) {
      if (component.type === 'VCardTitle') {
        // 在 VCardTitle 中查找或创建按钮容器
        let actionsDiv = component.children?.find(child =>
          child.type === 'div' && child.props?.class?.includes('d-flex')
        )

        if (!actionsDiv) {
          // 创建按钮容器
          actionsDiv = {
            type: 'div',
            props: { class: 'd-flex align-center gap-4' },
            children: []
          }
          component.children = component.children || []
          component.children.push(actionsDiv)
        }

        actionsDiv.children = actionsDiv.children || []
        actionsDiv.children.push(newButton)
        return true
      }

      // 递归查找子组件
      if (component.children) {
        for (const child of component.children) {
          if (addButtonToComponent(child)) {
            return true
          }
        }
      }

      return false
    }

    const updatedComponent = { ...props.selectedComponent }
    addButtonToComponent(updatedComponent)
    emitComponentUpdateWithHistory(updatedComponent)

    // 同时更新页面配置中的按钮列表
    const currentButtons = getFieldValue({ key: fieldKey }) || []
    const updatedButtons = [...currentButtons, { text: 'New Button', variant: 'flat' }]
    console.log('📄 更新页面配置中的按钮列表:', updatedButtons)
  } else if (fieldKey === 'filters') {
    // 为筛选字段添加，需要创建新的筛选组件
    const currentFilters = getFieldValue({ key: fieldKey }) || []
    const newFieldName = `field${currentFilters.length + 1}`
    
    const newFilter = {
      type: 'VCol',
      props: {
        cols: 12,
        sm: 4,
        xl: 3,
        xxl: 2
      },
      children: [{
        type: 'AppTextField',
        model: `{{searchQuery.${newFieldName}}}`,
        props: {
          label: 'New Field',
          placeholder: `Search ${newFieldName}`,
          clearable: true,
          variant: 'outlined'
        },
        events: {
          'update:modelValue': [{ action: 'handleSearch' }]
        }
      }]
    }

    // 递归查找 FilterPanel 并添加新的筛选字段
    function addFilterToComponent(component) {
      if (component.type === 'FilterPanel') {
        component.children = component.children || []
        component.children.push(newFilter)
        return true
      }

      // 递归查找子组件
      if (component.children) {
        for (const child of component.children) {
          if (addFilterToComponent(child)) {
            return true
          }
        }
      }

      return false
    }

    const updatedComponent = { ...props.selectedComponent }
    addFilterToComponent(updatedComponent)
    emitComponentUpdateWithHistory(updatedComponent)

    // 同时更新页面配置中的筛选字段列表
    const updatedFilters = [...currentFilters, { 
      field: newFieldName, 
      label: 'New Field', 
      type: 'text' // 新增字段默认为文本类型，后续可根据需要调整
    }]
    console.log('📄 更新页面配置中的筛选字段列表:', updatedFilters)
  } else if (fieldKey === 'columns') {
    // 为表格列添加，需要创建新的列配置
    const newColumn = {
      key: `column_${Date.now()}`,
      title: 'New Column',
      sortable: true,
      width: 'auto'
    }

    // 递归查找 VDataTableServer 并添加新的列
    function addColumnToComponent(component) {
      if (component.type === 'VDataTableServer' && component.props?.headers) {
        let headers = component.props.headers

        // 如果 headers 是模板表达式，需要更新数据源
        if (typeof headers === 'string' && headers.startsWith('{{') && headers.endsWith('}}')) {
          const dataKey = headers.replace(/[{}]/g, '').trim()
          console.log('➕ 检测到模板表达式，更新数据源:', dataKey)

          // 从页面配置中获取当前 headers
          let currentHeaders = []
          if (props.pageConfig?.data?.[dataKey]) {
            currentHeaders = props.pageConfig.data[dataKey]
          } else if (component.data?.[dataKey]) {
            currentHeaders = component.data[dataKey]
          }

          const actionColumns = currentHeaders.pop()

          // 添加新列到数据源
          const updatedHeaders = [...currentHeaders, newColumn, actionColumns]

          // 更新页面配置中的数据
          if (props.pageConfig?.data) {
            props.pageConfig.data[dataKey] = updatedHeaders
            console.log('➕ 更新 pageConfig.data:', updatedHeaders)
          }

          // 同时更新组件结构中的 headers 属性（保持模板表达式）
          return { ...component, props: { ...component.props, headers } }
        } else if (Array.isArray(headers)) {
          // 直接处理数组形式的 headers
          const updatedHeaders = [...headers, newColumn]
          return { ...component, props: { ...component.props, headers: updatedHeaders } }
        }
      }

      // 递归查找子组件
      if (component.children) {
        return {
          ...component,
          children: component.children.map(child => addColumnToComponent(child))
        }
      }

      return component
    }

    const updatedComponent = addColumnToComponent(props.selectedComponent)
    emitComponentUpdateWithHistory(updatedComponent)

    // 同时更新页面配置中的列列表
    const currentColumns = getFieldValue({ key: fieldKey }) || []
    const updatedColumns = [...currentColumns, { title: 'New Column', dataType: 'text' }]
    console.log('📄 更新页面配置中的列列表:', updatedColumns)
  } else {
    // 其他类型的列表项添加
    const currentItems = getFieldValue({ key: fieldKey }) || []
    let newItem = {}

    switch (fieldKey) {
      case 'columns':
        newItem = { key: '', title: '', sortable: false, width: '' }
        break
    }

    const updatedItems = [...currentItems, newItem]
    updateField(fieldKey, updatedItems)
  }
}

function removeListItem(fieldKey, index) {
  if (fieldKey === 'buttons') {
    // 为按钮删除，需要从组件结构中移除对应的 VBtn
    const currentButtons = getFieldValue({ key: fieldKey }) || []
    const buttonToRemove = currentButtons[index]
    console.log('🗑️ 准备删除按钮:', buttonToRemove, '索引:', index)

    let buttonFoundAndRemoved = false

    function removeButtonFromComponent(component, path = []) {
      if (component.type === 'VBtn') {
        // 使用更精确的匹配逻辑
        const componentId = component.id || `btn_${path.join('_')}`
        const isTargetButton =
          // 优先使用ID匹配
          (buttonToRemove._id && componentId === buttonToRemove._id) ||
          // 如果没有ID，使用路径匹配
          (buttonToRemove._path && JSON.stringify(path) === JSON.stringify(buttonToRemove._path)) ||
          // 最后使用属性匹配（但要求更严格）
          (component.props?.text === buttonToRemove.text &&
           component.props?.variant === buttonToRemove.variant &&
           !buttonFoundAndRemoved) // 确保只删除第一个匹配的

        if (isTargetButton) {
          console.log('✅ 找到要删除的按钮，路径:', path)
          buttonFoundAndRemoved = true
          return null // 标记为删除
        }
      }

      // 递归处理子组件
      if (component.children) {
        const updatedChildren = component.children
          .map((child, childIndex) => removeButtonFromComponent(child, [...path, childIndex]))
          .filter(child => child !== null)

        return {
          ...component,
          children: updatedChildren
        }
      }

      return component
    }

    const updatedComponent = removeButtonFromComponent(props.selectedComponent)

    if (buttonFoundAndRemoved) {
      emitComponentUpdateWithHistory(updatedComponent)
      console.log('✅ 按钮删除成功')
    } else {
      console.warn('⚠️ 未找到要删除的按钮:', buttonToRemove)
    }

    // 同时更新页面配置中的按钮列表
    const updatedButtons = currentButtons.filter((_, i) => i !== index)
    console.log('📄 更新页面配置中的按钮列表:', updatedButtons)
  } else if (fieldKey === 'filters') {
    // 为筛选字段删除，需要从组件结构中移除对应的 VCol
    const currentFilters = getFieldValue({ key: fieldKey }) || []
    const filterToRemove = currentFilters[index]
    console.log('🗑️ 删除筛选字段:', filterToRemove, '索引:', index)

    function removeFilterFromComponent(component) {
      if (component.type === 'VCol' && component.children) {
        // 检查是否是我们要删除的筛选字段
        const filterComponent = component.children[0]
        if (filterComponent && filterComponent.model) {
          // 从 model 中提取字段名
          const fieldMatch = filterComponent.model.match(/{{searchQuery\.(.+?)}}/)
          const fieldName = fieldMatch ? fieldMatch[1] : ''
          console.log("fieldName", fieldName, filterComponent)
          
          // 使用字段名匹配，确保删除正确的字段
          // 由于 hideLabels: true，默认字段没有 label 属性，所以只使用字段名匹配
          if (fieldName === filterToRemove.field) {
            console.log('🗑️ 找到要删除的筛选字段:', fieldName, filterToRemove)
            return null // 标记为删除
          }
        }
      }

      // 递归处理子组件
      if (component.children) {
        const updatedChildren = component.children
          .map(removeFilterFromComponent)
          .filter(child => child !== null)

        return {
          ...component,
          children: updatedChildren
        }
      }

      return component
    }

    const updatedComponent = removeFilterFromComponent(props.selectedComponent)
    emitComponentUpdateWithHistory(updatedComponent)

    // 同时更新页面配置中的筛选字段列表
    const updatedFilters = currentFilters.filter((_, i) => i !== index)
    console.log('📄 更新页面配置中的筛选字段列表:', updatedFilters)
  } else if (fieldKey === 'columns') {
    // 为表格列删除，需要从组件结构中移除对应的列
    const currentColumns = getFieldValue({ key: fieldKey }) || []
    const columnToRemove = currentColumns[index]
    console.log('🗑️ 删除表格列:', columnToRemove, '索引:', index)

    function removeColumnFromComponent(component) {
      if (component.type === 'VDataTableServer' && component.props?.headers) {
        let headers = component.props.headers

        // 如果 headers 是模板表达式，需要更新数据源
        if (typeof headers === 'string' && headers.startsWith('{{') && headers.endsWith('}}')) {
          const dataKey = headers.replace(/[{}]/g, '').trim()
          console.log('🗑️ 检测到模板表达式，更新数据源:', dataKey)

          // 从页面配置中获取当前 headers
          let currentHeaders = []
          if (props.pageConfig?.data?.[dataKey]) {
            currentHeaders = props.pageConfig.data[dataKey]
          } else if (component.data?.[dataKey]) {
            currentHeaders = component.data[dataKey]
          }

          if (Array.isArray(currentHeaders)) {
            // 先过滤出非特殊列，然后根据索引删除
            const normalHeaders = currentHeaders.filter(header =>
              header.key && header.key !== 'data-table-select' && header.key !== 'actions'
            )

            if (index >= 0 && index < normalHeaders.length) {
              const headerToRemove = normalHeaders[index]
              console.log('🗑️ 要删除的列:', headerToRemove)

              // 从原始 headers 中删除这一列
              const updatedHeaders = currentHeaders.filter(header =>
                !(header.key === headerToRemove.key && header.title === headerToRemove.title)
              )

              // 更新页面配置中的数据
              if (props.pageConfig?.data) {
                props.pageConfig.data[dataKey] = updatedHeaders
                console.log('🗑️ 更新 pageConfig.data:', updatedHeaders)
              }
            }

            // 同时更新组件结构中的 headers 属性（保持模板表达式）
            return { ...component, props: { ...component.props, headers } }
          }
        } else if (Array.isArray(headers)) {
          // 直接处理数组形式的 headers
          // 先过滤出非特殊列，然后根据索引删除
          const normalHeaders = headers.filter(header =>
            header.key && header.key !== 'data-table-select' && header.key !== 'actions'
          )

          if (index >= 0 && index < normalHeaders.length) {
            const headerToRemove = normalHeaders[index]
            console.log('🗑️ 要删除的列:', headerToRemove)

            // 从原始 headers 中删除这一列
            const updatedHeaders = headers.filter(header =>
              !(header.key === headerToRemove.key && header.title === headerToRemove.title)
            )

            return { ...component, props: { ...component.props, headers: updatedHeaders } }
          }
        }
      }

      // 递归处理子组件
      if (component.children) {
        return {
          ...component,
          children: component.children.map(child => removeColumnFromComponent(child))
        }
      }

      return component
    }

    const updatedComponent = removeColumnFromComponent(props.selectedComponent)
    emitComponentUpdateWithHistory(updatedComponent)

    // 同时更新页面配置中的列列表
    const updatedColumns = currentColumns.filter((_, i) => i !== index)
    console.log('📄 更新页面配置中的列列表:', updatedColumns)
  } else {
    // 其他类型的列表项删除
    const currentItems = getFieldValue({ key: fieldKey }) || []
    const updatedItems = currentItems.filter((_, i) => i !== index)
    updateField(fieldKey, updatedItems)
  }
}

function updateListItem(fieldKey, index, subKey, value) {
  console.log('🔄 更新列表项:', fieldKey, index, subKey, value)
  console.log('🔄 当前筛选字段列表:', getFieldValue({ key: 'filters' }))

  if (fieldKey === 'buttons') {
    // 按钮的特殊处理 - 直接更新组件结构
    updateButtonProperty(index, subKey, value)
  } else if (fieldKey === 'filters') {
    // 筛选字段的特殊处理 - 直接更新组件结构
    console.log('🔄 调用 updateFilterProperty:', index, subKey, value)
    updateFilterProperty(index, subKey, value)
  } else if (fieldKey === 'columns') {
    // 表格列的特殊处理 - 直接更新组件结构
    updateColumnProperty(index, subKey, value)
  } else {
    // 其他列表项的正常处理
    const currentItems = getFieldValue({ key: fieldKey }) || []
    const updatedItems = currentItems.map((item, i) =>
      i === index ? { ...item, [subKey]: value } : item
    )
    updateField(fieldKey, updatedItems)
  }
}

// 其他组件方法（占位符）
function getFilterPanelFilters() {
  // 从组件结构中获取筛选字段配置
  if (!props.selectedComponent) {
    console.log('🔍 getFilterPanelFilters: 没有选中的组件')
    return []
  }

  console.log('🔍 getFilterPanelFilters: 开始分析组件:', props.selectedComponent)

  // 递归查找 FilterPanel 中的筛选字段
  const filters = []

  function findFilters(component) {
    console.log('🔍 检查组件:', component.type, component)

    if (component.type === 'VCol' && component.children) {
      // 检查是否是筛选字段的 VCol
      const filterComponent = component.children[0]
      console.log('🔍 检查 VCol 的子组件:', filterComponent)

      if (filterComponent) {
        let fieldName = ''
        let displayName = ''
        let fieldType = 'text'

        // 尝试从 model 中提取字段名
        if (filterComponent.model) {
          const fieldMatch = filterComponent.model.match(/{{searchQuery\.(.+?)}}/)
          const extractedName = fieldMatch ? fieldMatch[1] : ''
          // 过滤掉临时字段名（以temp_开头的）
          fieldName = extractedName && !extractedName.startsWith('temp_') ? extractedName : ''
          console.log('🔍 从 model 提取的字段名:', fieldName, '从 model:', filterComponent.model)
        }

        // 如果没有 model 属性，尝试从其他属性中提取
        if (!fieldName) {
          // 从 placeholder 中提取字段名
          if (filterComponent.props?.placeholder) {
            const placeholderMatch = filterComponent.props.placeholder.match(/Search(.+)/)
            fieldName = placeholderMatch ? placeholderMatch[1] : filterComponent.props.placeholder
            console.log('🔍 从 placeholder 提取的字段名:', fieldName)
          }
          
          // 从 label 中提取字段名
          if (!fieldName && filterComponent.props?.label) {
            fieldName = filterComponent.props.label
            console.log('🔍 从 label 提取的字段名:', fieldName)
          }
        }

        // 即使字段名为空，也要识别为筛选字段，以便后续可以重新赋值
        // 由于 hideLabels: true，默认字段没有 label 属性，使用字段名作为显示名称
        displayName = filterComponent.props?.label || fieldName || '未命名字段'

        // 根据组件类型确定字段类型
        if (filterComponent.type === 'VSelect' || filterComponent.type === 'AppSelect') {
          fieldType = 'select'
        } else if (filterComponent.type === 'VDatePicker' || filterComponent.type === 'AppDateTimePicker') {
          fieldType = 'date'
        } else if (filterComponent.type === 'VTextField' || filterComponent.type === 'AppTextField') {
          fieldType = 'text'
        } else if (filterComponent.type === 'VSwitch') {
          fieldType = 'boolean'
        } else if (filterComponent.type === 'VTextarea') {
          fieldType = 'textarea'
        } else {
          // 默认类型
          fieldType = 'text'
        }

        const filterConfig = {
          field: fieldName, // 允许字段名为空字符串
          label: displayName,
          type: fieldType
        }

        console.log('🔍 添加筛选字段配置:', filterConfig, '索引:', filters.length)
        filters.push(filterConfig)
      }
    }

    // 递归查找子组件
    if (component.children) {
      component.children.forEach(findFilters)
    }
  }

  findFilters(props.selectedComponent)
  console.log('🔍 最终获取到的筛选字段:', filters)
  return filters
}

function getDataTableColumns() {
  // 触发强制刷新检查
  forceRefresh.value

  // 从组件结构中获取表格列配置
  if (!props.selectedComponent) {
    console.log('🔍 getDataTableColumns: 没有选中的组件')
    return []
  }

  console.log('🔍 getDataTableColumns: 开始分析组件:', props.selectedComponent)
  console.log('🔍 getDataTableColumns: 当前页面配置:', props.pageConfig)
  console.log('🔍 getDataTableColumns: 强制刷新标记:', forceRefresh.value)

  // 递归查找 DataTable 中的列配置
  const columns = []

  function findColumns(component) {
    console.log('🔍 检查组件:', component.type, component)

    if (component.type === 'VDataTableServer') {
      console.log('🔍 找到 VDataTableServer，props:', component.props)

      // 检查是否有 headers 属性
      if (component.props?.headers) { 
        let headers = component.props.headers

        // 如果 headers 是模板表达式，尝试从 data 中获取
        if (typeof headers === 'string' && headers.startsWith('{{') && headers.endsWith('}}')) {
          const dataKey = headers.replace(/[{}]/g, '').trim()
          console.log('🔍 检测到模板表达式，尝试从 data 获取:', dataKey)

          // 从页面配置或组件数据中获取 headers
          if (props.pageConfig?.data?.[dataKey]) {
            headers = props.pageConfig.data[dataKey]
            console.log('🔍 从 pageConfig.data 获取到 headers:', headers)
            console.log('🔍 pageConfig.data 完整内容:', props.pageConfig.data)
          } else if (component.data?.[dataKey]) {
            headers = component.data[dataKey]
            console.log('🔍 从 component.data 获取到 headers:', headers)
          } else {
            console.log('🔍 无法找到 headers 数据，使用默认列配置')
            console.log('🔍 dataKey:', dataKey)
            console.log('🔍 pageConfig.data:', props.pageConfig?.data)
            console.log('🔍 component.data:', component.data)
          }
        }

        console.log('🔍 处理后的 headers:', headers)

        if (Array.isArray(headers)) {
          headers.forEach((header, index) => {
            console.log('🔍 检查 header:', index, header)
            if (header.key && header.key !== 'data-table-select' && header.key !== 'actions') {
              const columnConfig = {
                key: header.key,
                title: header.title || header.key,
                dataType: getDataTypeFromHeader(header)
              }
              console.log('🔍 添加列配置:', columnConfig)
              columns.push(columnConfig)
            } else {
              console.log('🔍 跳过特殊列:', header.key)
            }
          })
        }
      }
    }

    // 递归查找子组件
    if (component.children) {
      component.children.forEach(findColumns)
    }
  }

  findColumns(props.selectedComponent)
  console.log('🔍 最终获取到的表格列:', columns)
  return columns
}

function getFormFieldsList() {
  // 优先从组件的 data 中获取字段列表
  if (props.selectedComponent?.data?.fields) {
    console.log('🔍 从组件 data 获取表单字段列表:', props.selectedComponent.data.fields)
    return props.selectedComponent.data.fields
  }

  // 如果没有 data.fields，则从组件结构中解析
  const fields = []

  // 递归遍历组件树，查找表单字段
  function findFormFields(component) {
    if (!component) return

    // 检查是否是表单字段组件
    if (isFormFieldComponent(component)) {
      const field = extractFieldInfo(component)
      if (field) {
        fields.push(field)
      }
    }

    // 递归检查子组件
    if (component.children && Array.isArray(component.children)) {
      component.children.forEach(findFormFields)
    }
  }

  // 判断是否是表单字段组件
  function isFormFieldComponent(component) {
    const fieldTypes = [
      'VTextField', 'VTextarea', 'VSelect', 'VCheckbox', 'VRadioGroup',
      'VSwitch', 'VSlider', 'VDatePicker', 'VTimePicker', 'VFileInput',
      'VAutocomplete', 'VCombobox', 'VNumberField'
    ]
    return fieldTypes.includes(component.type)
  }

  // 从组件中提取字段信息
  function extractFieldInfo(component) {
    const props = component.props || {}
    
    return {
      key: props.model || props.name || component.key || '',
      label: props.label || props.placeholder || '',
      type: getFieldType(component),
      required: props.required || false,
      placeholder: props.placeholder || '',
      disabled: props.disabled || false,
      readonly: props.readonly || false,
      rules: props.rules || [],
      options: props.items || props.options || [],
      componentType: component.type
    }
  }

  // 根据组件类型推断字段类型
  function getFieldType(component) {
    const type = component.type || ''
    
    if (type.includes('Textarea')) return 'textarea'
    if (type.includes('Select') || type.includes('Combobox') || type.includes('Autocomplete')) return 'select'
    if (type.includes('Checkbox')) return 'checkbox'
    if (type.includes('Radio')) return 'radio'
    if (type.includes('Switch')) return 'switch'
    if (type.includes('Slider')) return 'slider'
    if (type.includes('Date')) return 'date'
    if (type.includes('Time')) return 'time'
    if (type.includes('File')) return 'file'
    if (type.includes('Number')) return 'number'
    
    return 'text'
  }

  // 从选中的组件开始遍历
  findFormFields(props.selectedComponent)
  
  console.log('🔍 解析到的表单字段列表:', fields)
  return fields
}

function getFormFields() {
  const fields = []

  // 递归遍历组件树，查找表单字段
  function findFormFields(component) {
    if (!component) return

    // 检查是否是表单字段组件
    if (isFormFieldComponent(component)) {
      const field = extractFieldInfo(component)
      if (field) {
        fields.push(field)
      }
    }

    // 递归检查子组件
    if (component.children && Array.isArray(component.children)) {
      component.children.forEach(findFormFields)
    }
  }

  // 判断是否是表单字段组件
  function isFormFieldComponent(component) {
    const fieldTypes = [
      'VTextField', 'VTextarea', 'VSelect', 'VCheckbox', 'VRadioGroup',
      'VSwitch', 'VSlider', 'VDatePicker', 'VTimePicker', 'VFileInput',
      'VAutocomplete', 'VCombobox', 'VNumberField'
    ]
    return fieldTypes.includes(component.type)
  }

  // 从组件中提取字段信息
  function extractFieldInfo(component) {
    const props = component.props || {}
    
    return {
      key: props.model || props.name || component.key || '',
      label: props.label || props.placeholder || '',
      type: getFieldType(component),
      required: props.required || false,
      placeholder: props.placeholder || '',
      disabled: props.disabled || false,
      readonly: props.readonly || false,
      rules: props.rules || [],
      options: props.items || props.options || [],
      componentType: component.type
    }
  }

  // 根据组件类型推断字段类型
  function getFieldType(component) {
    const type = component.type || ''
    
    if (type.includes('Textarea')) return 'textarea'
    if (type.includes('Select') || type.includes('Combobox') || type.includes('Autocomplete')) return 'select'
    if (type.includes('Checkbox')) return 'checkbox'
    if (type.includes('Radio')) return 'radio'
    if (type.includes('Switch')) return 'switch'
    if (type.includes('Slider')) return 'slider'
    if (type.includes('Date')) return 'date'
    if (type.includes('Time')) return 'time'
    if (type.includes('File')) return 'file'
    if (type.includes('Number')) return 'number'
    
    return 'text'
  }

  // 从选中的组件开始遍历
  findFormFields(props.selectedComponent)
  
  console.log('🔍 解析到的表单字段:', fields)
  return fields
}
function getDataTypeFromHeader(header) {
  // 根据 header 信息推断数据类型
  const key = header.key?.toLowerCase() || ''
  const title = header.title?.toLowerCase() || ''

  if (key.includes('date') || title.includes('日期') || title.includes('时间')) {
    return 'date'
  }
  if (key.includes('amount') || title.includes('金额') || title.includes('价格')) {
    return 'amount'
  }
  if (key.includes('currency') || title.includes('币种')) {
    return 'currency'
  }
  if (key.includes('progress') || title.includes('进度')) {
    return 'progress'
  }
  if (key.includes('status') || title.includes('状态')) {
    return 'status'
  }
  if (key.includes('url') || title.includes('链接')) {
    return 'link'
  }
  if (key.includes('number') || title.includes('数量') || title.includes('数字')) {
    return 'number'
  }

  return 'text'
}

function getDataTableOptions() {
  return {
    showSelect: false,
    loading: false,
    itemsPerPage: 10
  }
}

// 从页面配置中提取字段信息
function getFieldsFromPageConfig(pageConfig) {
  const fields = []
  
  if (!pageConfig) return fields
  
  // 从页面级别的 data 中提取字段
  if (pageConfig.data && pageConfig.data.fields) {
    fields.push(...pageConfig.data.fields)
  }
  
  // 从组件中提取字段
  if (pageConfig.components) {
    pageConfig.components.forEach(component => {
      if (component.data && component.data.fields) {
        fields.push(...component.data.fields)
      }
      if (component.data && component.data.columns) {
        fields.push(...component.data.columns)
      }
      if (component.data && component.data.filters) {
        fields.push(...component.data.filters)
      }
    })
  }
  
  return fields
}

// 从组件配置中提取字段信息
function getFieldsFromComponentConfig(componentConfig) {
  const fields = []
  
  if (!componentConfig) return fields
  
  // 从组件的 data 中提取字段
  if (componentConfig.data) {
    if (componentConfig.data.fields) {
      fields.push(...componentConfig.data.fields)
    }
    if (componentConfig.data.columns) {
      fields.push(...componentConfig.data.columns)
    }
    if (componentConfig.data.filters) {
      fields.push(...componentConfig.data.filters)
    }
  }
  
  // 从 propertyConfig 中提取字段信息
  if (componentConfig.propertyConfig) {
    componentConfig.propertyConfig.sections.forEach(section => {
      section.fields.forEach(field => {
        if (field.type === 'list' && field.defaultValue) {
          fields.push(...field.defaultValue)
        }
      })
    })
  }
  
  return fields
}

// 通用字段提取函数 - 可以处理各种 JSON 结构
function extractFieldsFromJson(jsonData, options = {}) {
  const fields = []
  const { 
    fieldKeys = ['fields', 'columns', 'filters', 'items'], 
    keyPaths = ['data', 'propertyConfig.sections', 'children'] 
  } = options
  
  function traverse(obj, path = '') {
    if (!obj || typeof obj !== 'object') return
    
    // 检查当前对象是否包含字段数组
    for (const key of fieldKeys) {
      if (Array.isArray(obj[key])) {
        fields.push(...obj[key].map(field => ({
          ...field,
          source: path,
          sourceKey: key
        })))
      }
    }
    
    // 递归遍历指定路径
    for (const keyPath of keyPaths) {
      const keys = keyPath.split('.')
      let current = obj
      
      for (const key of keys) {
        if (current && typeof current === 'object' && key in current) {
          current = current[key]
        } else {
          current = null
          break
        }
      }
      
      if (current) {
        if (Array.isArray(current)) {
          current.forEach((item, index) => {
            traverse(item, `${path}.${keyPath}[${index}]`)
          })
        } else if (typeof current === 'object') {
          traverse(current, `${path}.${keyPath}`)
        }
      }
    }
    
    // 递归遍历所有子对象
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null) {
        traverse(value, path ? `${path}.${key}` : key)
      }
    }
  }
  
  traverse(jsonData)
  return fields
}

// 从页面配置中提取所有字段（包括嵌套的）
function getAllFieldsFromPage(pageConfig) {
  return extractFieldsFromJson(pageConfig, {
    fieldKeys: ['fields', 'columns', 'filters', 'items', 'buttons'],
    keyPaths: ['data', 'propertyConfig.sections', 'children', 'components']
  })
}

// 通用组件更新方法
function updateComponentProperty(key, value) {
  // 保存旧状态（深拷贝）
  const oldComponent = JSON.parse(JSON.stringify(props.selectedComponent))
  
  // 更新组件属性
  const updatedComponent = { ...props.selectedComponent, [key]: value }
  
  emitComponentUpdateWithHistory(updatedComponent)
}

function updateComponentProp(key, value) {
  // 保存旧状态（深拷贝）
  const oldComponent = JSON.parse(JSON.stringify(props.selectedComponent))
  
  const currentProps = props.selectedComponent.props || {}
  const updatedComponent = {
    ...props.selectedComponent,
    props: { ...currentProps, [key]: value }
  }
  
  emitComponentUpdateWithHistory(updatedComponent)
}

function updateComponentStyle(key, value) {
  // 保存旧状态（深拷贝）
  const oldComponent = JSON.parse(JSON.stringify(props.selectedComponent))
  
  const currentStyle = props.selectedComponent.style || {}
  const updatedComponent = {
    ...props.selectedComponent,
    style: { ...currentStyle, [key]: value }
  }
  
  emitComponentUpdateWithHistory(updatedComponent)
}

// 页面配置更新
function updatePageConfig(key, value) {
  emit('update:page-config', key, value)
}

function clearSelection() {
  emit('component-update', null, { action: 'clear-selection' })
}

function updateColumnProperty(columnIndex, property, value) {
  console.log('🔧 更新表格列属性:', columnIndex, property, value)
  console.log('🔧 当前页面配置:', props.pageConfig)
  console.log('🔧 当前选中组件:', props.selectedComponent)

  function updateColumnInComponent(component, currentIndex = 0) {
    console.log('🔧 updateColumnInComponent 被调用，组件类型:', component.type)
    console.log('🔧 组件 props.headers:', component.props?.headers)

    if (component.type === 'VDataTableServer' && component.props?.headers) {
      let headers = component.props.headers
      console.log('🔧 找到 VDataTableServer，headers 类型:', typeof headers, 'headers:', headers)

      // 如果 headers 是模板表达式，需要更新数据源
      if (typeof headers === 'string' && headers.startsWith('{{') && headers.endsWith('}}')) {
        const dataKey = headers.replace(/[{}]/g, '').trim()
        console.log('🔧 检测到模板表达式，更新数据源:', dataKey)

        // 从页面配置中获取当前 headers
        let currentHeaders = []
        if (props.pageConfig?.data?.[dataKey]) {
          currentHeaders = props.pageConfig.data[dataKey]
        } else if (component.data?.[dataKey]) {
          currentHeaders = component.data[dataKey]
        }

        if (Array.isArray(currentHeaders)) {
          // 先过滤出非特殊列，然后根据索引更新
          const normalHeaders = currentHeaders.filter(header =>
            header.key && header.key !== 'data-table-select' && header.key !== 'actions'
          )

          if (columnIndex >= 0 && columnIndex < normalHeaders.length) {
            const headerToUpdate = normalHeaders[columnIndex]
            console.log('🔧 要更新的列:', headerToUpdate)

            // 使用索引匹配而不是内容匹配，避免清空字段后匹配失败
            let normalHeaderIndex = 0
            const updatedHeaders = currentHeaders.map(header => {
              // 如果是普通列（非特殊列）
              if (header.key && header.key !== 'data-table-select' && header.key !== 'actions') {
                const currentIndex = normalHeaderIndex
                normalHeaderIndex++ // 先递增索引

                if (currentIndex === columnIndex) {
                  console.log('🔧 更新表格列:', property, value, '索引:', currentIndex)
                  if (property === 'title') {
                    return { ...header, title: value }
                  } else if (property === 'dataType') {
                    // 根据数据类型更新列的配置
                    const updatedHeader = { ...header }
                    switch (value) {
                      case 'date':
                        updatedHeader.type = 'date'
                        break
                      case 'amount':
                        updatedHeader.type = 'currency'
                        break
                      case 'currency':
                        updatedHeader.type = 'currency'
                        break
                      case 'progress':
                        updatedHeader.type = 'progress'
                        break
                      case 'status':
                        updatedHeader.type = 'status'
                        break
                      case 'link':
                        updatedHeader.type = 'link'
                        break
                      case 'number':
                        updatedHeader.type = 'number'
                        break
                      default:
                        updatedHeader.type = 'text'
                    }
                    return updatedHeader
                  }
                }
              }
              return header
            })

            // 更新页面配置中的数据
            console.log('🔧 检查页面配置更新条件:')
            console.log('🔧 props.pageConfig:', !!props.pageConfig)
            console.log('🔧 props.pageConfig.data:', !!props.pageConfig?.data)

            if (props.pageConfig?.data) {
              const updatedPageConfig = {
                ...props.pageConfig,
                data: {
                  ...props.pageConfig.data,
                  [dataKey]: updatedHeaders
                }
              }
              console.log('🔧 更新 pageConfig.data:', updatedHeaders)
              console.log('🔧 即将发送 update:page-config 事件:', updatedPageConfig)
              emit('update:page-config', updatedPageConfig)
            } else {
              console.log('🔧 ❌ 页面配置更新条件不满足')
            }
          }

          // 同时更新组件结构中的 headers 属性（保持模板表达式）
          return { ...component, props: { ...component.props, headers } }
        }
      } else if (Array.isArray(headers)) {
        // 直接处理数组形式的 headers
        // 先过滤出非特殊列，然后根据索引更新
        const normalHeaders = headers.filter(header =>
          header.key && header.key !== 'data-table-select' && header.key !== 'actions'
        )

        if (columnIndex >= 0 && columnIndex < normalHeaders.length) {
          const headerToUpdate = normalHeaders[columnIndex]
          console.log('🔧 要更新的列:', headerToUpdate)

          // 使用索引匹配而不是内容匹配
          let normalHeaderIndex = 0
          const updatedHeaders = headers.map(header => {
            // 如果是普通列（非特殊列）
            if (header.key && header.key !== 'data-table-select' && header.key !== 'actions') {
              const currentIndex = normalHeaderIndex
              normalHeaderIndex++ // 先递增索引

              if (currentIndex === columnIndex) {
                console.log('🔧 更新表格列:', property, value, '索引:', currentIndex)
                if (property === 'title') {
                  return { ...header, title: value }
                } else if (property === 'dataType') {
                  // 根据数据类型更新列的配置
                  const updatedHeader = { ...header }
                  switch (value) {
                    case 'date':
                      updatedHeader.type = 'date'
                      break
                    case 'amount':
                      updatedHeader.type = 'currency'
                      break
                    case 'currency':
                      updatedHeader.type = 'currency'
                      break
                    case 'progress':
                      updatedHeader.type = 'progress'
                      break
                    case 'status':
                      updatedHeader.type = 'status'
                      break
                    case 'link':
                      updatedHeader.type = 'link'
                      break
                    case 'number':
                      updatedHeader.type = 'number'
                      break
                    default:
                      updatedHeader.type = 'text'
                  }
                  return updatedHeader
                }
              }
            }
            return header
          })
          return { ...component, props: { ...component.props, headers: updatedHeaders } }
        }
      }
    }

    if (component.children) {
      return {
        ...component,
        children: component.children.map(child => updateColumnInComponent(child, currentIndex))
      }
    }

    return component
  }

  const updatedComponent = updateColumnInComponent(props.selectedComponent)
  emitComponentUpdateWithHistory(updatedComponent)

  // 强制刷新列配置，确保UI显示最新的数据
  nextTick(() => {
    forceRefresh.value++
    console.log('🔧 表格列更新完成，当前列配置:', getDataTableColumns())
    console.log('🔧 更新后的页面配置:', props.pageConfig)
  })
}
</script>

<style scoped>
.properties-panel {
  width: 420px;
  min-width: 420px;
  max-width: 420px;
  background: #2d2d2d;
  border-left: 1px solid #404040;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
  flex-shrink: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #404040;
}

.panel-title {
  display: flex;
  align-items: center;
  color: #ffffff;
  font-weight: 600;
  font-size: 14px;
}

.panel-actions {
  display: flex;
  gap: 4px;
}

.panel-content {
  padding: 12px 16px;
}

.page-properties,
.component-properties {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.component-info {
  margin-bottom: 12px;
}

.component-header {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #2d2d2d;
  border-radius: 6px;
  border: 1px solid #404040;
}

.component-name {
  margin: 0;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.component-type {
  margin: 0;
  color: #888;
  font-size: 12px;
}

.properties-sections {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.property-section {
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 6px;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #222;
  border-bottom: 1px solid #404040;
}

.section-title {
  color: #cccccc;
  font-size: 12px;
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
}

.section-content {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
}

.field-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 12px;
  border-bottom: 1px solid #333;
}

.field-item:last-child {
  border-bottom: none;
}

.field-item:hover {
  background: #222;
}

.field-label {
  min-width: 120px;
  color: #888;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
}

.field-input {
  flex: 1;
  min-width: 0;
}

.list-field {
  display: flex;
  flex-direction: column;
  padding: 8px 12px;
  flex: 1;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.list-label {
  color: #888;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.list-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.list-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  background: #222;
  border: 1px solid #404040;
  border-radius: 4px;
}

.item-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 6px;
}

.item-field {
  flex: 1;
  min-width: 0;
}

/* Vuetify 样式覆盖 */
:deep(.v-text-field .v-field) {
  background: #2a2a2a !important;
  border: 1px solid #444 !important;
  border-radius: 4px !important;
  height: 32px !important;
}

:deep(.v-text-field .v-field__input) {
  color: white !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
}

:deep(.v-text-field .v-label) {
  color: #888 !important;
  font-size: 12px !important;
}

:deep(.v-select .v-field) {
  background: #2a2a2a !important;
  border: 1px solid #444 !important;
  border-radius: 4px !important;
  height: 32px !important;
}

:deep(.v-select .v-field__input) {
  color: white !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
}

/* 属性组中的输入控件样式 */
.property-section :deep(.v-text-field .v-field) {
  background: #333 !important;
  border: 1px solid #555 !important;
}

.property-section :deep(.v-select .v-field) {
  background: #333 !important;
  border: 1px solid #555 !important;
}

/* 列表项中的输入控件样式 */
.list-item :deep(.v-text-field .v-field) {
  height: 28px !important;
  background: #333 !important;
  border: 1px solid #555 !important;
}

.list-item :deep(.v-text-field .v-field__input) {
  font-size: 11px !important;
  padding: 2px 6px !important;
}

.list-item :deep(.v-select .v-field) {
  height: 28px !important;
  background: #333 !important;
  border: 1px solid #555 !important;
}

.list-item :deep(.v-select .v-field__input) {
  font-size: 11px !important;
  padding: 2px 6px !important;
}

:deep(.v-switch .v-selection-control__input) {
  width: 36px;
  height: 20px;
}

:deep(.v-switch .v-selection-control__wrapper) {
  width: 36px;
  height: 20px;
}

:deep(.v-switch .v-label) {
  color: #888 !important;
  font-size: 12px !important;
}
</style>