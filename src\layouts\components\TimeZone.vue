<template>
  <div class="d-flex align-center me-2">
    Timezone- UTC{{ timezoneOffset }} 
  </div>
</template>

<script setup>
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

// 注册插件
dayjs.extend(utc)
dayjs.extend(timezone)

const currentTimezone = computed(() => {
  return dayjs.tz.guess()
})

const timezoneOffset = computed(() => {
  const offset = dayjs().tz(currentTimezone.value).utcOffset() / 60
  const sign = offset >= 0 ? '+' : '-'
  return `${sign}${Math.abs(offset).toString()}`
})
</script>

<style scoped></style>