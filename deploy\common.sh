#!/usr/bin/env bash
set -eu

#zgd,zgq,zgp
declare DeployEnv

#dev,qa,prod
declare ServiceEnv

declare Region

common_init()
{
  DeployEnv=$1
  ServiceEnv=$2
  Region=$3

  echo ">> common_init DeployEnv $DeployEnv ServiceEnv $ServiceEnv Region $Region"
}
export -f common_init

common_create_log()
{
  local logName=$1
  echo ">> common_create_log logName $logName"

  local logGroup
  logGroup=$(aws logs describe-log-groups --log-group-name-prefix "$logName" --region "$Region" | jq -r --arg name "$logName" '.logGroups[] | select(.logGroupName==$name) | .logGroupName')
  if [[ -z $logGroup ]]
  then
    echo ">> create logGroup $logGroup"
    aws logs create-log-group --log-group-name "$logName" --region "$Region" 
    
    if [[ $DeployEnv != "zgp" ]]
    then
      sleep 5
      aws logs put-retention-policy --log-group-name "$logName" --retention-in-days 7 --region "$Region"
    fi
  fi
}
export -f common_create_log

common_create_repository()
{
  local repoName=$1
  echo ">> common_create_repository repoName $repoName"
  
  #create repo if not present
  local repositoryUrl
  repositoryUrl=$(aws ecr describe-repositories --repository-names "$repoName" --region "$Region" | jq -r '.repositories[].repositoryUri')
  if [[ -z $repositoryUrl ]] 
  then
    aws ecr create-repository --repository-name "$repoName" --region "$Region"
  fi
}
export -f common_create_repository

common_create_taskdef()
{

  if [[ -f deploy/taskdef-$ServiceEnv.json ]]
  then
    echo ">> common_create_taskdef from deploy/taskdef-$ServiceEnv.json"
    mv deploy/taskdef-"$ServiceEnv".json  deploy/taskdef.json
  fi

  echo ">> common_create_taskdef from config/env-$ServiceEnv.json"
  if [[ -f config/volume-$ServiceEnv.json ]]
  then
    echo ">> common_create_taskdef volume from config/volume-$ServiceEnv.json"
    local vol
    vol=$(jq -c . config/volume-"$ServiceEnv".json)
    sed -e "s~\"%VOLUME%\"~${vol//&/\\&}~" deploy/taskdef.json > deploy/taskdef-"$ServiceEnv".json
    mv deploy/taskdef-"$ServiceEnv".json  deploy/taskdef.json
  fi

  local subs
  subs=$(jq -c . config/env-"$ServiceEnv".json)
  sed -e "s~\"%ENVIRONMENT%\"~${subs//&/\\&}~" deploy/taskdef.json > deploy/taskdef-"$ServiceEnv".json

  if [[ -f config/secret.json ]]
  then
    mv deploy/taskdef-"$ServiceEnv".json deploy/taskdef.json; 

    local -u deployEnv=$DeployEnv

    sed -e "s;%ENV%;$deployEnv;g" \
    config/secret.json > temp.json

    for k in $(jq -c '.[]' temp.json); do  
      valueFrom=$(echo "$k" | jq  -r '.valueFrom'); 
      arn=$(aws secretsmanager get-secret-value --secret-id "$valueFrom" --region "$Region" | jq -r '.ARN'); 
      sed -e "s;\"${valueFrom}\";\"${arn}\";g" temp.json > temp1.json;  
      mv temp1.json temp.json
    done
    mv temp.json config/secret.json
    local secrets
    secrets=$(jq -c . config/secret.json)
    sed -e "s~\"%SECRET%\"~${secrets//&/\\&}~" deploy/taskdef.json > deploy/taskdef-"$ServiceEnv".json
  fi
}
export -f common_create_taskdef

common_get_subnet()
{
  echo ">> common_get_subnet"

  local -u deployEnv=$DeployEnv

  local subnets
  case $DeployEnv in

    zgd)
    subnetA=$(aws ec2 describe-subnets --region "$Region" --filters Name=tag:Name,Values="$deployEnv"-SN-LAN-1a-OLD | jq -r '.Subnets[].SubnetId')
    subnetB=$(aws ec2 describe-subnets --region "$Region" --filters Name=tag:Name,Values="$deployEnv"-SN-LAN-1b-OLD | jq -r '.Subnets[].SubnetId')
    subnets=${subnetA},${subnetB}
    ;;

    zgs)
    subnetA=$(aws ec2 describe-subnets --region "$Region" --filters Name=tag:Name,Values="$deployEnv"-SN-LAN-1a | jq -r '.Subnets[].SubnetId')
    subnetB=$(aws ec2 describe-subnets --region "$Region" --filters Name=tag:Name,Values="$deployEnv"-SN-LAN-1b | jq -r '.Subnets[].SubnetId')
    subnets=${subnetA},${subnetB}
    ;;

    *)
    subnetA=$(aws ec2 describe-subnets --region "$Region" --filters Name=tag:Name,Values="$deployEnv"-SN-LAN-2a | jq -r '.Subnets[].SubnetId')
    subnetB=$(aws ec2 describe-subnets --region "$Region" --filters Name=tag:Name,Values="$deployEnv"-SN-LAN-2b | jq -r '.Subnets[].SubnetId')
    subnetC=$(aws ec2 describe-subnets --region "$Region" --filters Name=tag:Name,Values="$deployEnv"-SN-LAN-2c | jq -r '.Subnets[].SubnetId')
    subnetD=$(aws ec2 describe-subnets --region "$Region" --filters Name=tag:Name,Values="$deployEnv"-SN-LAN-2d | jq -r '.Subnets[].SubnetId')
    subnets=${subnetA},${subnetB},${subnetC},${subnetD}
    ;;
  esac
  
  RETURN_VALUE=$subnets
}
export -f common_get_subnet

common_get_security_group()
{
  echo ">> common_get_security_group"

  local -u deployEnv=$DeployEnv
  
  local securityGroup
  case $DeployEnv in
    zgd)
    securityGroup=$(aws ec2 describe-security-groups --region "$Region" --filters Name=tag:Name,Values="$deployEnv"-SG-INTRANET | jq -r '.SecurityGroups[].GroupId')
    ;;

    *)
    securityGroup=$(aws ec2 describe-security-groups --region "$Region" --filters Name=tag:Name,Values="$deployEnv"-SG-DEFAULT | jq -r '.SecurityGroups[].GroupId')
    ;;
  esac
  
  RETURN_VALUE=$securityGroup
}
export -f common_get_security_group

common_get_loadbalancer_target()
{
  local loadBalancer=$1
  local targetGroupName=$2
  local vpcId=$3
  local recordRoute=$4
  local hostedZone=$5
  local healthCheckPath=$6
  local slowStart=$7
  local condition=$8

   echo ">> common_get_loadbalancer_target loadBalancer $loadBalancer targetGroupName $targetGroupName"

  local dnsName
  local loadBalancerArn
  local canonicalHostedZoneId
  read -r dnsName loadBalancerArn canonicalHostedZoneId <<< "$(aws elbv2 describe-load-balancers --region "$Region" | jq -r --arg name "$loadBalancer" '.LoadBalancers[] | select(.LoadBalancerName==$name) | .DNSName + " " + .LoadBalancerArn + " " + .CanonicalHostedZoneId')"
  echo ">> loadBalancerArn $loadBalancerArn"
  echo ">> dnsName $dnsName"
  echo ">> canonicalHostedZoneId $canonicalHostedZoneId"

  echo ">> create target group"
  local timeout=5
  local healthyThresholdCount=5
  local unhealthyThresholdCount=2
  if [[ $slowStart == 1 ]]
  then 
    timeout=25
    healthyThresholdCount=2
    unhealthyThresholdCount=8
  fi

  local targetGroupArn
  
  targetGroupArn=$(aws elbv2 describe-target-groups --load-balancer-arn  "$loadBalancerArn" --region "$Region" --query "TargetGroups[?TargetGroupName == '$targetGroupName']" | jq -r '.[] .TargetGroupArn')
  echo ">> get targetGroupArn $targetGroupArn"
  if [[ -z $targetGroupArn ]]
  then
    targetGroupArn=$(aws elbv2 create-target-group --name "$targetGroupName" --protocol HTTPS --port 443 --target-type ip --vpc-id "$vpcId" --health-check-path "$healthCheckPath" --health-check-timeout-seconds $timeout --healthy-threshold-count $healthyThresholdCount --unhealthy-threshold-count $unhealthyThresholdCount --region "$Region" | jq -r '.TargetGroups[0].TargetGroupArn')
    echo ">> create targetGroupArn $targetGroupArn"
  fi
  
  echo ">> targetGroupArn $targetGroupArn"

  echo '>> Get listener'
  local listenerArn
  listenerArn=$(aws elbv2 describe-listeners --region "$Region" --load-balancer-arn "$loadBalancerArn" | jq -r '.Listeners[] | select(.Port==443) | .ListenerArn')
  echo ">> ListenerArn $listenerArn"

  local pathRoute
  pathRoute=$recordRoute.$hostedZone

  local hasExsitingRule=false
  if [[ -z $condition ]]
  then
    local existingRules
    existingRules=$(aws elbv2 describe-rules --listener-arn "$listenerArn" --region "$Region" --query "Rules[].Conditions[].HostHeaderConfig.Values" | jq -r 'flatten | join(",")')
    if [[ "$existingRules" == *$pathRoute* ]]; then
      hasExsitingRule=true
    fi
  else
    local existingRules
    existingRules=$(aws elbv2 describe-rules --listener-arn "$listenerArn" --region "$Region" --query "Rules[].Conditions[].PathPatternConfig.Values" | jq -r 'flatten | join(",")')
    if [[ "$existingRules" == *$condition* ]]; then
      hasExsitingRule=true
    fi
  fi

  echo ">> hasExsitingRule $hasExsitingRule"

  # if ! $hasExsitingRule
  # then
  #   echo ">> create rules"
    
  #   local rules
  #   if [[ -z $condition ]]
  #   then
  #     rules="[{\"Field\": \"host-header\", \"Values\":[\"$pathRoute\"]}]"
  #   else
  #     rules="[{\"Field\": \"host-header\", \"Values\":[\"$pathRoute\"]}, {\"Field\": \"path-pattern\", \"Values\":[\"$condition\"]}]"
  #   fi

  #   echo ">> rules $rules"
    
  #   echo ">> get rule priority"
  #   local rulePriority
  #   rulePriority=$(aws elbv2 describe-rules --listener-arn "$listenerArn" --query 'Rules[].Priority' --region "$Region" | jq '.[-2]')
  #   echo ">> RulePriority $rulePriority"
      
  #   echo increment
  #   rulePriority=$(echo "$rulePriority" | tr -d '"')
  #   rulePriority=$((rulePriority + 1))
  #   echo ">> RulePriority after increment $rulePriority"

  #   aws elbv2 create-rule --listener-arn "$listenerArn" --priority "$rulePriority" --conditions "$rules" --actions "Type=forward,TargetGroupArn=${targetGroupArn},Order=1" --region "$Region"
  # fi

  RETURN_VALUE=$targetGroupArn 
}
export -f common_get_loadbalancer_target

common_add_route53_record()
{
  local loadBalancer=$1
  local recordRoute=$2
  local hostedZone=$3

  echo ">> common_add_route53_record loadBalancer $loadBalancer recordRoute $recordRoute hostedZone $hostedZone"

  local hostedZoneId
  hostedZoneId=$(aws route53 list-hosted-zones | jq -r --arg NAME "$hostedZone." '.HostedZones[] | select(.Name==$NAME) | .Id')
  echo ">> hostedZoneId $hostedZoneId"

  local dnsName
  local loadBalancerArn
  local canonicalHostedZoneId
  read -r dnsName loadBalancerArn canonicalHostedZoneId <<< "$(aws elbv2 describe-load-balancers --region "$Region" | jq -r --arg name "$loadBalancer" '.LoadBalancers[] | select(.LoadBalancerName==$name) | .DNSName + " " + .LoadBalancerArn + " " + .CanonicalHostedZoneId')"
  echo ">> loadBalancerArn $loadBalancerArn"
  echo ">> dnsName $dnsName"
  echo ">> canonicalHostedZoneId $canonicalHostedZoneId"

  local dnsNameDual
  dnsNameDual="dualstack.$dnsName"
  
  sed -e "s;%NAME%;$recordRoute.$hostedZone;g" \
      -e "s;%HOSTED_ZONE_ID%;$canonicalHostedZoneId;g" \
      -e "s;%DNS_NAME%;$dnsNameDual;g" \
      deploy/record.json > record.json 

  cat record.json

  local getLoadBalanceRecord    
  getLoadBalanceRecord=$(aws route53 list-resource-record-sets --hosted-zone-id "$hostedZoneId" | jq -r --arg NAME "$recordRoute.$hostedZone." '.ResourceRecordSets[] | select(.Name==$NAME) | .Name')
  if [[ -z $getLoadBalanceRecord ]]
  then
    aws route53 change-resource-record-sets --hosted-zone-id "$hostedZoneId" --change-batch file://${WORKSPACE}/record.json
  fi
}

export -f common_add_route53_record

common_register_autoscaling()
{
  local cluster=$1
  local serviceName=$2
  local minCount=$3
  local maxCount=$4

  echo ">> common_register_autoscaling"

  local result
  result=$(aws application-autoscaling register-scalable-target \
    --service-namespace ecs \
    --scalable-dimension ecs:service:DesiredCount \
    --resource-id service/"$cluster/$serviceName" \
    --min-capacity "$minCount" \
    --max-capacity "$maxCount" \
    --region "$Region")

    RETURN_VALUE=$result

}
export -f common_register_autoscaling

common_create_autoscaling_target()
{
  local cluster=$1
  local serviceName=$2
  local policyName=$3
  local configuration=$4

  echo ">> common_create_autoscaling $policyName"

  local result
  result=$(aws application-autoscaling put-scaling-policy \
    --region "$Region" \
    --service-namespace ecs \
    --scalable-dimension ecs:service:DesiredCount \
    --resource-id service/"$cluster/$serviceName" \
    --policy-name "$policyName" \
    --policy-type TargetTrackingScaling \
    --target-tracking-scaling-policy-configuration ${configuration} | jq -r .PolicyARN)

  RETURN_VALUE=$result
}
export -f common_create_autoscaling_target

common_create_autoscaling_step()
{
  local cluster=$1
  local serviceName=$2
  local policyName=$3
  local configuration=$4

  echo ">> common_create_autoscaling $policyName"

  local result
  result=$(aws application-autoscaling put-scaling-policy \
    --region "$Region" \
    --service-namespace ecs \
    --scalable-dimension ecs:service:DesiredCount \
    --resource-id service/"$cluster/$serviceName" \
    --policy-name "$policyName" \
    --policy-type StepScaling \
    --step-scaling-policy-configuration "$configuration" | jq -r .PolicyARN)

  RETURN_VALUE=$result
}
export -f common_create_autoscaling_step

common_create_alarm_alb()
{
  local alarmName=$1
  local alarmEcsName=$2
  local targetgroupName=$3
  local okAction=$4
  local alarmAction=$5
  local alarmActionHigh=$6
  local alarmActionLow=$7

  local target
  target=$(aws elbv2 describe-target-groups --region "$Region" --names "$targetgroupName")
  
  local targetArn
  targetArn=$(echo "$target" | jq -r '.TargetGroups[].TargetGroupArn')
  targetGroup=${targetArn:52}
  echo ">> common_create_alarm_alb targetGroup $targetGroup"
  
  local loadbalancerArn
  loadbalancerArn=$(echo "$target" | jq -r '.TargetGroups[].LoadBalancerArns[0]')
  loadbalancer=${loadbalancerArn:65}
  echo ">> common_create_alarm_alb loadbalancer $loadbalancer"
  
  echo ">> common_create_alarm_alb_5xx"
  
  aws cloudwatch put-metric-alarm \
  --alarm-name "$alarmName"-HIGH-HTTP-5XXs \
	--region "$Region" \
	--ok-actions "$okAction" \
	--alarm-actions "$alarmAction" \
	--metric-name HTTPCode_Target_5XX_Count \
	--namespace "AWS/ApplicationELB" \
	--statistic Sum \
	--dimensions "[{\"Name\":\"TargetGroup\",\"Value\":\"$targetGroup\"},{\"Name\":\"LoadBalancer\",\"Value\":\"$loadbalancer\"}]" \
	--period 60 \
	--evaluation-periods 5 \
	--datapoints-to-alarm 1 \
	--threshold 20 \
	--comparison-operator GreaterThanOrEqualToThreshold \
  --treat-missing-data notBreaching

  echo ">> common_create_alarm_alb_high_request_count"

  aws cloudwatch put-metric-alarm \
  --alarm-name "$alarmName"-HIGH-REQUEST-COUNT-PER-TARGET \
	--region "$Region" \
  --ok-actions "$okAction" \
	--alarm-actions "$alarmActionHigh" "$okAction" \
	--metric-name "RequestCountPerTarget" \
	--namespace "AWS/ApplicationELB" \
	--statistic Sum \
	--dimensions "[{\"Name\":\"TargetGroup\",\"Value\":\"$targetGroup\"},{\"Name\":\"LoadBalancer\",\"Value\":\"$loadbalancer\"}]" \
	--period 60 \
	--evaluation-periods 3 \
	--datapoints-to-alarm 3 \
	--threshold 200 \
	--comparison-operator GreaterThanThreshold \
  --treat-missing-data breaching

  echo ">> common_create_alarm_alb_low_request_count"

  aws cloudwatch put-metric-alarm \
  --alarm-name "$alarmName"-LOW-REQUEST-COUNT-PER-TARGET \
	--region "$Region" \
  --alarm-actions "$alarmActionLow" \
	--metric-name "RequestCountPerTarget" \
	--namespace "AWS/ApplicationELB" \
	--statistic Sum \
	--dimensions "[{\"Name\":\"TargetGroup\",\"Value\":\"$targetGroup\"},{\"Name\":\"LoadBalancer\",\"Value\":\"$loadbalancer\"}]" \
	--period 60 \
	--evaluation-periods 5 \
	--datapoints-to-alarm 5 \
	--threshold 100 \
	--comparison-operator LessThanOrEqualToThreshold \
  --treat-missing-data missing

  echo ">> common_create_alarm_unhealthy_host"
  
  aws cloudwatch put-metric-alarm \
  --alarm-name "$alarmEcsName"-UNHEALTHY-HOSTS \
	--region "$Region" \
	--ok-actions "$okAction" \
	--alarm-actions "$alarmAction" \
	--metric-name UnHealthyHostCount \
	--namespace "AWS/ApplicationELB" \
	--statistic Average \
	--dimensions "[{\"Name\":\"TargetGroup\",\"Value\":\"$targetGroup\"},{\"Name\":\"LoadBalancer\",\"Value\":\"$loadbalancer\"}]" \
	--period 300 \
	--evaluation-periods 1 \
	--datapoints-to-alarm 1 \
	--threshold 1 \
	--comparison-operator GreaterThanOrEqualToThreshold \
  --treat-missing-data missing

  echo ">> common_create_alarm_high_target_response_time"
  
  aws cloudwatch put-metric-alarm \
  --alarm-name "$alarmName"-HIGH-TARGET-RESPONSE-TIME \
	--region "$Region" \
	--ok-actions "$okAction" \
	--alarm-actions "$alarmAction" \
	--metric-name TargetResponseTime \
	--namespace "AWS/ApplicationELB" \
	--statistic Average \
	--dimensions "[{\"Name\":\"TargetGroup\",\"Value\":\"$targetGroup\"},{\"Name\":\"LoadBalancer\",\"Value\":\"$loadbalancer\"}]" \
	--period 60 \
	--evaluation-periods 5 \
	--datapoints-to-alarm 5 \
	--threshold 3 \
	--comparison-operator GreaterThanThreshold \
  --treat-missing-data notBreaching
}
export -f common_create_alarm_alb

common_create_alarm_cpu()
{
  local alarmEcsName=$1
  local cluster=$2
  local serviceName=$3
  local okAction=$4
  local alarmAction=$5
  local cpuAlarmAction=$6

  echo ">> common_create_alarm_high_cpu"
  
  aws cloudwatch put-metric-alarm \
  --alarm-name "$alarmEcsName"-HIGH-CPU \
	--region "$Region" \
	--ok-actions "$okAction" \
	--alarm-actions "$cpuAlarmAction" \
	--metric-name CPUUtilization \
	--namespace "AWS/ECS" \
	--statistic Average \
	--dimensions "[{\"Name\":\"ServiceName\",\"Value\":\"$serviceName\"},{\"Name\":\"ClusterName\",\"Value\":\"$cluster\"}]" \
	--period 60 \
	--evaluation-periods 3 \
	--datapoints-to-alarm 3 \
	--threshold 60 \
	--comparison-operator GreaterThanOrEqualToThreshold \
   --treat-missing-data breaching

  echo ">> common_create_alarm_low_cpu"
  
  aws cloudwatch put-metric-alarm \
  --alarm-name "$alarmEcsName"-LOW-CPU \
	--region "$Region" \
	--metric-name CPUUtilization \
	--namespace "AWS/ECS" \
	--statistic Average \
	--dimensions "[{\"Name\":\"ServiceName\",\"Value\":\"$serviceName\"},{\"Name\":\"ClusterName\",\"Value\":\"$cluster\"}]" \
	--period 300 \
	--evaluation-periods 3 \
	--datapoints-to-alarm 3 \
	--threshold 30 \
	--comparison-operator LessThanOrEqualToThreshold \
  --treat-missing-data missing
}
export -f common_create_alarm_cpu

common_update_ecs_tags() {
  local service_arn="$1"
  local tags="$2"

  echo ">> common_update_ecs_tags"
  echo ">> enableServiceTagsUpdate: $ENABLE_ECS_TAGS_UPDATE"

  if [[ $ENABLE_ECS_TAGS_UPDATE == 1 ]]; then
    echo ">> service_arn: $service_arn"
    echo ">> tags: $tags"
    echo ">> Updating ECS tags..."
    aws ecs tag-resource --region "$Region" --resource-arn "$service_arn" --tags "$tags" 

    if [ $? -eq 0 ]; then
      echo ">> ECS tags updated successfully."
      return 0
    else
     echo ">> Failed to update ECS tags."
      return 1
    fi
  fi
}
export -f common_update_ecs_tags