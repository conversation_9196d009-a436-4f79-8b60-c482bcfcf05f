import { beforeEach, describe, expect, it, vi } from 'vitest'

import { nextTick } from 'vue'
import { useTable } from '@/hooks/useTable'

// Mock message utility
vi.mock('@/utils/message', () => ({
  default: {
    error: vi.fn()
  }
}))

describe('useTable', () => {
  const mockData = {
    data: {
      records: [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' }
      ],
      total: 2
    }
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should initialize with default values', () => {
    const table = useTable()
    
    expect(table.searchQuery.value).toEqual({})
    expect(table.isLoading.value).toBe(false)
    expect(table.tableData.value).toBeNull()
    expect(table.total.value).toBe(0)
    expect(table.itemsPerPage.value).toBe(10)
    expect(table.page.value).toBe(1)
    expect(table.items.value).toEqual([])
  })

  it('should initialize with custom default query', () => {
    const defaultQuery = {
      status: 1,
      type: 'test'
    }
    
    const table = useTable({
      createDefaultQuery: () => defaultQuery
    })
    
    expect(table.searchQuery.value).toEqual(defaultQuery)
  })

  it('should load data successfully', async () => {
    const fetchData = vi.fn().mockResolvedValue(mockData)
    
    const table = useTable({
      fetchData
    })
    
    await table.loadData()
    await nextTick()
    
    expect(table.isLoading.value).toBe(false)
    expect(table.tableData.value).toEqual(mockData.data.records)
    expect(table.total.value).toBe(mockData.data.total)
    expect(table.items.value).toEqual([
      { id: 1, name: 'Item 1', no: 1 },
      { id: 2, name: 'Item 2', no: 2 }
    ])
  })

  it('should handle data loading error', async () => {
    const error = new Error('Loading failed')
    const fetchData = vi.fn().mockRejectedValue(error)
    const message = (await import('@/utils/message')).default
    
    const table = useTable({
      fetchData
    })
    
    await table.loadData()
    await nextTick()
    
    expect(table.isLoading.value).toBe(false)
    expect(table.tableData.value).toBeNull()
    expect(message.error).toHaveBeenCalledWith('Failed to load data')
  })

  it('should handle search correctly', async () => {
    const fetchData = vi.fn().mockResolvedValue(mockData)
    
    const table = useTable({
      fetchData,
      createDefaultQuery: () => ({
        keyword: ''
      })
    })
    
    table.searchQuery.value.keyword = 'test'
    await table.handleSearch()
    await nextTick()
    
    expect(table.page.value).toBe(1)
    expect(fetchData).toHaveBeenCalledWith(expect.objectContaining({
      keyword: 'test',
      page: 1
    }))
  })

  it('should handle pagination changes correctly', async () => {
    const fetchData = vi.fn().mockResolvedValue(mockData)
    
    const table = useTable({
      fetchData
    })
    
    await table.handlePageChange(2)
    await nextTick()
    
    expect(table.page.value).toBe(2)
    expect(table.searchQuery.value.page).toBe(2)
    expect(fetchData).toHaveBeenCalledWith(expect.objectContaining({
      page: 2
    }))
  })

  it('should handle items per page changes correctly', async () => {
    const fetchData = vi.fn().mockResolvedValue(mockData)
    
    const table = useTable({
      fetchData
    })
    
    await table.handleItemsPerPageChange(20)
    await nextTick()
    
    expect(table.itemsPerPage.value).toBe(20)
    expect(table.searchQuery.value.size).toBe(20)
    expect(table.page.value).toBe(1)
    expect(fetchData).toHaveBeenCalledWith(expect.objectContaining({
      size: 20,
      page: 1
    }))
  })

  it('should handle sort changes correctly', async () => {
    const fetchData = vi.fn().mockResolvedValue(mockData)
    
    const table = useTable({
      fetchData
    })
    
    await table.handleSortChange([{ key: 'name', order: 'asc' }])
    await nextTick()
    
    expect(table.searchQuery.value.sortBy).toBe('name')
    expect(table.searchQuery.value.orderBy).toBe('asc')
    expect(table.page.value).toBe(1)
    expect(fetchData).toHaveBeenCalledWith(expect.objectContaining({
      orderItemList: [{
        column: 'name',
        asc: true
      }]
    }))
  })

  it('should reset search query correctly', () => {
    const defaultQuery = {
      status: 1,
      type: 'test'
    }
    
    const table = useTable({
      createDefaultQuery: () => defaultQuery
    })
    
    table.searchQuery.value = {
      ...table.searchQuery.value,
      keyword: 'test'
    }
    
    table.resetSearchQuery()
    
    expect(table.searchQuery.value).toEqual(defaultQuery)
  })

  it('should transform response correctly', async () => {
    const customResponse = {
      data: {
        items: [
          { id: 1, name: 'Item 1' },
          { id: 2, name: 'Item 2' }
        ],
        count: 2
      }
    }
    
    const fetchData = vi.fn().mockResolvedValue(customResponse)
    const transformResponse = (res) => ({
      records: res.data.items,
      total: res.data.count
    })
    
    const table = useTable({
      fetchData,
      transformResponse
    })
    
    await table.loadData()
    await nextTick()
    
    expect(table.tableData.value).toEqual(customResponse.data.items)
    expect(table.total.value).toBe(customResponse.data.count)
  })

  it('should calculate row numbers correctly', async () => {
    const fetchData = vi.fn().mockResolvedValue(mockData)
    
    const table = useTable({
      fetchData
    })
    
    table.page.value = 2
    table.itemsPerPage.value = 10
    await table.loadData()
    await nextTick()
    
    expect(table.items.value[0].no).toBe(11)
    expect(table.items.value[1].no).toBe(12)
  })
}) 