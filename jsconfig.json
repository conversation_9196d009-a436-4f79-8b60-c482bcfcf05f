{"include": ["./vite.config.*", "./src/**/*", "./src/**/*.vue", "./themeConfig.js", "test/unit/utils/colorConverter.test.js", "test/unit/utils/day.test.js", "test/unit/utils/dialog.test.js", "test/unit/utils/formatters.test.js", "test/unit/utils/helpers.test.js", "test/unit/utils/validators.test.js"], "exclude": ["./dist", "./node_modules"], "compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "preserve", "paths": {"@/*": ["./src/*"], "@themeConfig": ["./themeConfig.js"], "@layouts/*": ["./src/@layouts/*"], "@layouts": ["./src/@layouts"], "@core/*": ["./src/@core/*"], "@core": ["./src/@core"], "@images/*": ["./src/assets/images/*"], "@styles/*": ["./src/assets/styles/*"], "@validators": ["./src/@/utils/validators"], "@db/*": ["./src/plugins/fake-api/handlers/*"], "@api-utils/*": ["./src/plugins/fake-api/utils/*"]}, "types": ["vite/client", "unplugin-vue-router/client", "vite-plugin-vue-layouts/client"], "allowJs": true, "checkJs": false}}