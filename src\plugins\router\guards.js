import { useAuthStore } from "@/stores/useAuthStore";

/**
 * Check if route exists
 * @param {import('vue-router').Router} router - Vue Router instance
 * @param {string} path - Path to check
 * @returns {boolean} Whether route exists
 */
const isValidRoute = (router, path) => {
  try {
    const route = router.resolve(path)
    return route && route.matched.length > 0
  } catch {
    return false
  }
}

export const setupGuards = (router) => {
  const cachedMenuIds = []
  
  // 设置低代码路由守卫
  setupLowCodeGuards(router)
  
  // 👉 router.beforeEach
  // Docs: https://router.vuejs.org/guide/advanced/navigation-guards.html#global-before-guards
  router.beforeEach(async (to, from) => {
    // 低代码路由特殊处理
    if (to.path.startsWith('/low-code/') && !to.path.startsWith('/low-code-engine/')) {
      // 低代码路由不需要权限验证，直接通过
      return true
    }
    
    // Check if route exists
    if (!to.matched.length) {
      return {
        path: '/not-authorized',
        query: { 
          from: to.fullPath,
          message: `Page ${to.fullPath} does not exist` 
        }
      }
    }

    /*
     * If it's a public route, continue navigation. This kind of pages are allowed to visited by login & non-login users. Basically, without any restrictions.
     * Examples of public routes are, 404, under maintenance, etc.
     */
    if (to.meta.public) {
      return true;
    }

    const authStore = useAuthStore();

    // DONE: If no token, try auto login
    if (!authStore.token || !authStore.isValidToken(authStore.token)) {
      const success = await authStore.autoLogin();
      if (!success) {
        // Auto login failed, redirect to unauthorized page
        return "/not-authorized";
      }
    }

    // DONE: If has token but no user data, fetch user profile
    if (authStore.isValidToken(authStore.token) && !authStore.userData) {
      try {
        await authStore.fetchUserProfile();
      } catch (error) {
        // Clear token to avoid repeated attempts to fetch user info
        authStore.setToken(null);
        return "/not-authorized";
      }
    }

    // DONE: If has token but no menus, fetch menus
    if (authStore.isValidToken(authStore.token) && (!authStore.menus || authStore.menus.length === 0)) {
      await authStore.fetchMenus();
    }

    // Get button permissions for the corresponding menu
 

    const currentMenu = getCurrentMenu(authStore.menus, router, to)

    if (currentMenu && !cachedMenuIds.includes(currentMenu.id)) {
      await authStore.fetchAccessByMenuId(currentMenu.id)
      cachedMenuIds.push(currentMenu.id)
    } 

    const defaultRoute = authStore.defaultRoute

    if (to.name === 'index') {
      return { name: defaultRoute }
    }

    return true;
  });

  // Add error handling
  router.onError((error) => {
    if (error.message.includes('No match') || error.message.includes('Missing required param')) {
      router.push({
        path: '/not-authorized',
        query: {
          from: router.currentRoute.value.fullPath,
          message: 'Page does not exist or route configuration error'
        }
      })
    }
  })
};

/**
 * 低代码路由守卫
 * 处理 /low-code/* 路径的动态路由
 */
function setupLowCodeGuards(router) {
  router.beforeEach(async (to, from, next) => {
    // 检查是否是低代码路由
    if (to.path.startsWith('/low-code/') && !to.path.startsWith('/low-code-engine/')) {
      console.log('低代码路由拦截:', to.path)
      
      // 解析路径参数
      const pathParts = to.path.split('/').filter(Boolean)
      if (pathParts.length >= 2 && pathParts[0] === 'low-code') {
        const scenario = pathParts[1]
        const pageType = pathParts[2] || 'list'
        
        // 验证场景和页面类型
        if (!scenario) {
          next({ name: 'low-code-engine', query: { error: 'invalid-scenario' } })
          return
        }
        
        // 设置路由元信息
        to.meta.scenario = scenario
        to.meta.pageType = pageType
        to.meta.title = `${scenario} - ${pageType}`
        
        console.log('低代码路由参数:', { scenario, pageType })
        next()
        return
      }
    }
    
    next()
  })
}

const getCurrentMenu = (menus, router, to) => {
  if (!menus || menus.length === 0) {
    return null
  }

  let foundMenu = null
  
  for (const menu of menus) {
    // Check if route is valid
    const menuPath = resolvePath(menu.link)
    if (!isValidRoute(router, menuPath)) {
      continue
    }

    // Check current menu
    if (menuPath === to.path) {
      foundMenu = menu
      break
    }
    
    // Recursively check submenus
    if (menu.children && menu.children.length > 0) {
      foundMenu = getCurrentMenu(menu.children, router, to)
      if (foundMenu) break
    }
  }
  
  return foundMenu
};

const resolvePath = (path) => {
  if (!path || path === 'NA' || path === 'N/A') return ''
  if (!path.includes('-')) {
    return `${path.replaceAll("-", "/")}`
  }
  return path.startsWith('/') ? path : `/${path}`
};
