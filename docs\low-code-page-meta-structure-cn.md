# 低代码页面元数据结构分析

本文档旨在分析低代码引擎中用于定义动态页面的 JSON 结构。我们将以 `user-list-page` (`src/pages/low-code/engine/mockPageMeta.json`) 为例，详细解析其各个部分的构成。

## 1. 页面元数据 (Page Metadata)

这部分定义了页面的基础属性。

-   `id`: 页面的唯一标识符 (`user-list-page`)。
-   `name`: 页面名称，通常用于菜单或标题 (`用户管理`)。
-   `path`: 页面的 URL 路径 (`/user/list`)。
-   `title`: 显示在浏览器标签页中的标题 (`用户列表`)。
-   `layout`: 页面所使用的基础布局模板 (`default`)。

**示例:**
```json
{
  "id": "user-list-page",
  "name": "用户管理",
  "path": "/user/list",
  "title": "用户列表",
  "layout": "default"
}
```

## 2. 页面生命周期 (Page Lifecycle)

`lifecycle` 对象指定了在页面生命周期的不同阶段需要执行的动作。

-   `onMounted`: 一个动作数组，定义了当页面组件挂载到 DOM 后需要执行的操作，通常用于初始化数据。

**示例:**
```json
"lifecycle": {
  "onMounted": [
    { "action": "initData" },
    { "action": "handleSearch" }
  ]
}
```
在此示例中，`initData` 和 `handleSearch` 这两个方法会在页面加载时被调用。

## 3. 页面数据 (`data`)

`data` 对象持有页面的状态，类似于 Vue 组件中的 `data` 属性。此处定义的所有属性都是响应式的，可以与 UI 组件进行绑定。

关键属性示例:
-   `searchQuery`: 存储搜索过滤器值的对象。
-   `isLoading`: 布尔值，用于指示数据是否正在加载（例如，用于显示加载动画）。
-   `usersData`: 存储从 API 获取的用户列表。
-   `headers`: 定义数据表格的列。
-   `paginationOptions`, `itemsPerPage`, `page`: 用于分页的状态。

**示例:**
```json
"data": {
  "searchQuery": {
    "email": "",
    "roleIdList": [],
    "userGroupIdList": [],
    "status": null,
    // ...
  },
  "isLoading": false,
  "accounts": [],
  "totalUsers": 0,
  "headers": [
    { "title": "Email", "key": "email" },
    { "title": "Name", "key": "name" },
    // ...
  ]
}
```

## 4. UI 组件 (`components`)

这是一个对象数组，用声明式的方式定义了页面的 UI 结构。每个对象代表一个组件，包含以下属性：

-   `type`: 要渲染的组件名称 (例如, `VCard`, `VDataTableServer`, `AppTextField`)。
-   `props`: 传递给组件的属性 (props)。值可以是静态的，也可以使用 `{{...}}` 语法绑定到 `data` 对象中的数据。
-   `model`: 将组件的值与 `data` 对象中的属性进行双向绑定 (例如, `searchQuery.email`)。
-   `events`: 为组件定义事件监听器。其值是一个包含待执行动作的数组。
-   `children`: 子组件对象的数组。
-   `slots`: 为组件定义具名插槽。
-   `v-if`: 用于条件渲染，绑定到一个数据属性。
-   `permission`: 用于基于角色的访问控制。
-   `text`: 用于显示文本内容。

**示例 (文本输入框):**
```json
{
  "type": "AppTextField",
  "model": "searchQuery.email",
  "props": {
    "placeholder": "Search Email",
    "clearable": true
  },
  "events": {
    "update:modelValue": [{ "action": "handleSearch" }]
  }
}
```

**示例 (带自定义插槽的数据表格):**
```json
{
  "type": "VDataTableServer",
  "model": "selectedIds",
  "props": {
    "items": "{{accounts}}",
    // ...
  },
  "events": {
    "update:sort-by": [{ "action": "handleSortChange", "params": ["options"] }]
  },
  "slots": {
    "item.email": {
      "type": "AppText",
      "props": { "text": "{{item.accountEmail}}", "auth": ["View", "UserList"] },
      "events": { "click": [{ "action": "viewUser", "params": ["item"] }] }
    }
  }
}
```

## 5. API 定义 (`apis`)

该数组定义了页面可以调用的所有后端 API 接口。每个 API 对象包含：

-   `id`: 用于在方法中引用此 API 调用的唯一 ID。
-   `type`: API 的类型 (例如, `http`)。
-   `method`: HTTP 方法 (`GET`, `POST`, `PUT`)。
-   `url`: API 的端点 URL。
-   `urlParams`, `bodyParams`: 指定哪些数据属性应作为 URL 参数或请求体发送。

**示例:**
```json
{
  "id": "user-list",
  "type": "http",
  "method": "POST",
  "url": "/api/admin-api/v1/account/page",
}
```

## 6. 方法 (`methods`)

此对象包含页面的所有业务逻辑和功能实现。这些方法可以由 `lifecycle` 生命周期事件或组件的 `events` 触发。

-   每个键都是方法名。
-   `type`: 方法的类型，决定了其行为。
    -   `custom`: 执行一段自定义 JavaScript 脚本。脚本中的 `this` 指向页面上下文，可以访问 `data`, `apiManager` 等。
    -   `navigate`: 导航到另一个路由。
    -   `confirm`: 在执行其他操作前显示一个确认对话框。
    -   `callApi`: 以声明式的方式调用在 `apis` 部分定义的 API。
    -   `message`: 显示一个通知或提示消息。

**示例 (自定义脚本):**
```json
"handleSearch": {
  "type": "custom",
  "script": "this.setData('page', 1); /* ... 更多逻辑 ... */ this.apiManager.callApi('user-list', ...).then(...);"
}
```
该脚本调用了 `user-list` API，并用返回的结果更新页面的数据。

**示例 (确认对话框):**
```json
"batchDisable": {
  "type": "confirm",
  "title": "Confirm Disable Users",
  "text": "Are you sure you want to disable the selected user(s)?",
  "onConfirm": [
    {
      "action": "callApi",
      "apiId": "user-batch-status",
      "params": { "status": 0, "ids": "{{selectedRows.map(item => ({id: item.accountId}))}}" },
      "onSuccess": [
        { "action": "message", "messageType": "success", "text": "Users disabled successfully" },
        { "action": "handleSearch" }
      ]
    }
  ]
}
```
此方法会显示一个确认对话框。如果用户确认，它将调用 `user-batch-status` API，然后显示成功消息并刷新列表。

## 7. 渲染器实现细节

本节将解释低代码渲染器的内部工作原理，它负责将 JSON 元数据动态转换为一个实时的 Vue 组件树。核心逻辑位于 `src/business/low-code-engine/renderer/` 目录中。

### 核心文件

-   **`index.js`**: 渲染器的入口点。它负责初始化页面上下文，处理生命周期钩子 (`onMounted`)，并通过遍历 `meta.components` 中的根组件来启动渲染过程。
-   **`renderComponent.js`**: 渲染器的核心。该文件负责将单个组件的 JSON 元数据转换为 Vue 的虚拟节点 (VNode)。
-   **`context.js`**: 创建并管理整个页面的共享执行上下文，包括数据、方法和工具函数。
-   **`utils.js`**: 包含辅助函数，例如用于解析数据绑定表达式 (`{{some.data}}`) 的 `resolveExpression` 和用于动态解析组件实现的 `getComponentByName`。

### 渲染工作流

渲染过程可以概括为以下几个步骤：

1.  **创建上下文 (`context.js`)**:
    -   当 `index.js` 中的 `render()` 函数被调用时，它首先会创建一个统一的上下文。
    -   这个上下文对象聚合了页面的响应式 `data`，将 `methods` 从 JSON 定义转换为可执行函数（对自定义脚本使用 `new Function()`），并捆绑了 `apiManager` 和 `dataManager` 等实用工具。
    -   最关键的是，它返回一个 `Proxy` 对象。这使得在任何方法脚本中都可以通过 `this` 透明地访问数据和方法（例如，`this.isLoading` 会访问 `data.isLoading`，而 `this.handleSearch()` 会调用相应的方法）。

2.  **渲染组件 (`renderComponent.js`)**:
    -   `index.js` 遍历 `meta.components` 中的顶层组件，并为每个组件调用 `renderComponent`。
    -   对于每个组件的元数据，`renderComponent` 执行以下操作：
        -   **处理 `v-if`**: 首先评估 `v-if` 表达式。如果结果为 false，则返回 `null`，停止渲染该组件及其子组件。
        -   **解析 Props**: 遍历 `props` 对象。对于任何作为表达式的值（例如 `{{some.data}}`），它会使用 `resolveExpression` 从上下文中获取实际值。
        -   **处理 `v-model`**: 通过从上下文中获取值并创建一个 `onUpdate:modelValue` 事件处理器来更新数据，从而建立双向数据绑定。
        -   **处理事件**: 将事件元数据（如 `click`）映射到 Vue 的 `on[EventName]` 监听器。当事件触发时，页面 `methods` 中相应的动作将被执行。
        -   **渲染子节点和插槽**:
            -   对于 `children`，它会为每个子节点递归调用 `renderComponent`。
            -   对于具名 `slots`，它会创建一个函数，该函数在执行时将渲染插槽的内容。此函数接收 `slotProps`（从子组件传递上来的数据）并将其合并到一个新的上下文中，使其在插槽内部可用。
        -   **创建 VNode**: 最后，它使用 Vue 的 `h()` 函数来创建 VNode，并传入解析后的组件、props、事件和子节点/插槽。

3.  **生命周期钩子 (`index.js`)**:
    -   在初始设置之后、最终渲染之前，`onMounted` 生命周期钩子会被处理。它会在组件树挂载到 DOM 后执行定义好的动作（例如获取初始数据）。

整个过程是声明式的。JSON 元数据描述了"渲染什么"，而渲染器则负责"如何渲染"，从而从一个静态的数据结构创建出一个完全响应式和交互式的 Vue 组件。 