#!/usr/bin/env bash
set -eu

declare -x DEPLOY_ENV
declare -x SERVICE_ENV
declare -x CLUSTER
declare -x REGION
declare -xi SERVICE_COUNT
declare -xi SERVICE_MAX_COUNT
declare -x IMAGE_ENV
declare -x VPCID
declare -x LOADBALANCER_LAN
declare -x LOADBALANCER_WAN
declare -x HOSTED_ZONE

case $ENV in

  zgd)
  DEPLOY_ENV=zgd
  SERVICE_ENV=dev
  SERVICE_COUNT=1
  SERVICE_MAX_COUNT=1
  CLUSTER=zgd-ecs-cluster-fargate
  REGION=ap-southeast-1
  IMAGE_ENV=develop
  VPCID=vpc-06a2bbb02f091383e
  LOADBALANCER_LAN=zgd-alb-lan-ecs
  LOADBALANCER_WAN=zgd-alb-wan-ecs
  HOSTED_ZONE='zgold-dev.razer.com'
  ;;

  zgq)
  DEPLOY_ENV=zgq
  SERVICE_ENV=qa
  SERVICE_COUNT=1
  SERVICE_MAX_COUNT=1
  CLUSTER=ZGQ-ECS-CLUSTER-FARGATE
  REGION=us-east-1
  IMAGE_ENV=release
  VPCID=vpc-0b859e1d3871e20d4
  LOADBALANCER_LAN=ZGQ-ALB-LAN-API-2
  LOADBALANCER_WAN=ZGQ-ALB-WAN-API
  HOSTED_ZONE='zgold-qa.razer.com'
  ;;

  zgs)
  DEPLOY_ENV=zgs
  SERVICE_ENV=sb
  SERVICE_COUNT=1
  SERVICE_MAX_COUNT=1
  CLUSTER=ZGS-ECS-CLUSTER-FARGATE
  REGION=us-east-1
  IMAGE_ENV=sb
  VPCID=vpc-06c012a454ea65eed
  LOADBALANCER_LAN=ZGS-ALB-LAN-API-2
  LOADBALANCER_WAN=ZGS-ALB-WAN-API
  HOSTED_ZONE='gold-sandbox.razer.com'
  ;;

  zgp)
  DEPLOY_ENV=zgp
  SERVICE_ENV=prod
  SERVICE_COUNT=2
  SERVICE_MAX_COUNT=4
  CLUSTER=ZGP-ECS-CLUSTER-FARGATE
  REGION=us-east-1
  IMAGE_ENV=master
  VPCID=vpc-0e9ea4e4adc63f021
  LOADBALANCER_LAN=ZGP-ALB-LAN-API-2
  LOADBALANCER_WAN=ZGP-ALB-WAN-API
  HOSTED_ZONE='gold.razer.com'
  ;;
esac

declare -xu FAMILY=${DEPLOY_ENV}-ECS-FGTD-${SERVICE_NAME}
declare -xu ROLE="${DEPLOY_ENV}-ECS-FARGATE-ROLE"
declare -xu CONTAINER_NAME=${DEPLOY_ENV}-FG-${SERVICE_NAME}
declare -x REPOSITORY_NAME=$SERVICE_NAME
declare -xu DEPLOY_SERVICE_NAME="${DEPLOY_ENV}"-$SERVICE_NAME
declare -x HEALTH_CHECK_PATH="/healthcheck"
declare -xu TARGET_GROUP_NAME="$DEPLOY_ENV"-TG-"$SERVICE_NAME"
declare -x PLATFORM_VERSION=LATEST
declare -x MEMORY=512
declare -x CPU=256
declare -x SLOW_START=0
declare -x LAUNCH_TYPE=FARGATE
declare -x TAGS="key=Project,value=Gold-Pay"
declare -xu LOGNAME="${DEPLOY_ENV}-LG-${SERVICE_NAME}"
declare -x LOGSTREAM=ecs
declare -x DUAL_LOADBALANCER=0
declare -x CONDITIONER_LISTEN_RULE=''
declare -x CONDITIONER_LISTEN_WAN_RULE=''
declare -xu ALARM_NAME="$DEPLOY_ENV"_ALARM_TG-ECS-LAN-"$SERVICE_NAME"
declare -xu ALARM_ECS_NAME="$DEPLOY_ENV"_ALARM_ECS-LAN-"$SERVICE_NAME"
declare -x ALARM_ALERT_ACTION_QA=arn:aws:sns:us-east-1:903306222264:ZG-ALERTS-GOLD-SRE
declare -x ALARM_ALERT_ACTION=arn:aws:sns:us-east-1:903306222264:ALERT-EC2-ALB-RDS
declare -x LAMBDA_LAYER=''
declare -x LAMBDA_MEMORY=128
declare -xu C_ENV="$ENV"
declare -x TAGS="[{\"key\":\"Project\",\"value\":\"Gold-Pay\"},{\"key\":\"Environment\",\"value\":\"$C_ENV\"},{\"key\":\"Name\",\"value\":\"$DEPLOY_SERVICE_NAME\"}]"
declare -x TAGS_LAMBDA="Project=Gold-Shared,Environment=$C_ENV,Name=${C_ENV}-LAMBDA-${SERVICE_NAME}"

# Script Configs
declare -x ENABLE_ECS_TAGS_UPDATE=0






