import IndexedDBManager from "@/utils/indexedDB";
import { generatePagesFromBackendMeta } from '../editor/pageGenerator.js'
import { useDB } from './useDB.js'

/**
 * 页面生成hook
 * 现在使用 PageGenerator 来生成结构化的 JSON 配置，而不是原始 HTML 字符串。
 */

// 创建一个 IndexedDBManager 实例作为单例
const dbPageContent = useDB().pageContent;

export function usePageGeneration() {

  /**
   * 根据传入的页面类型、基础页面配置和初始数据，生成最终的页面配置。
   * @param {string} pageType - 页面类型: 'list' | 'create' | 'edit' | 'detail'
   * @param {Object} basePageConfig - 由 PageGenerator 生成的基础页面配置 (例如 listPage, createPage等)
   * @param {Object} [initialData={}] - 初始数据，用于填充页面数据模型 (如 formData, item, searchQuery)
   * @returns {Object} 最终的页面配置JSON对象
   */
  function generatePageContent(pageType, basePageConfig, initialData = {}) {
    if (!basePageConfig) {
      console.warn('generatePageContent received null or undefined basePageConfig.');
      return {};
    }

    // Create a deep copy to avoid modifying the original basePageConfig
    const finalPageConfig = JSON.parse(JSON.stringify(basePageConfig));

    if (finalPageConfig.data) {
      switch (pageType) {
        case 'list':
          // 🔥 重大修复：保留所有原有data字段，只更新initialData指定的字段
          finalPageConfig.data = {
            ...finalPageConfig.data,  // 保留所有原有字段（headers、listData等）
            searchQuery: {
              ...finalPageConfig.data.searchQuery,
              ...initialData
            }
          };
          console.log('generatePageContent: 列表页data处理完成:', finalPageConfig.data)
          console.log('generatePageContent: headers保留情况:', finalPageConfig.data.headers)
          break;
        case 'create':
          // For create form, initialData might be default values
          finalPageConfig.data.formData = {
            ...finalPageConfig.data.formData,
            ...initialData
          };
          break;
        case 'edit':
          // For edit form, initialData is the fetched item data
          finalPageConfig.data.formData = {
            ...finalPageConfig.data.formData,
            ...initialData
          };
          // Also set entityId if available in initialData for edit mode
          if (initialData.id) {
            finalPageConfig.data.entityId = initialData.id;
          }
          break;
        case 'detail':
          // For detail page, initialData is the fetched item data
          finalPageConfig.data.item = {
            ...finalPageConfig.data.item,
            ...initialData
          };
          // Also set entityId if available in initialData for detail mode
          if (initialData.id) {
            finalPageConfig.data.entityId = initialData.id;
          }
          break;
        default:
          // For other cases, just merge directly into data
          finalPageConfig.data = {
            ...finalPageConfig.data,
            ...initialData
          };
          break;
      }
    } else {
      // If no 'data' property initially, create it with initialData
      finalPageConfig.data = initialData;
    }

    return finalPageConfig;
  }


  function generatePageConfig(pageType, basePageConfig, initialData = {}) {
    return generatePageContent(pageType, basePageConfig, initialData);
  }

 async function savePageContent(pageType, basePageConfig, initialData = {}) {
    if (!basePageConfig) {
      console.warn('savePageContent received null or undefined basePageConfig.');
      return {};
    }
    const finalPageConfig = JSON.parse(JSON.stringify(basePageConfig));

    if (finalPageConfig.data) {
      switch (pageType) {
        case 'create':
          // For create form, initialData might be default values
          finalPageConfig.data.formData = {
            ...finalPageConfig.data.formData,
            ...initialData
          };
          break;
        case 'edit':
          // For edit form, initialData is the fetched item data
          finalPageConfig.data.formData = {
            ...finalPageConfig.data.formData,
            ...initialData
          };
          // Also set entityId if available in initialData for edit mode
          if (initialData.id) {
            finalPageConfig.data.entityId = initialData.id;
          }
          break;
        default:
          // For other cases, just merge directly into data
          finalPageConfig.data = {
            ...finalPageConfig.data,
            ...initialData
          };
          break;
      }
    }

    // 使用indexedDB保存到本地
    let pageContentRecord = await getPageContent(pageType);
    if (pageContentRecord) {
      pageContentRecord.pageContent = finalPageConfig;
      pageContentRecord = {
        pageType,
        pageContent: finalPageConfig
      }
      dbPageContent.put(pageType, pageContentRecord);
    } else {
      dbPageContent.add({
        pageType,
        pageContent: finalPageConfig
      });
    }


    return finalPageConfig;
  }

  function getPageContent(pageType) {
    return dbPageContent.get(pageType);
  }

  return {
    generatePagesFromBackendMeta,
    generatePageContent,
    savePageContent,
    getPageContent,
    generatePageConfig,
  }
} 