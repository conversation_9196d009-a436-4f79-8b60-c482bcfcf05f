import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import vuetify from 'vite-plugin-vuetify'
import { fileURLToPath } from 'node:url'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VueRouterAutoImports, getPascalCaseRouteName } from 'unplugin-vue-router'
import VueRouter from 'unplugin-vue-router/vite'

export default defineConfig({
  plugins: [
    VueRouter({
      getRouteName: routeNode => {
        // Convert pascal case to kebab case
        return getPascalCaseRouteName(routeNode)
          .replace(/([a-z\d])([A-Z])/g, '$1-$2')
          .toLowerCase()
      },
      dts: false,
    }),
    vue(),
    vuetify(),
    Components({
      dirs: ['src/components'],
      dts: false,  // Disable auto-generated types component.d.ts file
      resolvers: [
        componentName => {
          // Auto import `VueApexCharts`
          if (componentName === 'VueApexCharts')
            return { name: 'default', from: 'vue3-apexcharts', as: 'VueApexCharts' }
        },
      ],
    }),
    AutoImport({
      imports: ['vue', VueRouterAutoImports, '@vueuse/core', '@vueuse/math', 'vue-i18n', 'pinia'],
      vueTemplate: true,
      dirs: [
        './src/hooks/',
      ],
      // ℹ️ Disabled to avoid confusion & accidental usage
      ignore: ['useCookies', 'useStorage'],
      eslintrc: {
        enabled: true,
        filepath: './.eslintrc-auto-import.json',
      },
      dts: false
    }),
  ],
  test: {
    // 全局配置
    globals: true,
    
    // 包含所有测试文件
    include: ['test/**/*.{test,spec}.{js,ts}'],
    
    // 环境配置 - 根据测试类型使用不同环境
    environment: process.env.TEST_TYPE === 'integration' ? 'node' : 'jsdom',
    
    // 设置文件
    setupFiles: process.env.TEST_TYPE === 'integration' 
      ? ['./test/integration/setup/index.js']
      : ['./test/unit/setup/index.js'],
    
    // 依赖配置
    deps: {
      inline: [/vuetify/]
    },
    
    // 增加超时时间，因为浏览器操作可能需要更长时间
    testTimeout: 30000,
    
    // 钩子函数超时时间
    hookTimeout: 60000,
    
    // 别名配置
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@test': fileURLToPath(new URL('./test', import.meta.url)),
      '@themeConfig': fileURLToPath(new URL('./themeConfig.js', import.meta.url)),
        '@core': fileURLToPath(new URL('./src/@core', import.meta.url)),
        '@layouts': fileURLToPath(new URL('./src/layouts/common', import.meta.url)),
        '@images': fileURLToPath(new URL('./src/assets/images/', import.meta.url)),
        '@styles': fileURLToPath(new URL('./src/assets/styles/', import.meta.url)),
        '@configured-variables': fileURLToPath(new URL('./src/assets/styles/variables/_template.scss', import.meta.url)),
        '@db': fileURLToPath(new URL('./src/plugins/fake-api/handlers/', import.meta.url)),
        '@api-utils': fileURLToPath(new URL('./src/plugins/fake-api/utils/', import.meta.url))
    }
  }
}) 