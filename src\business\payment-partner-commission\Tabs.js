import { computed, defineComponent, h, provide, ref, watch } from 'vue'

import TabItem from './TabItem.vue'
import { VIcon } from 'vuetify/components'

export default defineComponent({
  name: 'Tabs',
  components: {
    TabItem,
    VIcon
  },
  props: {
    // Currently active tab, can be index or name
    modelValue: { // Vue 3 uses modelValue instead of value
      type: [String, Number], 
      default: 0
    },
  },
  emits: ['update:modelValue', 'tab-change'], // Vue 3 v-model convention
  setup(props, { slots, emit }) {
    // Current active tab
    const activeTab = ref(props.modelValue)

    const isExpand = ref(true)
    // Store expand state for each tab
    const tabExpandStates = ref({})

    provide('isExpand', isExpand)
    
    // Watch modelValue changes
    watch(() => props.modelValue, (newVal) => {
      activeTab.value = newVal
      // Restore tab expand state, default to expanded if no record
      isExpand.value = tabExpandStates.value[newVal] !== undefined 
        ? tabExpandStates.value[newVal] 
        : true 
    })

    // watch isExpand changes
    watch(() => isExpand.value, (newVal) => {
      console.log('isExpand changed:', newVal)
      // Save current tab expand state
      tabExpandStates.value[activeTab.value] = newVal
    })
    
    // Method to switch tabs
    const setActiveTab = (index) => {
      if (index === activeTab.value) {
        // Directly collapse/expand
        isExpand.value = !isExpand.value
        // Save current state
        tabExpandStates.value[activeTab.value] = isExpand.value
        console.log('collapse', isExpand.value)
        return 
      }
      activeTab.value = index
      // Restore tab expand state, default to expanded if no record
      isExpand.value = tabExpandStates.value[index] !== undefined 
        ? tabExpandStates.value[index] 
        : true
      emit('update:modelValue', index)
      
      // Trigger tab-change event
      const children = slots.default ? slots.default() : []
      const tabs = children.filter(child => 
        child.type === TabItem || (child.type && (child.type.name === 'TabItem' || child.type.__name === 'TabItem'))
      )
      if (tabs[index]) {
        emit('tab-change', {index, ...tabs[index].props})
      }
    }
    
    return () => {
      // Get all TabItem child components
      const children = slots.default ? slots.default() : []
      const tabs = children.filter(child => 
        child.type === TabItem || (child.type && (child.type.name === 'TabItem' || child.type.__name === 'TabItem'))
      )
      
      if (tabs.length === 0) {
        console.warn('No TabItem components found')
        return h('div', { class: 'tabs-container' }, 'No tabs')
      }
      
      // Create tab headers using manual rendering
      const headers = tabs.map((tab, index) => {
        const isActive = activeTab.value === index
        return h('div', {
          key: `tab-header-${index}`,
          class: {
            'tab-header': true,
            'active': isActive
          },
          onClick: () => setActiveTab(index)
        }, [
          h('div', {
            class: `flex-inline min-w-240px h-48px px-6 bg-#1E1E1E ${isActive ? 'bg-#2D2D2D' : ''}`,
          }, [
            h('div', {
              class: 'flex-1 flex items-center px-4 gap-2 cursor-pointer'
            }, [
              h('span', {
                class: `text-center duration-300 flex-1 flex items-center justify-center w-full font-500 ${isActive ? 'text-primary' : ''}`
              }, tab.props?.label || `Label ${index + 1}`),
              h(VIcon, {
                icon: isActive ? (isExpand.value ? 'tabler-chevron-up' : 'tabler-chevron-down') : 'tabler-chevron-down',
                class: ['duration-300', isActive ? 'text-primary' : '']
              })
            ])
          ])
        ])
      })
      
      // Create content area - Key change: use v-show instead of conditional rendering
      const content = h('div', { class: 'tab-content' }, 
        tabs.map((tab, index) => {
          return h('div', {
            key: `tab-content-${index}`,
            style: { display: activeTab.value === index ? 'block' : 'none' }
          }, [tab])
        })
      )
      
      return h('div', { class: 'tabs-container' }, [
        // Tab header container - add flex layout for horizontal tab arrangement
        h('div', { class: 'tab-headers flex' }, headers),
        content
      ])
    }
  }
})
