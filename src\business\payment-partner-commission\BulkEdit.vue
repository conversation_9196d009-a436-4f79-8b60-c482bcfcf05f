<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useAttrs } from 'vue';
import AppDateTimePicker from '@/components/AppDateTimePicker.vue';
import { commissionRateValidator } from '@/utils/validators';

const attrs = useAttrs();
const editBtn = ref(null);
const isBulkEditModalOpen = ref(false);
const commissionRate = ref('');
const effectiveDate = ref('');

const emit = defineEmits('update')

async function openBulkEditModal() {
  isBulkEditModalOpen.value = true;
  await nextTick();

  const overlay = document.querySelector('.bulk-edit-overlay');
  if (overlay) {
    const btn = editBtn.value?.$el || document.querySelector('.edit-btn');
    if (!btn) return;

    const btnRect = btn.getBoundingClientRect();
    const popupEl = overlay.querySelector('.v-overlay__content');
    if (!popupEl) return;

    const popupRect = popupEl.getBoundingClientRect();
    const windowHeight = window.innerHeight;

    // Calculate available space below
    const bottomSpace = windowHeight - btnRect.bottom;
    // Check if there is enough space to show below
    const showBelow = bottomSpace >= popupRect.height + 8;

    if (showBelow) {
      // Show below the button
      popupEl.style.top = `${btnRect.bottom + 8}px`;
      popupEl.style.bottom = 'auto';
    } else {
      // Show above the button
      popupEl.style.bottom = `${windowHeight - btnRect.top + 8}px`;
      popupEl.style.top = 'auto';
    }

    // Horizontal alignment
    popupEl.style.left = `${btnRect.left}px`;
  }
}

function handleCancel() {
  isBulkEditModalOpen.value = false;
  // Clear data
  commissionRate.value = '';
  effectiveDate.value = '';
}

function handleApply() {
  // Implement apply changes logic
  const data = {
    commissionRate: commissionRate.value,
    effectiveDate: effectiveDate.value,
  };
  emit('update', data);
  // Close modal
  handleCancel();
}
</script>

<template>
  <div class="relative">
    <VBtn ref="editBtn" v-bind="attrs" @click="openBulkEditModal">EDIT</VBtn>

    <VOverlay v-model="isBulkEditModalOpen" class="bulk-edit-overlay" scroll-strategy="close" :scrim="true" persistent>
      <VCard width="400" class="bulk-edit-card">
        <VCardTitle class="bulk-edit-title">Bulk Edit</VCardTitle>
        <VCardText class="bulk-edit-content">
          <div class="field-container">
            <label class="field-label">Commission Rate (%)</label>
            <AppTextField placeholder="Commission Rate" v-model="commissionRate" type="number" min="0" max="100" decimal-places="2">
              <template #append-inner>%</template>
            </AppTextField>
          </div>

          <div class="field-container">
            <label class="field-label">Effective On</label>
            <AppDateTimePicker v-model="effectiveDate" :config="{
              enableTime: true,
              dateFormat: 'd M Y H:i:S',
              time_24hr: true,
            }"  hide-details variant="outlined" prepend-inner-icon="mdi-calendar-clock-outline"  placeholder="Select Effective On"/>
          </div>
        </VCardText>
        <VCardActions class="flex justify-end px-6! py-4">
          <VBtn @click="handleCancel" variant="outlined" color="secondary" class="w-90px">CANCEL</VBtn>
          <VBtn @click="handleApply" color="primary" variant="flat" class="w-120px">APPLY</VBtn>
        </VCardActions>
      </VCard>
    </VOverlay>
  </div>
</template>

<style scoped>
.edit-btn {
  text-transform: uppercase;
  font-weight: 500;
}

.bulk-edit-overlay {
  position: absolute;
}

:deep(.v-overlay__scrim) {
  background-color: rgba(0, 0, 0, 0);
}

:deep(.v-overlay__content) {
  position: absolute;
  width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.bulk-edit-card {
  background-color: #1E1E1E;
  border-radius: 4px;
  color: white;
  border: 1px solid #333;
}

.bulk-edit-title {
  font-size: 1rem;
  font-weight: 700;
  padding: 24px;
  color: white;
  background-color: #1E1E1E;
}

.bulk-edit-content {
  padding: 0 24px;
  background-color: #1E1E1E;
}

.field-container {
  margin-bottom: 16px;
}

.field-label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}
</style>