<script setup>
import AppTextField from '@/components/AppTextField.vue'
import { computed, onMounted, ref } from 'vue'
import TablePagination from '@/components/TablePagination.vue'
import AppSelect from '@/components/AppSelect.vue'
import AppDateTimePicker from '@/components/AppDateTimePicker.vue'
import { useRouter } from 'vue-router'
import {
  usePINCardTypes,
  useProductNames,
  useRedemptionRegions,
} from '@/business/pin-inventory/pin-list/usePINListRequestsConfig'
import AppAutocomplete from '@/components/AppAutocomplete.vue'
import { useDict } from '@/hooks/useDict'
import { useDownloadCSV } from '@/business/pin-inventory/pin-list/useDownloadCSV'

const createDefaultQuery = () => {
  return {
    batchName: null,
    statuses: null,
    requestedOn: null,
    productIds: null,
    pinCardTypeIds: null,
    redemptionRegionIds: null,
    page: 1,
    pageSize: 10,
    sortBy: null,
    orderBy: null,
  }
}

const headers = [
  {
    title: 'No',
    key: 'no',
    sortable: false,
    width: 70,
  },
  {
    title: 'Pin List Id',
    key: 'taskId',
  },
  {
    title: 'Batch Name',
    key: 'batchName',
  },
  {
    title: 'Product Name',
    key: 'productId',
  },
  {
    title: 'Redemption Region',
    key: 'redemptionRegionId',
  },
  {
    title: 'Redemption Currency',
    key: 'redemptionCurrencyCode',
  },
  {
    title: 'PIN Card Type',
    key: 'pinCardTypeId',
  },
  {
    title: 'Activation From',
    key: 'activationPeriodStart',
  },
  {
    title: 'Activation Till',
    key: 'activationPeriodEnd',
  },
  {
    title: 'Redemption From',
    key: 'redemptionPeriodStart',
  },
  {
    title: 'Redemption Till',
    key: 'redemptionPeriodEnd',
  },
  {
    title: 'Requestor',
    key: 'createdBy',
  },
  {
    title: 'Requested On',
    key: 'createdDateTime',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'Actions',
    key: 'id',
  },
]

const searchQuery = ref(createDefaultQuery())
const isLoading = ref(false)
const pinListData = ref(null)
const totalRecords = ref(0)
const itemsPerPage = ref(10)
const page = ref(1)

const { items: pinListStatusItems } = useDict('PINInventoryPINListStatus')

const statusDict = computed(() => {
  const nameMap =
    pinListStatusItems.value?.reduce((acc, item) => {
      acc[item.itemCode] = item.itemName
      return acc
    }, {}) || {}

  const options =
    pinListStatusItems.value?.map((item) => ({
      title: item.itemName,
      value: item.itemCode,
    })) || []

  return {
    nameMap,
    options,
    getDisplayName: (code) => nameMap[code] || code,
  }
})

const getStatusDisplayName = (code) => statusDict.value.getDisplayName(code)
const statuses = computed(() => statusDict.value.options)

const productNameOptions = useProductNames()

const pinCardTypeOptions = usePINCardTypes()

const redemptionRegionOptions = useRedemptionRegions()

const getQueryParams = () => {
  const params = {}
  params.page = searchQuery.value.page
  params.size = searchQuery.value.pageSize

  params.batchName = searchQuery.value.batchName
  params.statuses = searchQuery.value.statuses
  params.productIds = searchQuery.value.productIds
  params.pinCardTypeIds = searchQuery.value.pinCardTypeIds
  params.redemptionRegionIds = searchQuery.value.redemptionRegionIds

  if (searchQuery.value.sortBy && searchQuery.value.orderBy) {
    params.orderItemList = [
      {
        column: searchQuery.value.sortBy,
        asc: searchQuery.value.orderBy === 'asc',
      },
    ]
  } else {
    params.orderItemList = null
  }

  if (searchQuery.value.requestedOn) {
    const [start, end] = searchQuery.value.requestedOn.split('to')
    if (start) {
      params.requestOnStartTime = dateUtil.format(
        start,
        'YYYY-MM-DDTHH:mm:ss.SSSZ'
      )
      params.requestOnEndTime = dateUtil.format(
        start,
        'YYYY-MM-DDT23:59:59.SSSZ'
      )
    }
    if (end) {
      params.requestOnEndTime = dateUtil.format(end, 'YYYY-MM-DDT23:59:59.SSSZ')
    }
  }

  return params
}

const loadData = async () => {
  try {
    isLoading.value = true
    pinListData.value = null
    const params = getQueryParams()
    const res = await $api('/api/pin-inventory/v1/pin-list/page-query', {
      method: 'POST',
      body: params,
    })

    pinListData.value = res.data
    totalRecords.value = res.data.total
  } catch (error) {
    console.error('Error loading data:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  loadData()
})

const handlePageChange = (newPage) => {
  page.value = newPage
  searchQuery.value.page = newPage

  loadData()
}

const handleItemsPerPageChange = (newItemsPerPage) => {
  itemsPerPage.value = parseInt(newItemsPerPage, 10)
  searchQuery.value.pageSize = itemsPerPage.value
  page.value = 1
  searchQuery.value.page = 1

  loadData()
}

const handleSearch = () => {
  page.value = 1
  searchQuery.value.page = 1
  loadData()
}

const router = useRouter()
const handleRequestCSV = () => {
  router.push('/pin-inventory/pin-list/add')
}

const pinLists = computed(() => {
  return (
    pinListData.value?.records?.map((item, index) => {
      let config = {}
      try {
        if (item.config) {
          config = JSON.parse(item.config)
        }
      } catch (error) {
        message.error(error)
      }

      return {
        ...item,
        ...config,
        config: undefined,
        no: (page.value - 1) * itemsPerPage.value + index + 1,
        createdDateTime: dateUtil.format(
          item.createdDateTime,
          'DD-MMM-YYYY HH:mm:ss'
        ),
        activationPeriodStart: dateUtil.formatWithUTCOffsetLabel(
          config.activationPeriodStart,
          config.utcOffset
        ),
        activationPeriodEnd: dateUtil.formatWithUTCOffsetLabel(
          config.activationPeriodEnd,
          config.utcOffset
        ),
        redemptionPeriodStart: dateUtil.formatWithUTCOffsetLabel(
          config.redemptionPeriodStart,
          config.utcOffset
        ),
        redemptionPeriodEnd: dateUtil.formatWithUTCOffsetLabel(
          config.redemptionPeriodEnd,
          config.utcOffset
        ),
      }
    }) || []
  )
})

const getStatusStyleConfig = (status, type) => {
  const statusDefination = {
    SUCCESS: {
      color: '#44D62C',
      icon: 'mdi-check-decagram',
      size: '14px',
    },
    FAILED: {
      color: '#D50000',
      icon: 'mdi-alert-circle',
      size: '14px',
    },
    PROCESSING: {
      color: '#2962FF',
      icon: 'mdi-circle-slice-4',
      size: '14px',
      iconStyle: { transform: 'rotate(90deg)' },
    },
  }
  return statusDefination[status][type]
}

const productNameMap = computed(() => {
  return productNameOptions.value.reduce((acc, item) => {
    acc[item.value] = item.title
    return acc
  }, {})
})

const getProductDisplayName = (productId) => {
  return productNameMap.value[productId] || productId
}

const redemptionRegionMap = computed(() => {
  return redemptionRegionOptions.value.reduce((acc, item) => {
    acc[item.value] = item.title
    return acc
  }, {})
})

const getRedemptionRegionDisplayName = (regionId) => {
  return redemptionRegionMap.value[regionId] || regionId
}

const pinCardTypeMap = computed(() => {
  return pinCardTypeOptions.value.reduce((acc, item) => {
    acc[item.value] = item.title
    return acc
  }, {})
})

const getPinCardTypeDisplayName = (typeId) => {
  return pinCardTypeMap.value[typeId] || typeId
}
</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            RG PIN List Requests
            <VSpacer />
            <div class="app-user-search-filter d-flex align-center gap-4">
              <VBtn v-can="['View', 'RGPinList']" @click="handleRequestCSV">
                CREATE
              </VBtn>
            </div>
          </div>
        </VCardTitle>
      </VCardItem>
      <VCard border>
        <VCardText class="px-0">
          <VRow>
            <VCol cols="12" md="4">
              <AppTextField
                outline-lable="Batch Name"
                v-model="searchQuery.batchName"
                placeholder="Search batch name"
                clearable
                clear-icon="tabler-x"
                prepend-inner-icon="mdi-magnify"
                @update:model-value="handleSearch"
              />
            </VCol>

            <VCol cols="12" md="4">
              <AppSelect
                outline-lable="Status"
                v-model="searchQuery.statuses"
                placeholder="Select Status"
                :items="statuses"
                multiple
                clearable
                clear-icon="tabler-x"
                @update:model-value="handleSearch"
              />
            </VCol>

            <VCol cols="12" md="4">
              <AppDateTimePicker
                outline-lable="Requested On"
                v-model="searchQuery.requestedOn"
                :config="{
                  mode: 'range',
                  enableTime: false,
                  dateTimeFormat: 'Y-m-d',
                }"
                placeholder="Select Requested On"
                clearable
                clear-icon="tabler-x"
                prepend-inner-icon="mdi-calendar-range"
                @update:model-value="handleSearch"
              />
            </VCol>

            <VCol cols="12" md="4">
              <AppAutocomplete
                outline-lable="Product Names"
                v-model="searchQuery.productIds"
                placeholder="Select Product Names"
                :items="productNameOptions"
                multiple
                clearable
                clear-icon="tabler-x"
                filterable
                @update:model-value="handleSearch"
              />
            </VCol>

            <VCol cols="12" md="4">
              <AppAutocomplete
                outline-lable="PIN Card Types"
                v-model="searchQuery.pinCardTypeIds"
                placeholder="Select PIN Card Types"
                :items="pinCardTypeOptions"
                multiple
                clearable
                clear-icon="tabler-x"
                filterable
                @update:model-value="handleSearch"
              />
            </VCol>

            <VCol cols="12" md="4">
              <AppAutocomplete
                outline-lable="Redemption Regions"
                v-model="searchQuery.redemptionRegionIds"
                placeholder="Select Redemption Regions"
                :items="redemptionRegionOptions"
                multiple
                clearable
                clear-icon="tabler-x"
                filterable
                @update:model-value="handleSearch"
              />
            </VCol>
          </VRow>
        </VCardText>
      </VCard>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer
        :items="pinLists"
        :headers="headers"
        :items-length="totalRecords"
        :loading="isLoading"
        class="text-no-wrap"
        disable-sort
      >
        <template #item.productId="{ item }">
          {{ getProductDisplayName(item.productId) }}
        </template>

        <template #item.redemptionRegionId="{ item }">
          {{ getRedemptionRegionDisplayName(item.redemptionRegionId) }}
        </template>

        <template #item.pinCardTypeId="{ item }">
          {{ getPinCardTypeDisplayName(item.pinCardTypeId) }}
        </template>

        <!--   Status Field     -->
        <template #item.status="{ item }">
          <VIcon
            :icon="getStatusStyleConfig(item.status, 'icon')"
            :color="getStatusStyleConfig(item.status, 'color')"
            :size="getStatusStyleConfig(item.status, 'size')"
            :style="getStatusStyleConfig(item.status, 'iconStyle') || {}"
          />
          {{ getStatusDisplayName(item.status) }}
          <!-- Use the function to display name -->
        </template>

        <template #item.id="{ item }">
          <VBtn
            icon="mdi-download"
            variant="text"
            size="15px"
            color="1E1E1E"
            :disabled="item.status !== 'SUCCESS'"
            @click="item.status === 'SUCCESS' && useDownloadCSV(item.id)"
            v-tooltip="'Download CSV'"
            class="hover:text-primary"
          />
        </template>

        <template #bottom>
          <TablePagination
            :page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalRecords"
            @update:page="handlePageChange"
          >
            <div class="d-flex gap-3">
              <AppSelect
                :model-value="itemsPerPage"
                :items="[
                  { value: 10, title: '10' },
                  { value: 25, title: '25' },
                  { value: 50, title: '50' },
                  { value: 100, title: '100' },
                ]"
                style="inline-size: 6.25rem"
                @update:model-value="handleItemsPerPageChange"
              />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
      <!-- /SECTION -->
    </VCard>
  </section>
</template>
