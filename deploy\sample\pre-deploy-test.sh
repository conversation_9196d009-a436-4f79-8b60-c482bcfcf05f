#!/usr/bin/env bash

declare -x SERVICE_NAME=paychn-xxx
declare -xr BUILD=PREBUILD
declare -x TEST_DIR=Paychn.XXX.Test
declare -x SERVICE_ENV=dev

deploy_override()
{
    echo "nothing to override"
}

prebuild()
{
    if [ "$ENV" != 'zgp' ]; then
        #dotnet nuget locals all --clear
        sed -e "s;%SERVICE_NAME%;$SERVICE_NAME;g" \
            -e "s;%SERVICE_ENV%;$SERVICE_ENV;g" \
            config/env-test.json > env-test.json 

        while read -r name value; do
            export "$name=$value"
        done< <(jq -r '.[] | "\(.name) \(.value)"' env-test.json)
        
        sed -e "s;%ENV%;ZGD;g" config/secret.json > secret.json 
        for k in $(jq -c '.[]' secret.json); do  
        name=$(echo "$k" | jq  -r '.name') ; 
        valueFrom=$(echo "$k" | jq  -r '.valueFrom') ; 
        secret=$(aws secretsmanager get-secret-value --secret-id "$valueFrom" --region ap-southeast-1 | jq -r '.SecretString' | tr -d '\r\n\t'); 
        export "$name=$secret"
        done

        dotnet test "./$TEST_DIR/$TEST_DIR.csproj" 
        if [ $? -ne 0 ]; then
            echo "Tests must pass before commit!"
        exit 1
        fi
    fi
}



