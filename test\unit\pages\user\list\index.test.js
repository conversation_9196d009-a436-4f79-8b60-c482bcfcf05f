import { beforeEach, describe, expect, it, vi } from 'vitest'
import { nextTick, ref } from 'vue'

import UserList from '@/pages/user/list/index.vue'
import { createTestingPinia } from '@pinia/testing'
import { mount } from '@vue/test-utils'
import { vuetify } from '@test/unit/setup/vuetify'

// Use global mockCookies and mockStorage
const { mockCookies, mockStorage } = global


describe('UserList.vue', () => {
  let wrapper
  
  beforeEach(() => {
    // Clear all mocks
    vi.clearAllMocks()
    
    // Clear storage
    Object.keys(mockCookies).forEach(key => delete mockCookies[key])
    Object.keys(mockStorage).forEach(key => delete mockStorage[key])

    // 初始化响应式数据
    const mockUsers = {
      data: {
        records: [
          {
            "accountId": 86,
            "accountEmail": "<EMAIL>",
            "accountName": null,
            "accountStatusId": 1,
            "userGroupList": [
                {
                    "id": 1,
                    "name": "updated name184",
                    "statusId": 1,
                    "roleList": [
                        {
                            "id": 15,
                            "name": "***************",
                            "statusId": 1
                        }
                    ]
                },
                {
                    "id": 2,
                    "name": "insertTest541",
                    "statusId": 1,
                    "roleList": [
                        {
                            "id": 34,
                            "name": "UpdateName184",
                            "statusId": 1
                        }
                    ]
                }
            ],
            "createdDateTime": "2025-02-26T03:02:06.386162Z",
            "updatedDateTime": "2025-02-26T03:04:23.393389Z"
          }
        ],
        total: 1
      }
    }

    // Mock API call
    global.$api = vi.fn().mockResolvedValue(mockUsers)

    // Mount component with correct setup
    wrapper = mount(UserList, {
      global: {
        plugins: [
          createTestingPinia(),
          vuetify
        ],
        stubs: {
          AppTextField: true,
          AppSelect: true,
          AppDateTimePicker: true,
          AppCollapsible: true,
          VCard: true,
          VCardItem: true,
          VCardTitle: true,
          VCardText: true,
          VBtn: true,
          VRow: true,
          VCol: true,
          VSpacer: true,
          VDataTableServer: {
            template: `
              <div class="v-data-table">
                <div v-if="$attrs.items && $attrs.items.length">
                  <div v-for="item in $attrs.items" :key="item.id" class="user-row">
                    {{ item.accountEmail }}
                  </div>
                </div>
              </div>
            `,
            props: ['items']
          }
        },
        mocks: {
          $api: global.$api
        }
      }
    })

    // 直接设置依赖数据
    wrapper.vm.usersData = ref({
      records: [
        {
          "accountId": 86,
          "accountEmail": "<EMAIL>",
          "accountName": null,
          "accountStatusId": 1,
          "userGroupList": [
              {
                  "id": 1,
                  "name": "updated name184",
                  "statusId": 1,
                  "roleList": [
                      {
                          "id": 15,
                          "name": "***************",
                          "statusId": 1
                      }
                  ]
              },
              {
                  "id": 2,
                  "name": "insertTest541",
                  "statusId": 1,
                  "roleList": [
                      {
                          "id": 34,
                          "name": "UpdateName184",
                          "statusId": 1
                      }
                  ]
              }
          ],
          "createdDateTime": "2025-02-26T03:02:06.386162Z",
          "updatedDateTime": "2025-02-26T03:04:23.393389Z"
        }
      ],
      total: 1
    })
    wrapper.vm.page = ref(1)
    wrapper.vm.itemsPerPage = ref(10)

    // 确保数据加载
    wrapper.vm.loadData()

    // 确保模拟 API 返回预期的数组格式
    global.$api.mockResolvedValueOnce({ 
      data: [
        { id: 1, name: '管理组', /* 其他必要属性 */ },
        { id: 2, name: '用户组', /* 其他必要属性 */ }
      ] 
    });
  })

  it('should load user data on initialization', async () => {
    await nextTick()
    
    // 等待数据加载
    await new Promise(resolve => setTimeout(resolve, 0))
    
    // 使用 toRaw 获取原始值
    const accounts = wrapper.vm.accounts
    
    expect(accounts).toBeDefined()
    expect(accounts.length).toBeGreaterThan(0)
    expect(accounts[0]).toMatchObject({
      accountEmail: '<EMAIL>'
    })
  })

  it('should set search query parameters correctly', async () => {
    // Clear previous API call records
    global.$api.mockClear()
    
    // Set query parameters
    wrapper.vm.searchQuery.value.email = '<EMAIL>'
    
    // Wait for watch execution
    await wrapper.vm.handleSearch()
    
    // Wait for async operation
    await nextTick()

    // Verify API call
    expect(global.$api).toHaveBeenCalledWith('/api/admin-api/v1/account/page', {
      method: 'POST',
      body: expect.objectContaining({
        emailLike: '<EMAIL>',
        page: 1,
        size: 10
      })
    })
  })

  it('should reset query parameters to default values', async () => {
    // Set query parameters
    wrapper.vm.searchQuery.value.email = '<EMAIL>'

    // Reset
    await wrapper.vm.resetQueryParams()
    await nextTick()

    // Verify reset values
    expect(wrapper.vm.searchQuery.value.email).toBe('')
  })

  it('should handle pagination changes correctly', async () => {
    // Modify searchQuery directly
    wrapper.vm.searchQuery.value.page = 2
    
    // Wait for watch execution
    await nextTick()
    
    // Trigger handlePageChange
    await wrapper.vm.handlePageChange(2)
    
    // Wait for async operation
    await nextTick()

    // Verify updated searchQuery
    expect(wrapper.vm.searchQuery.value.page).toBe(2)
  })

  it('should reload data when refresh button is clicked', async () => {
    // Clear previous call records
    global.$api.mockClear()

    // Click refresh
    await wrapper.vm.loadData()
    await nextTick()

    // Verify API call
    expect(global.$api).toHaveBeenCalled()
  })

  it('should convert status display correctly', () => {
    const status = wrapper.vm.resolveUserStatus(1)
    expect(status).toBe('Enable')
  })

  it('should convert status style correctly', () => {
    const style = wrapper.vm.resolveUserStatusVariant(1)
    expect(style).toBe('#69F0AE')
  })
})
