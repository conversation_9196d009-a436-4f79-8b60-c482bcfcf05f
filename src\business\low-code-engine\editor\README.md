# 低代码编辑器重构设计

## 🎯 设计目标

### 问题分析
1. **当前编辑器过于复杂**：三栏布局操作繁琐，用户需要频繁切换面板
2. **元数据结构直接暴露**：前端被后端结构"绑架"，缺乏灵活性
3. **缺乏可视化编辑**：用户难以直观理解页面结构
4. **拖拽体验差**：原生拖拽API不够流畅

### 解决思路
1. **简化UI设计**：采用更直观的布局，减少操作复杂度
2. **增加适配层**：前端与后端元数据结构解耦
3. **所见即所得**：提供可视化编辑体验
4. **优化交互**：使用interact.js提升拖拽体验

## 🏗️ 新架构设计

### 1. 编辑器布局重构

```
┌─────────────────────────────────────────┐
│ 顶部工具栏 (保存/预览/发布/撤销/重做)      │
├─────────────────────────────────────────┤
│ 左侧: 组件树 + 页面结构可视化              │
├─────────────────────────────────────────┤
│ 主区域: 所见即所得编辑器 (WYSIWYG)        │
├─────────────────────────────────────────┤
│ 底部: 属性编辑面板 (可折叠)               │
└─────────────────────────────────────────┘
```

### 2. 数据流重构

```
后端元数据 → 适配器 → 前端友好结构 → 编辑器 → 渲染器 → 最终页面
                ↑
            解耦层
```

### 3. 核心模块

#### A. 适配器层 (Adapter Layer)
- **`metadataAdapter.js`**: 元数据格式转换
- **`componentAdapter.js`**: 组件配置适配
- **`pageAdapter.js`**: 页面配置适配

#### B. 编辑器层 (Editor Layer)
- **`WysiwygEditor.vue`**: 所见即所得主编辑器
- **`ComponentTree.vue`**: 组件树结构可视化
- **`PropertyPanel.vue`**: 属性编辑面板
- **`Toolbar.vue`**: 顶部工具栏

#### C. 交互层 (Interaction Layer)
- **`useInteract.js`**: interact.js 封装
- **`useDragDrop.js`**: 拖拽逻辑封装
- **`useSelection.js`**: 选择逻辑封装

## 🎨 UI/UX 设计原则

### 1. 简化操作流程
- **一键生成**：快速生成常用页面类型
- **可视化编辑**：直接点击编辑，减少属性面板依赖
- **智能推荐**：根据场景自动推荐组件

### 2. 提升编辑体验
- **实时预览**：编辑时实时看到效果
- **撤销重做**：支持操作历史
- **快捷键**：常用操作支持快捷键

### 3. 降低学习成本
- **引导式设计**：新手引导和提示
- **模板系统**：提供常用模板
- **帮助文档**：内置帮助和示例

## 🔧 技术实现要点

### 1. Interact.js 集成
```javascript
// 拖拽配置
interact('.draggable-component').draggable({
  inertia: true,
  modifiers: [interact.modifiers.restrictRect()],
  listeners: {
    start: handleDragStart,
    move: handleDragMove,
    end: handleDragEnd
  }
})

// 放置区域配置
interact('.dropzone').dropzone({
  ondrop: handleDrop,
  ondropenter: handleDropEnter,
  ondropleave: handleDropLeave
})
```

### 2. 适配器模式
```javascript
// 后端格式 → 前端格式
export function backendToFrontend(backendMeta) {
  return {
    id: backendMeta.id,
    name: backendMeta.name,
    components: backendMeta.components.map(adaptComponent),
    // ... 更多转换逻辑
  }
}

// 前端格式 → 后端格式
export function frontendToBackend(frontendMeta) {
  return {
    id: frontendMeta.id,
    name: frontendMeta.name,
    components: frontendMeta.components.map(deadaptComponent),
    // ... 更多转换逻辑
  }
}
```

### 3. 状态管理
```javascript
// 编辑器状态
const editorState = reactive({
  selectedComponent: null,
  pageStructure: [],
  history: [],
  currentIndex: -1,
  isDirty: false
})

// 操作历史
const history = {
  push(action) {
    // 添加操作到历史
  },
  undo() {
    // 撤销操作
  },
  redo() {
    // 重做操作
  }
}
```

## 📋 实施计划

### Phase 1: 基础重构 (1-2周)
- [x] 重构 ComponentLibrary.vue (使用interact.js)
- [ ] 创建适配器层基础结构
- [ ] 重构编辑器主布局

### Phase 2: 核心功能 (2-3周)
- [ ] 实现所见即所得编辑器
- [ ] 实现组件树可视化
- [ ] 实现属性面板重构

### Phase 3: 高级功能 (1-2周)
- [ ] 实现撤销重做功能
- [ ] 实现模板系统
- [ ] 实现快捷键支持

### Phase 4: 优化完善 (1周)
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 文档完善

## 🎯 预期效果

### 用户体验提升
- **操作效率提升50%**：减少不必要的面板切换
- **学习成本降低30%**：更直观的界面设计
- **编辑体验提升**：流畅的拖拽和实时预览

### 开发效率提升
- **代码维护性提升**：清晰的架构分层
- **扩展性增强**：适配器模式便于扩展
- **测试覆盖提升**：模块化设计便于测试

## 📝 注意事项

1. **向后兼容**：确保现有功能不受影响
2. **渐进式重构**：分阶段实施，避免一次性大改
3. **性能考虑**：大量组件时的渲染性能
4. **错误处理**：完善的错误处理和用户提示
