<script setup lang="jsx">
import { ref, computed, watch, watchEffect, inject } from 'vue';
import { useStorage } from '@/hooks/useStorage';
import PaymentChannelTable from './PaymentChannelTable.vue';
import AppTextField from '@/components/AppTextField.vue';
import AppAutocomplete from '@/components/AppAutocomplete.vue';
import AppDateTimePickerWithActions from '@/components/AppDateTimePickerWithActions.vue';
import { useMerchantCurrencyList } from './useMerchantCurrency';
import { useLocalPaymentChannelList, useLocalCommissionTerritoryList } from './usePaymentChannel';
// state 
const tabIndex = inject('tabIndex')
const razerGoldSelectedIds = inject('razerGoldSelectedIds')
const razerGoldPinSelectedIds = inject('razerGoldPinSelectedIds')
const thirdPartySelectedIds = inject('thirdPartySelectedIds')
const razerGoldPaymentChannelItems = inject('razerGoldPaymentChannelItems')
const razerGoldPinPaymentChannelItems = inject('razerGoldPinPaymentChannelItems')
const thirdPartyPaymentChannelItems = inject('thirdPartyPaymentChannelItems')

// Inject AdditionalCurrencySetting related data
const razerGoldAdditionalCurrencyItems = inject('razerGoldAdditionalCurrencyItems')
const razerGoldPinAdditionalCurrencyItems = inject('razerGoldPinAdditionalCurrencyItems')
const thirdPartyAdditionalCurrencyItems = inject('thirdPartyAdditionalCurrencyItems')
const updateAdditionalCurrencyItems = inject('updateAdditionalCurrencyItems')
const additionalCurrencyErrors = inject('additionalCurrencyErrors')

// Use useStorage to manage data for different tabs
const getStorageKey = (key) => {
  const tabNames = ['razerGold', 'razerGoldPin', 'thirdParty']
  return `${tabNames[tabIndex.value]}${key.charAt(0).toUpperCase() + key.slice(1)}`
}

// Create independent storage for each tab
const items = ref([])
const searchQuery = ref({
  paymentChannel: null,
  commissionTerritory: null
})
const selectedRows = ref([])

const { merchantCurrencyList } = useMerchantCurrencyList()

// Initialize current tab data
const initCurrentTabData = () => {
  if (tabIndex.value === 0) {
    items.value = razerGoldAdditionalCurrencyItems.value;
  } else if (tabIndex.value === 1) {
    items.value = razerGoldPinAdditionalCurrencyItems.value;
  } else if (tabIndex.value === 2) {
    items.value = thirdPartyAdditionalCurrencyItems.value;
  }
}

// Watch tabIndex changes and load corresponding tab data
watch(tabIndex, () => {
  initCurrentTabData();
}, { immediate: true })

// Watch data changes in parent components
watch([
  razerGoldAdditionalCurrencyItems,
  razerGoldPinAdditionalCurrencyItems,
  thirdPartyAdditionalCurrencyItems
], () => {
  initCurrentTabData();
}, { deep: true })

// Watch items changes and update parent component
watch(items, (newItems) => {
  if (tabIndex.value === 0) {
    razerGoldAdditionalCurrencyItems.value = newItems;
  } else if (tabIndex.value === 1) {
    razerGoldPinAdditionalCurrencyItems.value = newItems;
  } else if (tabIndex.value === 2) {
    thirdPartyAdditionalCurrencyItems.value = newItems;
  }
}, { deep: true })

const getPaymentChannelItems = (selectedIds, paymentChannelList) => {
  return paymentChannelList.filter(item => selectedIds.includes(item.id));
}

const paymentChannelItems = computed(() => {
  console.log('tabIndex', tabIndex.value)
  if (tabIndex.value === 0) {
    return getPaymentChannelItems(razerGoldSelectedIds.value, razerGoldPaymentChannelItems.value)
  } else if (tabIndex.value === 1) {
    return getPaymentChannelItems(razerGoldPinSelectedIds.value, razerGoldPinPaymentChannelItems.value)
  } else if (tabIndex.value === 2) {
    return getPaymentChannelItems(thirdPartySelectedIds.value, thirdPartyPaymentChannelItems.value)
  }
  return []
})

const { paymentChannelList: paymentChannelOptions } = useLocalPaymentChannelList(paymentChannelItems)
const { commissionTerritoryList } = useLocalCommissionTerritoryList(paymentChannelItems)

watchEffect(() => {
  // console.log('watch tempItems', paymentChannelItems.value)
  // console.log('watch paymentChannelOptions', paymentChannelOptions.value)
  console.log('watch additionalCurrencyErrors', additionalCurrencyErrors.value)
})

// event 
const clearAllSelected = () => { }

const handleDelete = (item) => {
  items.value = items.value.filter(i => i.no !== item.no)
  validateDuplicates()
}

const handleEdit = (item) => {
  item.isEditing = true
}

// Calculate next available no value
const getNextNo = () => {
  if (items.value.length === 0) return 1;

  // Find current maximum no value
  const maxNo = Math.max(...items.value.map(item => item.no));
  return maxNo + 1;
}

const handleAdd = () => {
  // Get next available no value
  const nextNo = getNextNo();

  // Create new item
  const newItem = {
    paymentMethodName: null,
    paymentMethodId: null,
    merchantCurrency: null,
    minAmount: null,
    commissionRate: null,
    fixAmount: null,
    commissionTerritory: null,
    territoryName: null,
    effectiveOn: null,
    isEditing: true,
    isDoubleClickEdit: false,
    convertCommissionRate: null,
    convertEffectiveOn: null,
    no: nextNo,
    status: "Draft",
    isMinAmountEditing: false,
    isFixAmountEditing: false,
    isMerchantCurrencyEditing: false,
    isPaymentChannelEditing: false,
    isCommissionRateEditing: false,
    isEffectiveOnEditing: false,
    regionId: null,
    id: null
  };

  // Add to beginning of array
  items.value.unshift(newItem);

  // Recalculate no values
  items.value.forEach((item, index) => {
    item.no = index + 1;
  });

  // Save to localStorage
  useStorage(getStorageKey('items'), items.value)

  // Directly update corresponding data
  if (tabIndex.value === 0) {
    razerGoldAdditionalCurrencyItems.value = items.value;
  } else if (tabIndex.value === 1) {
    razerGoldPinAdditionalCurrencyItems.value = items.value;
  } else if (tabIndex.value === 2) {
    thirdPartyAdditionalCurrencyItems.value = items.value;
  }

  validateDuplicates()
}

// Add function to validate single field
const validateField = (item, index, field) => {
  let isValid = true;
  
  switch (field) {
    case 'paymentChannel':
      isValid = !!item.paymentMethodId;
      break;
    case 'merchantCurrency':
      isValid = !!item.merchantCurrency;
      break;
    case 'minAmount':
      isValid = !!item.minAmount && parseFloat(item.minAmount) >= 0;
      break;
    case 'commissionRate':
      isValid = !!item.convertCommissionRate && 
                parseFloat(item.convertCommissionRate) >= 0 && 
                parseFloat(item.convertCommissionRate) <= 100 &&
                /^\d+(\.\d{0,2})?$/.test(String(item.convertCommissionRate));
      break;
    case 'fixAmount':
      isValid = !!item.fixAmount && parseFloat(item.fixAmount) >= 0;
      break;
    case 'effectiveOn':
      isValid = !!item.convertEffectiveOn;
      break;
  }

  // If current field is invalid, add to error list
  if (!isValid) {
    if (!currentTabErrors.value[field].includes(index)) {
      currentTabErrors.value[field].push(index);
    }
  } else {
    // If current field is valid, remove from error list
    const errorIndex = currentTabErrors.value[field].indexOf(index);
    if (errorIndex > -1) {
      currentTabErrors.value[field].splice(errorIndex, 1);
    }
  }

  // Update error status in parent component
  if (tabIndex.value === 0) {
    additionalCurrencyErrors.value.razerGold = { ...currentTabErrors.value };
  } else if (tabIndex.value === 1) {
    additionalCurrencyErrors.value.razerGoldPin = { ...currentTabErrors.value };
  } else if (tabIndex.value === 2) {
    additionalCurrencyErrors.value.thirdParty = { ...currentTabErrors.value };
  }
}

// Field modification handler functions
const handlePaymentChannelChange = (item, value) => {
  item.paymentMethodId = value;
  const index = items.value.findIndex(i => i.no === item.no);
  validateField(item, index, 'paymentChannel');

  // Set default value
  const paymentChannel = paymentChannelOptions.value.find(i => i.value === value);
  if (paymentChannel) {
    setDefaultValues(item, paymentChannel);
  }

  validateDuplicates()
}

const setDefaultValues = (item, paymentChannel) => {
  item.id = paymentChannel.id
  item.minAmount = paymentChannel.minAmount
  item.fixAmount = paymentChannel.fixAmount
  item.convertCommissionRate = paymentChannel.convertCommissionRate
  item.convertEffectiveOn = paymentChannel.convertEffectiveOn
  item.regionId = paymentChannel.regionId
  item.territoryName = paymentChannel.territoryName
}

const handleMerchantCurrencyChange = (item, value) => {
  item.merchantCurrency = value;
  const index = items.value.findIndex(i => i.no === item.no);
  validateField(item, index, 'merchantCurrency');

  validateDuplicates()
}

const handleMinAmountChange = (item, value) => {
  item.minAmount = value;
  const index = items.value.findIndex(i => i.no === item.no);
  validateField(item, index, 'minAmount');
}

const handleFixAmountChange = (item, value) => {
  item.fixAmount = value;
  const index = items.value.findIndex(i => i.no === item.no);
  validateField(item, index, 'fixAmount');
}

const handleCommissionRateChange = (item, value) => {
  item.convertCommissionRate = value;
  const index = items.value.findIndex(i => i.no === item.no);
  validateField(item, index, 'commissionRate');
}

const handleEffectiveDateChange = (item, value) => {
  item.convertEffectiveOn = value;
  const index = items.value.findIndex(i => i.no === item.no);
  validateField(item, index, 'effectiveOn');
}

const handleDoubleClickEdit = (item, e, type = 'isEditing') => {
  item[type] = true;

  nextTick(() => {
    const cell = e.target.closest('[data-focus="true"]');
    if (cell) {
      const input = cell.querySelector('input');
      if (input) {
        input.focus();
      }
    }
  });
};

const handleInsert = (item) => {
  if (item.isEditing) return
  items.value.forEach(i => {
    i.isEditing = false
  })
  const newItem = {
    ...item,
    paymentMethodId: null,
    merchantCurrency: null,
    minAmount: null,
    fixAmount: null,
    convertCommissionRate: null,
    convertEffectiveOn: null,
    isEditing: true,
    no: item.no + 1,
    status: "Draft",
  }
  const index = items.value.findIndex(i => i.no === item.no)
  items.value.splice(index + 1, 0, newItem)

  // Recalculate no values
  items.value.forEach((item, index) => {
    item.no = index + 1;
  });

  // Directly update corresponding data
  if (tabIndex.value === 0) {
    razerGoldAdditionalCurrencyItems.value = items.value;
  } else if (tabIndex.value === 1) {
    razerGoldPinAdditionalCurrencyItems.value = items.value;
  } else if (tabIndex.value === 2) {
    thirdPartyAdditionalCurrencyItems.value = items.value;
  }

  validateDuplicates()
}

const handleBlur = (item, type) => {
  item[type] = false;
  
  // Validate corresponding field when losing focus
  const index = items.value.findIndex(i => i.no === item.no);
  switch (type) {
    case 'isPaymentChannelEditing':
      validateField(item, index, 'paymentChannel');
      break;
    case 'isMerchantCurrencyEditing':
      validateField(item, index, 'merchantCurrency');
      break;
    case 'isMinAmountEditing':
      validateField(item, index, 'minAmount');
      break;
    case 'isFixAmountEditing':
      validateField(item, index, 'fixAmount');
      break;
    case 'isCommissionRateEditing':
      validateField(item, index, 'commissionRate');
      break;
    case 'isEffectiveOnEditing':
      validateField(item, index, 'effectiveOn');
      break;
  }
}

const resolvePaymentChannel = (paymentChannelId) => {
  return paymentChannelOptions.value.find(i => i.id === paymentChannelId)?.title || '-'
}

// Get current tab error status
const currentTabErrors = ref({
  paymentChannel: [],
  merchantCurrency: [],
  minAmount: [],
  commissionRate: [],
  fixAmount: [],
  effectiveOn: []
});

// Watch changes in tabIndex and additionalCurrencyErrors
watch(
  [tabIndex, () => additionalCurrencyErrors.value],
  ([currentTabIndex, errors]) => {
    console.log('watch tabIndex and errors', currentTabIndex, errors);
    if (currentTabIndex === 0) {
      currentTabErrors.value = errors.razerGold;
    } else if (currentTabIndex === 1) {
      currentTabErrors.value = errors.razerGoldPin;
    } else if (currentTabIndex === 2) {
      currentTabErrors.value = errors.thirdParty;
    } else {
      currentTabErrors.value = {
        paymentChannel: [],
        merchantCurrency: [],
        minAmount: [],
        commissionRate: [],
        fixAmount: [],
        effectiveOn: []
      };
    }
  },
  { immediate: true, deep: true }
);

const props = defineProps({
  duplicateErrors: {
    type: Object,
    default: () => ({
      razerGold: { duplicate: [] },
      razerGoldPin: { duplicate: [] },
      thirdParty: { duplicate: [] }
    })
  }
});

// 添加计算属性来获取当前标签页的重复错误
const currentDuplicateErrors = computed(() => {
  if (tabIndex.value === 0) {
    return props.duplicateErrors.razerGold?.duplicate || [];
  } else if (tabIndex.value === 1) {
    return props.duplicateErrors.razerGoldPin?.duplicate || [];
  } else if (tabIndex.value === 2) {
    return props.duplicateErrors.thirdParty?.duplicate || [];
  }
  return [];
});

// 添加重复数据校验函数
const validateDuplicates = () => {
  // 创建一个 Set 来存储已经出现过的组合
  const seen = new Set();
  const duplicates = [];

  items.value.forEach((item, index) => {
    if (item.paymentMethodId && item.merchantCurrency) {
      const key = `${item.paymentMethodId}-${item.merchantCurrency}`;
      if (seen.has(key)) {
        duplicates.push(index);
      } else {
        seen.add(key);
      }
    }
  });

  // 更新错误状态
  if (tabIndex.value === 0) {
    additionalCurrencyErrors.value.razerGold.duplicate = duplicates;
  } else if (tabIndex.value === 1) {
    additionalCurrencyErrors.value.razerGoldPin.duplicate = duplicates;
  } else if (tabIndex.value === 2) {
    additionalCurrencyErrors.value.thirdParty.duplicate = duplicates;
  }
}
</script>


<template>
  <div class="pt-6 bg-#1E1E1E flex flex-col">
    <PaymentChannelTable
      v-model:items="items"
      v-model:selectedRows="selectedRows"
      :payment-channel-list="paymentChannelOptions"
      :commission-territory-list="commissionTerritoryList"
      :commission-rate-errors="currentTabErrors.commissionRate"
      :effective-on-errors="currentTabErrors.effectiveOn"
      :duplicate-errors="currentDuplicateErrors"
      @delete="handleDelete"
      @edit="handleEdit"
    >
      <template #bulk>
        <AppDateTimePickerWithActions></AppDateTimePickerWithActions>
      </template>
      <!-- Custom Payment Channel slot -->
      <template #payment-channel="{ item, index }">
        <div class="flex-center w-200px" data-focus="true"
          :class="{ 
            'border-error border-1px hover:border-none': (currentTabErrors.paymentChannel.includes(index) || currentDuplicateErrors.includes(index)) && 
                                                       !(item.isEditing || item.isPaymentChannelEditing) 
          }">
          <AppAutocomplete v-show="item.isEditing || item.isPaymentChannelEditing" placeholder="Payment Channel"
            :items="paymentChannelOptions" v-model="item.paymentMethodId"
            @update:model-value="(value) => handlePaymentChannelChange(item, value)"
            :error="currentTabErrors.paymentChannel.includes(index) || currentDuplicateErrors.includes(index)"
            @blur="handleBlur(item, 'isPaymentChannelEditing')" />
          <div v-show="!(item.isEditing || item.isPaymentChannelEditing)"
            @dblclick="handleDoubleClickEdit(item, $event, 'isPaymentChannelEditing')"
            class="w-100% px-4 h-38px rounded hover:border-primary flex align-center">
            {{ resolvePaymentChannel(item.paymentMethodId) }}
          </div>
        </div>
      </template>

      <!-- Custom Merchant Currency slot -->
      <template #merchant-currency="{ item, index }">
        <div class="flex-center w-200px" data-focus="true"
          :class="{ 
            'border-error border-1px hover:border-none': (currentTabErrors.merchantCurrency.includes(index) || currentDuplicateErrors.includes(index)) && 
                                                       !(item.isEditing || item.isMerchantCurrencyEditing) 
          }">
          <AppAutocomplete v-show="item.isEditing || item.isMerchantCurrencyEditing" placeholder="Currency"
            :items="merchantCurrencyList" v-model="item.merchantCurrency"
            @update:model-value="(value) => handleMerchantCurrencyChange(item, value)"
            :error="currentTabErrors.merchantCurrency.includes(index) || currentDuplicateErrors.includes(index)"
            @blur="handleBlur(item, 'isMerchantCurrencyEditing')" />
          <div v-show="!(item.isEditing || item.isMerchantCurrencyEditing)"
            @dblclick="handleDoubleClickEdit(item, $event, 'isMerchantCurrencyEditing')"
            class="w-100% px-4 h-38px rounded hover:border-primary flex align-center">
            {{ item.merchantCurrency || '-' }}
          </div>
        </div>
      </template>

      <!-- Custom Minimum Amount slot -->
      <template #min-amount="{ item, index }">
        <div class="flex-center w-120px" data-focus="true"
          :class="{ 'border-error border-1px hover:border-none': currentTabErrors.minAmount.includes(index) && !(item.isEditing || item.isMinAmountEditing) }">
          <AppTextField v-show="item.isEditing || item.isMinAmountEditing" placeholder="Min Amount"
            v-model="item.minAmount" min="0" type="number" decimal-places="2"
            @update:model-value="(value) => handleMinAmountChange(item, value)"
            :error="currentTabErrors.minAmount.includes(index)"
            @blur="handleBlur(item, 'isMinAmountEditing')" />
          <div v-show="!(item.isEditing || item.isMinAmountEditing)"
            @dblclick="handleDoubleClickEdit(item, $event, 'isMinAmountEditing')"
            class="w-100% px-4 h-38px rounded hover:border-primary flex align-center">
            {{ item.minAmount || '-' }}
          </div>
        </div>
      </template>

      <!-- Custom Fix Amount slot -->
      <template #fix-amount="{ item, index }">
        <div class="flex-center w-120px" data-focus="true"
          :class="{ 'border-error border-1px hover:border-none': currentTabErrors.fixAmount.includes(index) && !(item.isEditing || item.isFixAmountEditing) }">
          <AppTextField v-show="item.isEditing || item.isFixAmountEditing" placeholder="Fix Amount" min="0"
            type="number" decimal-places="2" v-model="item.fixAmount"
            @update:model-value="(value) => handleFixAmountChange(item, value)"
            :error="currentTabErrors.fixAmount.includes(index)"
            @blur="handleBlur(item, 'isFixAmountEditing')" />
          <div v-show="!(item.isEditing || item.isFixAmountEditing)"
            @dblclick="handleDoubleClickEdit(item, $event, 'isFixAmountEditing')"
            class="w-100% px-4 h-38px rounded hover:border-primary flex align-center">
            {{ item.fixAmount || '-' }}
          </div>
        </div>
      </template>

      <!-- Custom Actions slot -->
      <template #actions="{ item }">
        <div class="flex-center w-70px">
          <IconBtn v-tooltip="'Duplicate'">
            <VIcon icon="mdi-plus-box-multiple" @click="handleInsert(item)"
              :class="{ 'text-#767676!': item.isEditing, 'hover:text-primary': !item.isEditing }" />
          </IconBtn>
        </div>
      </template>
    </PaymentChannelTable>
    <div class="flex justify-center items-center h-52px">
      <VBtn variant="outlined" class="w-110px" :disabled="paymentChannelItems.length === 0" @click="handleAdd">ADD
      </VBtn>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.v-table__wrapper) {
  thead {
    .v-data-table-column--fixed {
      background-color: #000000 !important;
    }
  }

  tbody {
    background-color: #1e1e1e !important;

    .v-data-table__tr:hover {
      background-color: #131212 !important;

      .v-data-table-column--fixed {
        background-color: #131212 !important;
      }
    }

    .v-data-table__tr {
      .v-input__control {
        background: #000000;
      }
    }

    .v-data-table-column--fixed {
      background-color: #1e1e1e !important;
    }
  }
}

.border-error {
  border: 2px solid rgb(var(--v-theme-error)) !important;
  border-radius: 4px;
}

.text-error {
  color: rgb(var(--v-theme-error));
}
</style>