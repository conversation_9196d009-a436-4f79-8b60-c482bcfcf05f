import "@/assets/styles/styles.scss";

import {
  VBtn,
  VCard,
  VCardActions,
  VCardText,
  VCardTitle,
  VDialog,
} from "vuetify/components";
import { createApp, h, ref } from "vue";

import DialogCloseBtn from "@/components/DialogCloseBtn.vue";
import vuetify from "@/plugins/vuetify";

const DialogComponent = {
  props: {
    title: String,
    text: String,
    confirmButtonText: String,
    cancelButtonText: String,
    confirmButtonColor: String,
    cancelButtonColor: String,
    onConfirm: Function,
    onCancel: Function,
    width: Number,
    render: Function,
  },
  emits: ["confirm", "cancel", "update:modelValue"],
  setup(props, { emit }) {
    const show = ref(true);

    const handleConfirm = () => {
      const close = () => (show.value = false);
      props.onConfirm(close);
    };

    const handleCancel = () => {
      show.value = false;
      props.onCancel();
    };

    return () =>
      h(
        VDialog,
        {
          modelValue: show.value,
          width: props.width,
          persistent: true,
          class: "v-dialog-custom",
        },
        {
          default: () =>
            h(
              VCard,
              { class: "v-card-custom" },
              {
                default: () => [
                  // Title Bar
                  h(
                    VCardTitle,
                    { class: "d-flex justify-space-between align-center" },
                    () => [
                      h("span", props.title),
                      // h(DialogCloseBtn, {
                      //   style: {borderRadius: '50%'},
                      //   onClick: handleCancel,
                      // }),
                    ]
                  ),
                  // Content Area
                  h(VCardText, null, () => props.text),
                  //  custom render area
                  props.render
                    ? props.render({ close: () => (show.value = false) })
                    : null,
                  // Button Area
                  h(VCardActions, { class: "justify-end" }, () => [
                    h(
                      VBtn,
                      {
                        variant: "outlined",
                        color: props.cancelButtonColor || "secondary",
                        onClick: handleCancel,
                        style: { minWidth: "100px" },
                      },
                      () => props.cancelButtonText
                    ),
                    h(
                      VBtn,
                      {
                        color: props.confirmButtonColor || "primary",
                        variant: "flat",
                        onClick: handleConfirm,
                        style: { color: "#000000", minWidth: "120px" },
                      },
                      () => props.confirmButtonText
                    ),
                  ]),
                ],
              }
            ),
        }
      );
  },
};

export const confirmDialog = ({
  title = "Confirm",
  text = "",
  confirmButtonText = "Confirm",
  confirmButtonColor = "primary",
  cancelButtonText = "Cancel",
  cancelButtonColor = "secondary",
  onConfirm = () => {},
  onCancel = () => {},
  width = 400,
  render = null,
}) => {
  return new Promise((resolve) => {
    const container = document.createElement("div");
    document.body.appendChild(container);

    const app = createApp(DialogComponent, {
      title,
      text,
      confirmButtonText,
      confirmButtonColor,
      cancelButtonText,
      cancelButtonColor,
      width,
      onConfirm,
      onCancel,
      render,
    });

    app.use(vuetify);
    app.mount(container);
  });
};
