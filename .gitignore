# Vite cache
.vite
.custom-cache-dir

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/*.code-snippets
!.vscode/tours
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.yarn


# Ignore MSW script
public/mockServiceWorker.js

# Docker
docker-compose.yml

# Env files
.env*
!.env.dev
!.env.prod
!.env.qa
!.env.sandbox

# test/api.rest
test/api.rest

.cursor

