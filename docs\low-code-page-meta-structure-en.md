# Low-Code Page Metadata Structure Analysis

This document provides an analysis of the JSON structure used to define dynamic pages in the low-code engine. We will use the `user-list-page` (`src/pages/low-code/engine/mockPageMeta.json`) as an example to break down each part of the structure.

## 1. Page Metadata

This section defines the fundamental properties of the page.

-   `id`: A unique identifier for the page (`user-list-page`).
-   `name`: The name of the page, often used in menus or titles (`User Management`).
-   `path`: The URL path for the page (`/user/list`).
-   `title`: The title displayed in the browser tab (`User List`).
-   `layout`: The base layout template to use for the page (`default`).

**Example:**
```json
{
  "id": "user-list-page",
  "name": "User Management",
  "path": "/user/list",
  "title": "User List",
  "layout": "default"
}
```

## 2. Page Lifecycle

The `lifecycle` object specifies actions to be executed at different points in the page's lifecycle.

-   `onMounted`: An array of actions to run when the page component is mounted. This is typically used for initializing data.

**Example:**
```json
"lifecycle": {
  "onMounted": [
    { "action": "initData" },
    { "action": "handleSearch" }
  ]
}
```
In this example, `initData` and `handleSearch` methods are called upon page load.

## 3. Page Data (`data`)

The `data` object holds the state of the page. It's similar to the `data` property in a Vue component. All properties defined here are reactive and can be bound to UI components.

Key properties in the example:
-   `searchQuery`: An object holding the values of search filters.
-   `isLoading`: A boolean to indicate if data is being fetched (e.g., for showing a loading spinner).
-   `usersData`: Holds the list of users fetched from the API.
-   `headers`: Defines the columns for the data table.
-   `paginationOptions`, `itemsPerPage`, `page`: State for pagination.

**Example:**
```json
"data": {
  "searchQuery": {
    "email": "",
    "roleIdList": [],
    "userGroupIdList": [],
    "status": null,
    // ...
  },
  "isLoading": false,
  "accounts": [],
  "totalUsers": 0,
  "headers": [
    { "title": "Email", "key": "email" },
    { "title": "Name", "key": "name" },
    // ...
  ]
}
```

## 4. UI Components (`components`)

This is an array of objects that defines the UI of the page. It's a declarative representation of the component tree. Each object has:

-   `type`: The component to be rendered (e.g., `VCard`, `VDataTableServer`, `AppTextField`).
-   `props`: The properties (props) to pass to the component. Values can be static or bound to the `data` object using `{{...}}` syntax.
-   `model`: Binds the component's value to a property in the `data` object (e.g., `searchQuery.email`).
-   `events`: Defines event listeners for the component. The value is an array of actions to execute.
-   `children`: An array of child component objects.
-   `slots`: Defines named slots for components like `VDataTableServer`.
-   `v-if`: for conditional rendering, bound to a data property.
-   `permission`: for role-based access control.
-  `text`: for displaying text.

**Example (A Text Field):**
```json
{
  "type": "AppTextField",
  "model": "searchQuery.email",
  "props": {
    "placeholder": "Search Email",
    "clearable": true
  },
  "events": {
    "update:modelValue": [{ "action": "handleSearch" }]
  }
}
```

**Example (Data Table with a custom slot):**
```json
{
  "type": "VDataTableServer",
  "model": "selectedIds",
  "props": {
    "items": "{{accounts}}",
    // ...
  },
  "events": {
    "update:sort-by": [{ "action": "handleSortChange", "params": ["options"] }]
  },
  "slots": {
    "item.email": {
      "type": "AppText",
      "props": { "text": "{{item.accountEmail}}", "auth": ["View", "UserList"] },
      "events": { "click": [{ "action": "viewUser", "params": ["item"] }] }
    }
  }
}
```

## 5. API Definitions (`apis`)

This array defines the API endpoints the page can call. Each API object has:

-   `id`: A unique ID to reference this API call in methods.
-   `type`: The type of API (e.g., `http`).
-   `method`: HTTP method (`GET`, `POST`, `PUT`).
-   `url`: The API endpoint URL.
-   `params`: Data to be sent with the request. Can be bound to the `data` object.
-   `urlParams`, `bodyParams`: Specifies which data properties should be sent as URL or body parameters.

**Example:**
```json
{
  "id": "user-list",
  "type": "http",
  "method": "POST",
  "url": "/api/admin-api/v1/account/page",
  "params": "{{getQueryParams(searchQuery)}}"
}
```

## 6. Methods (`methods`)

This object contains all the functions and logic for the page. These methods can be triggered by `lifecycle` events or component `events`.

-   Each key is the method name.
-   `type`: The type of method, which defines its behavior.
    -   `custom`: Executes a custom JavaScript string. `this` refers to the page context, providing access to `data`, `apiManager`, etc.
    -   `navigate`: Navigates to a different route.
    -   `confirm`: Shows a confirmation dialog before executing other actions.
    -   `callApi`: A declarative way to call an API defined in the `apis` section.
    -   `message`: Shows a notification/toast message.

**Example (Custom script):**
```json
"handleSearch": {
  "type": "custom",
  "script": "this.setData('page', 1); /* ... more logic ... */ this.apiManager.callApi('user-list', ...).then(...);"
}
```
The script calls the `user-list` API and updates the page's data with the result.

**Example (Confirmation dialog):**
```json
"batchDisable": {
  "type": "confirm",
  "title": "Confirm Disable Users",
  "text": "Are you sure you want to disable the selected user(s)?",
  "onConfirm": [
    {
      "action": "callApi",
      "apiId": "user-batch-status",
      "params": { "status": 0, "ids": "{{selectedRows.map(item => ({id: item.accountId}))}}" },
      "onSuccess": [
        { "action": "message", "messageType": "success", "text": "Users disabled successfully" },
        { "action": "handleSearch" }
      ]
    }
  ]
}
```
This shows a confirmation dialog. If confirmed, it calls the `user-batch-status` API and then shows a success message and refreshes the list.

## 7. Renderer Implementation Details

This section explains the internal workings of the low-code renderer's core files and workflow.

### Core Files

-   **`index.js`**: The entry point of the renderer. It initializes the page context, handles lifecycle hooks (`onMounted`), and starts the rendering process by iterating through the root components in `meta.components`.
-   **`renderComponent.js`**: The heart of the renderer. This file is responsible for converting a single component's JSON metadata into a Vue Virtual Node (VNode).
-   **`context.js`**: Creates and manages the shared execution context for the entire page, which includes data, methods, and utility functions.
-   **`utils.js`**: Contains helper functions, such as `resolveExpression` for evaluating data-bound expressions (e.g., `{{some.data}}`) and `getComponentByName` for dynamically resolving component implementations.

### Rendering Workflow

The rendering process can be summarized in the following steps:

1.  **Context Creation (`context.js`)**:
    -   When `render()` is called in `index.js`, it first creates a unified context.
    -   This context object aggregates the page's reactive `data`, transforms `methods` from JSON into executable functions (using `new Function()` for custom scripts), and bundles utility managers like `apiManager` and `dataManager`.
    -   Crucially, it returns a `Proxy` object. This allows transparent access to both data and methods via `this` within any method script (e.g., `this.isLoading` accesses `data.isLoading`, and `this.handleSearch()` calls the corresponding method).

2.  **Component Rendering (`renderComponent.js`)**:
    -   `index.js` iterates through the top-level components in `meta.components` and calls `renderComponent` for each one.
    -   For each component metadata, `renderComponent` performs the following actions:
        -   **Handles `v-if`**: It first evaluates the `v-if` expression. If the result is false, it returns `null` and stops rendering this component and its children.
        -   **Resolves Props**: It iterates over the `props` object. For any value that is an expression (e.g., `{{some.data}}`), it uses `resolveExpression` to get the actual value from the context.
        -   **Handles `v-model`**: It sets up two-way data binding by getting the value from the context and creating an `onUpdate:modelValue` event handler to update the data.
        -   **Handles Events**: It maps event metadata (e.g., `click`) to Vue's `on[EventName]` listeners. When an event fires, the corresponding action from the page's `methods` is executed.
        -   **Renders Children & Slots**:
            -   For `children`, it recursively calls `renderComponent` for each child.
            -   For named `slots`, it creates a function that, when executed, will render the slot's content. This function receives `slotProps` (data passed up from the child component) and merges them into a new context, making them available inside the slot.
        -   **Creates a VNode**: Finally, it uses Vue's `h()` function to create the VNode, passing in the resolved component, props, events, and children/slots.

3.  **Lifecycle Hooks (`index.js`)**:
    -   After the initial setup but before the final render, the `onMounted` lifecycle hook is processed. It executes the defined actions (like fetching initial data) once the component tree is mounted to the DOM.

This entire process is declarative. The JSON metadata describes "what" to render, and the renderer takes care of "how" to render it, creating a fully reactive and interactive Vue component from a static data structure. 