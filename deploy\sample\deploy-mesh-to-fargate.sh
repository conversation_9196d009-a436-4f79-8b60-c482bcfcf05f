#!/usr/bin/env bash

declare -x SERVICE_NAME=paychn-template-fg
declare -xr BUILD=FARGA<PERSON>
declare -x DUAL_LOADBALANCER
echo BUILD $BUILD

declare -xr ROUTE_RECORD=lanpaychn
declare -x CONDITIONER_LISTEN_RULE
declare -xr ROUTE_RECORD_WAN=paychn
declare -x CONDITIONER_LISTEN_RULE_WAN
declare -xu TARGET_GROUP_NAME
declare -xu TARGET_GROUP_NAME_WAN
declare -xr USE_ROUTE_53=0
declare -x TAGS
declare -x REPOSITORY_NAME
declare -x HEALTH_CHECK_PATH
declare -xu LOGNAME
declare -x LOGSTREAM

deploy_override()
{
    CONDITIONER_LISTEN_RULE="/template/*"
    CONDITIONER_LISTEN_RULE_WAN="/template/return/*"
    DUAL_LOADBALANCER=1
    LOGNAME="${ENV}-LG-PAYCHN-MESH"
    LOGSTREAM=template
    HEALTH_CHECK_PATH="/hc"
    TARGET_GROUP_NAME="$DEPLOY_ENV"-TG-LAN-"$SERVICE_NAME"
    TARGET_GROUP_NAME_WAN="$DEPLOY_ENV"-TG-WAN-"$SERVICE_NAME"
    REPOSITORY_NAME="paychn-template"
    TAGS='[{"key":"Project","value":"Gold-Pay"},{"key":"Framework","value":"Net6"}]'
}
