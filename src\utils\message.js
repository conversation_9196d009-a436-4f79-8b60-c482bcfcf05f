import { h, createApp, ref } from "vue";
import { VSnackbar, VBtn } from "vuetify/components";
import vuetify from "@/plugins/vuetify";

const MessageComponent = {
  props: {
    message: String,
    type: String,
    duration: Number,
    onClose: Function,
  },
  setup(props) {
    const show = ref(true);
    const timeout = ref(props.duration);

    const getMessageStyle = () => {
      let backgroundColor;
      switch (props.type) {
        case "success":
          backgroundColor = "rgb(var(--v-theme-primary))";
          break;
        case "error":
          backgroundColor = "#FF5252";
          break;
        case "warning":
          backgroundColor = "#FFC107";
          break;
        case "info":
        default:
          backgroundColor = "#EEEEEE";
          break;
      }
      return {
        backgroundColor,
        color: "#000000",
        fontWeight: 500,
        padding: "12px 16px",
        borderRadius: "2px",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
      };
    };

    const handleClose = () => {
      show.value = false;
      setTimeout(() => {
        props.onClose?.();
      }, 300);
    };

    return () =>
      h(
        VSnackbar,
        {
          modelValue: show.value,
          timeout: timeout.value,
          location: "top",
          class: "message-snackbar",
          color: "#000000",
          elevation: 0,
          "onUpdate:modelValue": handleClose,
        },
        {
          default: () => [
            h("div", { 
              style: getMessageStyle(),
            }, [
              h("span", props.message),
              h(VBtn, {
                icon: "tabler-x",
                color: "rgba(0, 0, 0, 0.87)",
                onClick: handleClose,
                variant: "text",
                size: "x-small",
                class: "close-btn",
              }),
            ]),
          ],
        }
      );
  },
};

// 修改样式
const style = document.createElement("style");
style.textContent = `
.v-snackbar__content {
  padding: 0 !important;
}
.message-snackbar {
  margin-top: 24px !important;
}

.close-btn {
  background-color: rgba(0, 0, 0, 0.15) !important;
  border-radius: 50% !important;
  min-width: 20px !important;
  width: 20px !important;
  height: 20px !important;
  color: rgba(0, 0, 0, 0.87) !important;
}

.close-btn:hover {
  background-color: rgba(0, 0, 0, 0.25) !important;
}
`;
document.head.appendChild(style);

const createMessage =
  (type) =>
  (message, duration = 3000) => {
    const container = document.createElement("div");
    document.body.appendChild(container);

    const app = createApp(MessageComponent, {
      message,
      type,
      duration,
      onClose: () => {
        setTimeout(() => {
          app.unmount();
          container.remove();
        }, 300);
      },
    });

    app.use(vuetify);
    app.mount(container);
  };

const message = {
  success: createMessage("success"),
  error: createMessage("error"),
  warning: createMessage("warning"),
  info: createMessage("info"),
};

export default message;
