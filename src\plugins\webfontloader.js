/**
 * plugins/webfontloader.js
 *
 * webfontloader documentation: https://github.com/typekit/webfontloader
 */

import "roboto-fontface/css/roboto/roboto-fontface.css";
import "@/assets/fonts/razerf5/razerf5-fontface.css";

export async function loadFonts() {
  const webFontLoader = await import("webfontloader");
  setTimeout(() => {
    webFontLoader.load({
      google: {
        families: [
          "Public+Sans:300,400,500,600,700&display=swap",
          "Roboto:100,300,400,500,600,700,900&display=swap",
          "RazerF5:600&display=swap",
        ],
      },
      timeout: 2000,
      loading: () => {
        // 字体加载中的处理
      },
      active: () => {
        // 字体加载完成的处理
      },
      inactive: () => {
        // 字体加载失败的处理
      },
    });
  }, 0);
}

export default function () {
  loadFonts();
}
