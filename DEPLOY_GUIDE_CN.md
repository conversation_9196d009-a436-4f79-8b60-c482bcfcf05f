# 前端项目部署全流程指南（基于 rollout-dev.yaml、Dockerfile、nginx.conf、sonar-project.properties）

---

## 一、整体部署流程

1. **代码开发与提交**
   - 开发者完成前端代码开发，通过PR提交到代码仓库（如 Bitbuckit）。

2. **代码质量检测**
   - CI/CD 流水线自动调用 SonarQube，读取 `sonar-project.properties`，分析代码质量。

3. **构建 Docker 镜像**
   - CI/CD 或本地执行 `docker build`，读取 `Dockerfile`：
     - 用 Node 镜像编译前端代码
     - 用 Nginx 镜像托管静态文件
     - 拷贝 `nginx.conf` 作为 Nginx 配置
   - 生成的镜像推送到镜像仓库（AWS）。

4. **K8s 部署上线**
   - 运维或 CI/CD 工具用 `rollout-dev.yaml` 部署到 K8s 集群：
     - 拉取镜像，启动 Pod
     - Nginx 启动，提供 HTTPS 服务
     - K8s 通过 `/healthcheck` 检查服务健康
     - Argo Rollouts 控制升级过程，支持金丝雀发布、回滚

5. **服务对外提供访问**
   - 用户通过域名访问服务，Nginx 负责静态资源和 API 代理。

---

## 二、四个配置文件的作用

### 1. `sonar-project.properties`
- **作用**：配置 SonarQube 代码质量检测。
- **内容**：指定项目标识、编码格式、项目根目录。
- **场景**：CI/CD 流水线中自动检测代码质量，发现潜在 bug 和代码异味。

### 2. `nginx.conf`
- **作用**：定义 Nginx 服务器行为。
- **内容**：
  - 配置 HTTPS 证书
  - 静态资源托管（前端页面）
  - 反向代理 `/api` 到后端服务
  - 健康检查 `/healthcheck`
  - 安全 headers 设置
- **场景**：容器启动时 Nginx 读取此配置，对外提供安全的 Web 服务。

### 3. `Dockerfile`
- **作用**：定义如何构建前端应用的 Docker 镜像。
- **内容**：
  - 用 Node 镜像编译前端代码
  - 用 Nginx 镜像托管编译产物
  - 拷贝 nginx.conf 作为 Nginx 配置
- **场景**：CI/CD 或本地构建镜像，保证环境一致性，便于 K8s 部署。

### 4. `rollout-dev.yaml`
- **作用**：Kubernetes（Argo Rollouts）部署配置，声明如何在 K8s 集群中部署和升级应用。
- **内容**：
  - 金丝雀发布策略（渐进式上线）
  - 镜像地址、资源限制、健康检查、证书挂载等
- **场景**：CI/CD 或运维用 `kubectl apply -f` 部署到 K8s 集群，实现自动化、可回滚的上线。

---



## 三、新手部署新项目操作指引

### 1. 代码质量检测
- 配置好 `sonar-project.properties`
- 要配置`sonar.projectKey`,这是项目的标识符，用于标识项目，不能重复
```
sonar.projectKey=ZGQ-DEPLOY-GOLD-ADMIN-PORTAL-V2
```
- CI/CD 执行 SonarQube 扫描

### 2. 构建镜像
- 确认 `Dockerfile`、`nginx.conf` 已按项目需求配置好
- nginx.conf 中的`location /api` 需要修改为项目实际 api 路径
``` conf
    location /api {
        proxy_pass https://console-v2.zgold-dev.razer.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $http_x_forwarded_proto;
    }
```

### 3. 配置 K8s 部署文件
- 复制并修改 `rollout-dev.yaml`：
- 生产环境要注意修改的配置:
``` yaml
spec: 
  replicas: 1 # 副本数 生产环境改为2
  revisionHistoryLimit: 3
  strategy:
    canary:
      steps:
        # - pause: {}  # 在开发，测试环境，不需要暂停， 在生产环境需要暂停
        - setWeight: 100
        # Refer to https://argo-rollouts.readthedocs.io/en/stable/features/canary/
        # Example:
        # - pause: {}
        # - setWeight: 33
        # - pause: { duration: 10s }
```

## 四、常见问题与排查建议

- **API 代理异常**：检查 `nginx.conf` 中 `/api` 代理配置
- **金丝雀发布策略不生效**：检查 `rollout-dev.yaml` 中 canary 配置
- **代码回滚**：用 Argo Rollouts 或 kubectl 回滚到旧版本

---
