<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { PAGINATION_OPTIONS as paginationOptions } from "@/utils/constants";
import { isTextOverflow } from "@/utils/helpers";
import { hasPermission } from "@/directives/can";
// 👉 Store
const searchQuery = ref({
  roleName: "",
  userGroupName: "",
  status: null,
  userGroupIdList: null,
  page: 1,
  size: 10,
  sortBy: null,
  orderBy: null,
});

const router = useRouter();
const { userGroupList } = useGroup();

// Data table options
const itemsPerPage = ref(10);
const page = ref(1);
const selectedRows = ref([]);

// Headers
const headers = [
  {
    title: "No",
    key: "no",
  },
  {
    title: "Role",
    key: "name",
  },
  {
    title: "User Group",
    key: "userGroupName",
  },
  {
    title: "Description",
    key: "description",
    sortable: false,
  },
  {
    title: "Status",
    key: "status",
    sortable: false,
  },

  {
    title: "Actions",
    key: "actions",
    sortable: false,
  },
];

const isLoading = ref(false);

const rolesData = ref(null);
// 👉 get query params
const getQueryParams = () => {
  const params = {};
  params.page = searchQuery.value.page;
  params.size = searchQuery.value.size;
  params.userGroupName = searchQuery.value.userGroupName;
  params.roleName = searchQuery.value.name;
  params.userGroupIdList = searchQuery.value.userGroupIdList;

  if (searchQuery.value.sortBy && searchQuery.value.orderBy) {
    params.orderItemList = [
      {
        column: searchQuery.value.sortBy || "",
        asc: searchQuery.value.orderBy === "asc",
      },
    ];
  } else {
    params.orderItemList = null;
  }

  if (searchQuery.value.status === 0 || searchQuery.value.status === 1) {
    params.statusId = searchQuery.value.status;
  }
  return params;
};
const loadData = async () => {
  isLoading.value = true;
  try {
    const params = getQueryParams();
    const res = await $api("/api/admin-api/v1/role/role-pageable", {
      method: "POST",
      body: params,
    });
    rolesData.value = res.data;
  } catch (error) {
    console.error("Error loading data:", error);
  } finally {
    isLoading.value = false;
  }
};

loadData();

const handleSearch = () => {
  page.value = 1;
  searchQuery.value.page = 1;
  loadData();
};

const handleSortChange = (options) => {
  const sortBy = options[0]?.key;
  const orderBy = options[0]?.order;
  searchQuery.value.sortBy = sortBy;
  searchQuery.value.orderBy = orderBy;
  page.value = 1;
  searchQuery.value.page = 1;
  loadData();
};

// computed
const roles = computed(
  () =>
    rolesData.value?.records.map((item, index) => ({
      ...item,
      no: index + 1,
    })) || []
);
const totalRoles = computed(() => rolesData.value?.total || 0);

// 👉 search filters
const status = [
  {
    title: "Enable",
    value: 1,
  },
  {
    title: "Disable",
    value: 0,
  },
];


const addRole = () => {
  router.push("/user/role/add");
};

const editRole = async (id) => {
  router.push(`/user/role/edit?id=${id}`);
};

const handlePageChange = (newPage) => {
  page.value = newPage;
  searchQuery.value.page = newPage;
  loadData();
};

const handleItemsPerPageChange = (newItemsPerPage) => {
  newItemsPerPage = parseInt(newItemsPerPage, 10);
  itemsPerPage.value = newItemsPerPage;
  searchQuery.value.size = newItemsPerPage;
  page.value = 1;
  searchQuery.value.page = 1;
  loadData();
};

// 👉 toggle status
const toggleStatusId = async (item, e) => {
  e.preventDefault();

  if (!hasPermission('Edit', 'UserRole')) return
  
  confirmDialog({
    title: `${item.statusId === 1 ? 'Disable Role?' : 'Enable Role?'}`,
    text: `${item.statusId === 1 ? 'Once disabled, this role and its permissions cannot be assigned to users.' : 'Once enabled, this role and its permissions can be assigned to users.'}`,
    confirmButtonText: item.statusId === 1  ? "DISABLE" : "ENABLE",
    confirmButtonColor: item.statusId === 1 ? "error" : "primary",
    cancelButtonText: 'CANCEL',
    async onConfirm(close) {
      const params = { id: item.id, name: item.name, statusId: item.statusId === 1 ? 0 : 1 }
      await $api(`/api/admin-api/v1/role/change-status`, {
        method: 'PUT',
        body: params
      })
      message.success('Role status updated successfully')
      close()
      loadData()
    }
  })

}
</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            Roles
            <VSpacer />

            <div class="d-flex align-center gap-4">
              <!-- 👉 Add role button -->
              <VBtn @click="addRole" v-can="['Add', 'UserRole']"> CREATE </VBtn>
            </div>
          </div>
        </VCardTitle>
      </VCardItem>

      <VCardText class="px-0">
        <VRow>
          <!-- 👉 Search -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField
              v-model="searchQuery.name"
              placeholder="Search Role"
              clearable
              @update:model-value="handleSearch"
            />
          </VCol>
          <!-- 👉 Select Group -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete
              v-model="searchQuery.userGroupIdList"
              placeholder="Select User Group"
              :items="userGroupList"
              item-value="id"
              clearable
              clear-icon="tabler-x"
              filterable
              multiple
              @update:model-value="handleSearch"
            >
              <template #selection="{ item, index }">
                <span v-if="index === 0">
                  {{ item.title }}
                </span>
                <span
                  v-else-if="index === 1"
                  class="text-caption align-self-center text-primary font-weight-medium ml-2"
                >
                  (+{{ searchQuery.userGroupIdList.length - 1 }})
                </span>
              </template>
            </AppAutocomplete>
          </VCol>
          <!-- 👉 Select Status -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppSelect
              v-model="searchQuery.status"
              placeholder="Select Status"
              :items="status"
              clearable
              clear-icon="tabler-x"
              @update:model-value="handleSearch"
            />
          </VCol>
        </VRow>
      </VCardText>

      <VDivider />

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer
        v-model:model-value="selectedRows"
        :items="roles"
        item-value="id"
        :items-length="totalRoles"
        :headers="headers"
        :loading="isLoading"
        class="text-no-wrap"
        @update:sort-by="handleSortChange"
      >
        <!-- Description -->
        <template #[`item.description`]="{ item }">
          <div v-if="isTextOverflow(item.description, 500)" class="w-[500px]">
            <VTooltip>
              <template #activator="{ props }">
                <div class="text-truncate" v-bind="props">
                  {{ item.description }}
                </div>
              </template>
              <div>
                {{ item.description }}
              </div>
            </VTooltip>
          </div>
          <div v-else class="text-truncate w-500">
            {{ item.description }}
          </div>
        </template>

        <!-- Status -->
        <template #[`item.status`]="{ item }">
          <VSwitch
            :model-value="item.statusId"
            @click.stop="toggleStatusId(item, $event)"
            :false-value="0"
            :true-value="1"
            :class="hasPermission('Edit', 'UserRole') ? 'cursor-pointer' : 'opacity-50'"
          ></VSwitch>
        </template>

        <!-- Actions -->
        <template #[`item.actions`]="{ item }">
          <IconBtn
            @click="editRole(item.id)"
            v-tooltip="'Edit User Role'"
            v-can="['Edit', 'UserRole']"
          >
            <VIcon icon="mdi-pencil" class="hover:text-primary" />
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination
            :page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalRoles"
            @update:page="handlePageChange"
          >
            <div class="d-flex gap-3">
              <AppSelect
                :model-value="itemsPerPage"
                :items="paginationOptions"
                @update:model-value="handleItemsPerPageChange"
              />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
  </section>
</template>
