import auditLog from "./audit-log";
import developerControlPanel from "@/config/navigation/developer-control-panel.js";
import paymentPartnerCommission from "./payment-partner-commission";
import pinInventory from "./pin-inventory";
import { useAuthStore } from "@/stores/useAuthStore";
import user from "./user";

const authStore = useAuthStore();

const useRemoteMenu = import.meta.env.VITE_USE_REMOTE_MENU === "true";

let routes = useRemoteMenu
  ? []
  : [
      ...user,
      ...auditLog,
      ...paymentPartnerCommission,
      ...pinInventory,
      ...developerControlPanel,
    ];

if (useRemoteMenu) {
  const menus = computed(() => authStore.menuTree);
  routes = menus.value || [];
}

export default routes;
