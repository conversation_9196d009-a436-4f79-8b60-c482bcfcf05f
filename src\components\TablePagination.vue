<script setup>
import { watch, computed } from 'vue'
import { paginationMeta } from '@/utils/paginationMeta'

const props = defineProps({
  page: {
    type: Number,
    required: true,
  },
  itemsPerPage: {
    type: Number,
    required: true,
  },
  totalItems: {
    type: Number,
    required: true,
  },
})

const emit = defineEmits(['update:page'])

const updatePage = value => {
  console.log(`[TablePagination] updatePage called with:`, value, 'current page:', props.page)
  emit('update:page', value)
}

// 监听 page 变化
watch(() => props.page, (newPage, oldPage) => {
  console.log(`[TablePagination] page changed from ${oldPage} to ${newPage}`)
}, { immediate: true })
</script>

<template>
  <div>
    <VDivider />

    <div class="d-flex align-center justify-sm-end justify-center flex-wrap gap-3 px-6 py-3">
      <p class="text-disabled mb-0 d-flex align-center gap-2">
        <span class="text-disabled">Items per page</span>
        <slot>
          {{ paginationMeta({ page, itemsPerPage }, totalItems) }}
          </slot>
      </p>

      <VPagination
        :key="`pagination-${page}-${totalItems}`"
        :model-value="page"
        active-color="primary"
        :length="Math.ceil(totalItems / itemsPerPage)"
        :total-visible="$vuetify.display.xs ? 1 : Math.min(Math.ceil(totalItems / itemsPerPage), 5)"
        @update:model-value="updatePage"
      />
      
    </div>
  </div>
</template>
