<template>
  <LowCodeLayout navigation-value="page-builder">
    <div class="editor-container">
      <!-- 左侧组件库 -->
      <ComponentLibrary :selected-scenario="selectedScenario" :scenarios="scenarios"
        @scenario-change="handleScenarioChange" @component-drag-start="handleDragStart"
        @generate-page="handleGeneratePage" />

      <!-- 中间画布区域 -->
      <CanvasArea :selected-scenario="selectedScenario" :scenarios="scenarios" :selected-page-type="selectedPageType"
        :has-content="hasContent" :canvas-content="canvasContent" :page-config="pageConfig"
        @page-change="handlePageChange" @save-page="savePage" @preview-page="previewPage" @publish-page="publishPage"
        @canvas-drop="handleDrop" @component-select="handleComponentSelect" @component-update="handleComponentUpdate"
        @component-delete="handleComponentDelete" @generate-page="handleGeneratePage" />

      <!-- 右侧属性面板 -->
      <PropertiesPanel 
        :property-level="propertyLevel" 
        :page-config="pageConfig" 
        :table-config="tableConfig"
        :selected-component="selectedComponent"
        :open-panels="openPanels" 
        :component-panels="componentPanels" 
        @update:property-level="propertyLevel = $event"
        @update:page-config="updatePageConfig" 
        @update:table-config="updateTableConfig"
        @update:open-panels="openPanels = $event" 
        @update:component-panels="componentPanels = $event"
        @reset-properties="resetProperties" 
        @toggle-property-search="togglePropertySearch"
        @show-data-config-modal="showDataConfigModal" 
        @show-api-config-modal="showApiConfigModal"
        @show-add-column-modal="showAddColumnModal" 
        @edit-data-item="editDataItem" 
        @edit-api-item="editApiItem"
        @delete-api-item="deleteApiItem" 
        @remove-column="removeColumn" 
        @component-update="handleComponentUpdate" />
    </div>
  </LowCodeLayout>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import LowCodeLayout from '@/business/low-code-engine/layout/LowCodeLayout.vue'
import ComponentLibrary from '@/business/low-code-engine/editor/ComponentLibrary.vue'
import CanvasArea from '@/business/low-code-engine/editor/CanvasArea.vue'
import PropertiesPanel from '@/business/low-code-engine/editor/PropertiesPanel.vue'
import { usePageGeneration } from '@/business/low-code-engine/hooks/usePageGeneration'
import { useScenarioConfig } from '@/business/low-code-engine/hooks/useScenarioConfig'
import { useLowCodeAPI } from '@/business/low-code-engine/api/useLowCodeAPI'
import { generatePagesFromBackendMeta } from '@/business/low-code-engine/meta-manager/pageGenerator'
import { useTablePagination } from '@/business/low-code-engine/hooks/usePagination'

definePage({
  alias: '/low-code-engine/editor',
  name: 'low-code-engine-editor',
  meta: {
    layout: 'blank',
    public: true,
  },
})

const emit = defineEmits(['update-canvas-components'])

// ===== Composables =====
const {
  scenarioConfigs,
} = useScenarioConfig()
const { generatePageContent, generatePageConfig } = usePageGeneration()
const { getMetadata } = useLowCodeAPI()

// ===== 状态管理 =====
const selectedScenario = ref(null)
const selectedPageType = ref(null)
const propertyLevel = ref('page')
const hasContent = ref(false)
const canvasContent = ref('')
const scenarios = ref([])

// 展开面板状态
const openPanels = ref(['general'])
const componentPanels = ref(['table'])

// 场景数据
watchEffect(() => {
  // Use watchEffect to reactively update baseScenarios when scenarioConfigs changes.
  // This handles the initial asynchronous loading correctly.
  if (scenarioConfigs.value) {
    scenarios.value = Array.from(scenarioConfigs.value.values()).map(item => {
      return {
        ...item,
        title: item.scenario,
        value: item.scenario
      }
    })
    console.log(scenarios.value)
  }
});



// 🔥 修复：页面配置初始化为空对象，避免结构干扰
const pageConfig = ref({})

// 选中的组件状态
const selectedComponent = ref(null)

// 表格配置
const tableConfig = ref({
  bulkSelection: true,
  sorting: true,
  columns: []
})

// ===== 核心方法 =====

/**
 * 场景变更处理
 */
function handleScenarioChange(scenarioId) {
  selectedScenario.value = scenarioId

  if (scenarioId) {
    const scenario = scenarios.value.find(s => s.id === scenarioId)
    if (scenario) {
      // 重置页面内容
      hasContent.value = false
      canvasContent.value = ''
      selectedPageType.value = ''

      // 生成默认表格配置
      Object.assign(tableConfig.value, generateTableConfig(scenario))
    }
  }
}

/**
 * 页面类型变更处理
 */
async function handlePageChange(pageType) {
  selectedPageType.value = pageType

  if (!pageType || !selectedScenario.value) {
    console.warn('Editor: pageType 或 selectedScenario 为空', { pageType, selectedScenario: selectedScenario.value })
    return
  }

  console.log('Editor: 开始处理页面变更', { pageType, scenario: selectedScenario.value })

  // 获取当前场景
  const scenario = scenarios.value.find(s => s.scenario === selectedScenario.value)
  console.log('Editor: 找到场景配置:', scenario)

  try {
    // 获取当前场景下的元数据
    console.log('Editor: 开始获取元数据...')
    const metaResponse = await getMetadata({ scenario: selectedScenario.value })
    console.log('Editor: 元数据API响应:', metaResponse)
    const meta = metaResponse.data
    console.log('Editor: 解析出的元数据:', meta)

    if (!meta || !meta.fields || meta.fields.length === 0) {
      console.error('Editor: 元数据为空或没有字段定义')
      return
    }

    // 生成基础页面配置
    console.log('Editor: 开始生成页面配置...')
    const pages = generatePagesFromBackendMeta(meta, useTablePagination())
    console.log('Editor: 生成的所有页面配置:', pages)

    // 取出对应类型的页面配置
    const basePageConfig = pages[pageType]
    console.log('Editor: 目标页面类型配置:', pageType, basePageConfig)

    if (!basePageConfig) {
      console.error('Editor: 没有找到对应页面类型的配置', pageType)
      return
    }

    // 生成最终可编辑的页面配置
    const newPageConfig = generatePageConfig(pageType, basePageConfig)
    console.log('Editor: 生成的最终页面配置:', newPageConfig)
    
    // 🔥 修复：强制重新赋值以确保响应性
    pageConfig.value = { ...newPageConfig }
    console.log('📝 Editor: pageConfig.value 已更新!')
    
    // 🔥 紧急调试：检查pageConfig.data中的headers
    console.log('📝 Editor: pageConfig.value.data.headers长度:', pageConfig.value.data?.headers?.length || 0)
    console.log('📝 Editor: pageConfig.value.data.headers详情:', pageConfig.value.data?.headers)
    
    // 🔥 强制触发响应性，确保CanvasArea能收到更新
    nextTick(() => {
      console.log('📝 Editor: nextTick后pageConfig状态:', {
        hasData: !!pageConfig.value.data,
        hasHeaders: !!pageConfig.value.data?.headers,
        headersLength: pageConfig.value.data?.headers?.length || 0
      })
    })

    // 🔥 修复：生成页面内容并正确设置到画布
    const pageContent = generatePageContent(pageType, basePageConfig)
    console.log('Editor: 生成的页面内容:', pageContent)
    
    // 确保画布有内容显示
    hasContent.value = true
    canvasContent.value = JSON.stringify(pageContent)
    
    console.log('Editor: canvasContent已设置:', canvasContent.value)
    console.log('Editor: pageConfig.value:', pageConfig.value)

    // 将生成的组件配置传递给画布
    if (pageContent.components && pageContent.components.length > 0) {
      console.log('Editor: 发现组件配置:', pageContent.components.length, '个组件')
      console.log('Editor: 组件详情:', pageContent.components)
    } else {
      console.warn('Editor: 没有找到组件配置，但仍继续处理')
    }

    console.log('Editor: 页面处理完成')
  } catch (error) {
    console.error('Editor: 页面变更处理失败:', error)
  }
}

/**
 * 智能生成页面
 */
function handleGeneratePage(pageType) {
  if (!selectedScenario.value) {
    message.warning('Please select a scenario first!')
    return
  }

  selectedPageType.value = pageType
  handlePageChange(pageType)
}

/**
 * 更新页面配置
 */
function updatePageConfig(key, value) {
  pageConfig.value[key] = value
}

/**
 * 更新表格配置
 */
function updateTableConfig(key, value) {
  tableConfig.value[key] = value
}

/**
 * 保存页面
 */
function savePage() {
  console.log('保存页面', pageConfig.value)
}

/**
 * 预览页面
 */
function previewPage() {
  console.log('预览页面')
}

/**
 * 发布页面
 */
function publishPage() {
  console.log('发布页面')
}

/**
 * 重置属性
 */
function resetProperties() {
  if (confirm('确定要重置所有属性到默认值吗？')) {
    const scenario = scenarios.value.find(s => s.id === selectedScenario.value)
    if (scenario && selectedPageType.value) {
      const newPageConfig = generatePageConfig(selectedPageType.value, scenario)
      Object.assign(pageConfig.value, newPageConfig)
      Object.assign(tableConfig.value, generateTableConfig(scenario))
    }
  }
}

/**
 * 切换属性搜索
 */
function togglePropertySearch() {
  console.log('切换属性搜索')
}

/**
 * 拖拽处理
 */
function handleDragStart(event, component) {
  console.log('Editor: 拖拽开始', component)
  event.dataTransfer.setData('component', JSON.stringify(component))
}

function handleDrop(event) {
  console.log('Editor: 接收到拖拽', event)
  // try {
  //   const componentData = JSON.parse(event.dataTransfer.getData('component'))
  //   console.log('Editor: 添加组件到画布:', componentData)
  // } catch (error) {
  //   console.error('Editor: 拖拽处理失败:', error)
  // }
}

/**
 * 组件选择处理
 */
function handleComponentSelect(componentId) {
  console.log('🔥 Editor: 组件选择事件触发, componentId:', componentId)
  console.log('🔥 Editor: 当前pageConfig.components:', pageConfig.value.components)
  
  if (!componentId) {
    // 取消选择
    selectedComponent.value = null
    console.log('🔥 Editor: 取消选择组件')
    return
  }
  
  // 递归查找组件（支持嵌套组件）
  function findComponentById(components, id) {
    console.log('🔍 Editor: findComponentById被调用, components:', components, 'id:', id)
    if (!components) {
      console.log('🔍 Editor: components为null，返回null')
      return null
    }
    
    for (let i = 0; i < components.length; i++) {
      const component = components[i]
      console.log(`🔍 Editor: 检查组件[${i}]:`, component)
      console.log(`🔍 Editor: 组件[${i}].id:`, component.id, '(类型:', typeof component.id, ')')
      console.log(`🔍 Editor: 目标id:`, id, '(类型:', typeof id, ')')
      console.log(`🔍 Editor: 是否相等:`, component.id === id)
      
      if (component.id === id) {
        console.log('✅ Editor: 找到匹配的组件!')
        return component
      }
      if (component.children) {
        console.log(`🔍 Editor: 检查组件[${i}]的children:`, component.children)
        const found = findComponentById(component.children, id)
        if (found) return found
      }
    }
    console.log('❌ Editor: 未找到组件')
    return null
  }
  
  const component = findComponentById(pageConfig.value.components || [], componentId)
  if (component) {
    selectedComponent.value = component
    console.log('✅ Editor: 成功选中组件:', component)
    console.log('✅ Editor: selectedComponent.value已设置:', selectedComponent.value)
  } else {
    selectedComponent.value = null
    console.log('❌ Editor: 未找到组件, componentId:', componentId)
    console.log('❌ Editor: 可用组件列表:', pageConfig.value.components)
  }
}

/**
 * 组件更新处理
 */
function handleComponentUpdate(componentId, updates) {
  console.log('更新组件:', componentId, updates)
  
  // 处理清除选择操作
  if (updates?.action === 'clear-selection') {
    selectedComponent.value = null
    return
  }
  
  if (!componentId || !updates) return
  
  // 递归更新组件
  function updateComponentInTree(components, id, updates) {
    if (!components) return false
    
    for (const component of components) {
      if (component.id === id) {
        // 更新组件数据
        Object.assign(component, updates)
        return true
      }
      if (component.children) {
        if (updateComponentInTree(component.children, id, updates)) {
          return true
        }
      }
    }
    return false
  }
  
  // 更新页面配置中的组件
  const updated = updateComponentInTree(pageConfig.value.components || [], componentId, updates)
  
  if (updated) {
    // 同时更新选中组件的状态
    if (selectedComponent.value && selectedComponent.value.id === componentId) {
      Object.assign(selectedComponent.value, updates)
    }
    console.log('组件更新成功:', componentId, updates)
  } else {
    console.warn('未找到要更新的组件:', componentId)
  }
}

/**
 * 组件删除处理
 */
function handleComponentDelete(componentId) {
  console.log('删除组件:', componentId)
  // 这里可以从页面配置中删除组件
}

/**
 * 数据配置相关
 */
function showDataConfigModal() {
  console.log('显示数据配置模态框')
}

function editDataItem(key) {
  console.log('编辑数据项:', key)
}

/**
 * API配置相关
 */
function showApiConfigModal() {
  console.log('显示API配置模态框')
}

function editApiItem(index) {
  console.log('编辑API项:', index)
}

function deleteApiItem(index) {
  pageConfig.value.apis.splice(index, 1)
}

/**
 * 表格配置相关
 */
function showAddColumnModal() {
  console.log('显示添加列模态框')
}

function removeColumn(index) {
  tableConfig.value.columns.splice(index, 1)
}
</script>

<style scoped>
.editor-container {
  display: flex;
  height: calc(100vh - 68px);
  background: #1a1a1a;
  overflow: hidden; /* 防止主容器滚动 */
}

/* 确保组件库固定宽度 */
.editor-container > :first-child {
  flex-shrink: 0; /* 防止收缩 */
  flex-grow: 0;   /* 防止增长 */
}

/* 确保画布区域占用剩余空间并可滚动 */
.editor-container > :nth-child(2) {
  flex: 1;        /* 占用剩余空间 */
  min-width: 0;   /* 防止flex item变得太宽 */
  overflow: hidden; /* 让内部处理滚动 */
}

/* 确保属性面板固定宽度 */
.editor-container > :last-child {
  flex-shrink: 0; /* 防止收缩 */
  flex-grow: 0;   /* 防止增长 */
}
</style>