import { calculateHeight } from "@/utils/helpers"
export const useHeight = (cardRef) => {
  const height = ref(0)
  const setCardHeight = () => {
    height.value = calculateHeight(cardRef)
  }

  onMounted(() => {
    setCardHeight()
    window.addEventListener('resize', setCardHeight)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', setCardHeight)
  })

  return {
    height,
  }
}