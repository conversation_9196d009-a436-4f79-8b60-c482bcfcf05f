<script setup>
import { ref, computed, watch } from 'vue'
import { debounce } from '@/utils/helpers'
import { useRouter } from 'vue-router'
import { usePin } from '@/business/pin-generator/usePin'
import { PAGINATION_OPTIONS as paginationOptions } from "@/utils/constants";

const { statuses, region, razerGoldPINGroups } = usePin()

// 👉 Store
const searchQuery = ref({
  page: 1,
  size: 10,
})

const router = useRouter()

// Data table options
const itemsPerPage = ref(10)
const page = ref(1)
const selectedRows = ref([])

const updateOptions = options => {
  queryParams.value.sortBy = options.sortBy[0]?.key
  queryParams.value.orderBy = options.sortBy[0]?.order
}

// Headers
const headers = [
  {
    title: 'No',
    key: 'no',
    sortable: false,
  },
  {
    title: 'Request ID',
    key: 'requestId',
    align: 'end',
    sortable: false,
  },
  {
    title: 'Batch Name',
    key: 'batchName',
    sortable: false,
  },
  {
    title: 'Requestor',
    key: 'requestorName',
    sortable: false,
  },
  {
    title: 'Region',
    key: 'region',
    sortable: false,
  },
  {
    title: 'Quantity Of PINs',
    key: 'quantity',
    align: 'end',
    sortable: false,
  },
  {
    title: 'SFTP File',
    key: 'needSFTP',
    align: 'center',
    sortable: false,
  },
  {
    title: 'Requested On',
    key: 'createdDateTime',
    sortable: false,
  },
  {
    title: 'Approved On',
    key: 'approvedDateTime',
    sortable: false,
  },
  {
    title: 'Status',
    key: 'status',
    sortable: false,
  },
  {
    title: 'Actions',
    key: 'actions',
    sortable: false,
  }
]

const queryParams = ref({
  page: 1,
  size: 10,
  sortBy: null,
  orderBy: null,
  roleName: '',
  userGroupName: null
})

const isLoading = ref(false)

const pinsData = ref([])
// 👉 get query params
const getQueryParams = () => {
  const params = {}
  params.page = searchQuery.value.page
  params.size = searchQuery.value.size
  params.batchName = searchQuery.value.batchName
  params.statuses = searchQuery.value.statuses
  params.requestId = searchQuery.value.requestId
  params.regions = searchQuery.value.regions
  params.quantity = Number(searchQuery.value.quantity)
  params.needSFTP = searchQuery.value.needSFTP

  if (searchQuery.value.requestedOn) {
    const [start, end] = searchQuery.value.requestedOn.split(' to ')
    if (start) {
      params.createdDateTimeStart = dateUtil.format(start, 'YYYY-MM-DDTHH:mm:ss.SSSZ')
      params.createdDateTimeEnd = dateUtil.format(start, 'YYYY-MM-DDT23:59:59.SSSZ')
    }
    if (end) {
      params.createdDateTimeEnd = dateUtil.format(end, 'YYYY-MM-DDT23:59:59.SSSZ')
    }
  }
  if (searchQuery.value.approvedOn) {
    const [start, end] = searchQuery.value.approvedOn.split(' to ')
    if (start) {
      params.approvedDateTimeStart = dateUtil.format(start, 'YYYY-MM-DDTHH:mm:ss.SSSZ')
      params.approvedDateTimeEnd = dateUtil.format(start, 'YYYY-MM-DDT23:59:59.SSSZ')
    }
    if (end) {
      params.approvedDateTimeEnd = dateUtil.format(end, 'YYYY-MM-DDT23:59:59.SSSZ')
    }
  }
  if (queryParams.value.sortBy && queryParams.value.orderBy) {
    params.orderItemList = [{
      column: queryParams.value.sortBy || '',
      asc: queryParams.value.orderBy === 'asc',
    }]
  } else {
    params.orderItemList = null
  }

  return params
}
const loadData = async () => {
  isLoading.value = true
  try {
    const params = getQueryParams()
    const res = await $api('/api/admin-api/v1/pin-generate-request/page-query', {
      method: 'POST',
      body: params
    })
    pinsData.value = res.data
  } catch (error) {
    console.error("Error loading data:", error)
  } finally {
    isLoading.value = false
  }
}

const resolvePinStatusVariant = (status, type = 'color') => {
  const statusDefination = {
    Approved: {
      color: '#00C853',
      text: 'Approved',
      icon: 'tabler-circle-filled',
      size: '15px'
    },
    Rejected: {
      color: '#FD4949',
      text: 'Rejected',
      icon: 'tabler-circle-filled',
      size: '15px'
    },
    Processing: {
      color: '#2962FF',
      text: 'Processing',
      icon: 'mdi-circle-slice-4',
      size: '15px',
      iconStyle: { transform: 'rotate(90deg)' }
    },
    Success: {
      color: '#44D62C',
      text: 'Generate Success',
      icon: 'mdi-check-decagram',
      size: '15px'
    },
    Failed: {
      color: '#EB0000',
      text: 'Generate Failed',
      icon: 'mdi-alert-circle',
      size: '15px'
    },
    Pending: {
      color: '#FFA800',
      text: 'Pending Approval',
      icon: 'tabler-clock-hour-3-filled',
      size: '15px'
    },
  }
  return statusDefination[status][type]
}

loadData()
// 👉 debounced load data
const debouncedLoadData = debounce(loadData, 200)


const handleSearch = () => {
  page.value = 1
  searchQuery.value.page = 1
  loadData()
}


// computed 
const pins = computed(() => pinsData.value?.records?.map((item, index) => ({ ...item, no: index + 1 })) || [])
const totalPins = computed(() => pinsData.value?.total || 0)
const status = computed(() => statuses.value.map(item => ({title: item, value: item})) || [])

const createPin = () => {
  router.push('/pin-inventory/pin-generation-request/add')
}

const handlePageChange = newPage => {
  page.value = newPage
  searchQuery.value.page = newPage
  loadData()
}

const handleItemsPerPageChange = newItemsPerPage => {
  newItemsPerPage = parseInt(newItemsPerPage, 10)
  itemsPerPage.value = newItemsPerPage
  searchQuery.value.size = newItemsPerPage
  page.value = 1
  searchQuery.value.page = 1
  loadData()
}

const handleApprove = (item) => {
  console.log(item)
  router.push({
    path: '/pin-inventory/pin-generation-request/update',
    query: {
      id: item.requestId
    }
  })
}
</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            Razer Gold PIN Generation
            <VSpacer />

            <div class="app-user-search-filter d-flex align-center gap-4">

              <!-- 👉 Create PIN button -->
              <VBtn @click="createPin" v-can="['Add', 'RGPinGeneration']">
                CREATE
              </VBtn>
            </div>
          </div>
        </VCardTitle>

      </VCardItem>

      <VCardText class="px-0">
        <VRow>
          <!-- 👉 Search Requestor -->
          <VCol cols="12" sm="4" xl="4" xxl="2">
            <AppTextField v-model="searchQuery.requestId" placeholder="Search Request ID" clearable
              @update:model-value="handleSearch" />
          </VCol>
          <!-- 👉 Search Batch Name -->
          <VCol cols="12" sm="4" xl="4" xxl="2">
            <AppTextField v-model="searchQuery.batchName" placeholder="Search Batch Name" clearable
              @update:model-value="handleSearch" />
          </VCol>
          <!-- 👉 Select Status -->
          <VCol cols="12" sm="4" xl="4" xxl="2">
            <AppSelect v-model="searchQuery.statuses" placeholder="Select Status" :items="status" clearable
              clear-icon="tabler-x" @update:model-value="handleSearch" multiple/>
          </VCol>
          <!-- 👉 Search Region -->
          <VCol cols="12" sm="4" xl="4" xxl="2">
            <AppSelect v-model="searchQuery.regions" placeholder="Select Region" :items="razerGoldPINGroups" clearable
              clear-icon="tabler-x" @update:model-value="handleSearch" multiple/>
          </VCol>
          <!-- 👉 Select Requested On -->
          <VCol cols="12" sm="4" xl="4" xxl="2">
            <AppDateTimePicker v-model="searchQuery.requestedOn" :config="{
              mode: 'range',
              enableTime: false,
              dateFormat: 'Y-m-d',
            }" placeholder="Select Requested On" clearable @update:model-value="handleSearch" />
          </VCol>
          <!-- 👉 Select Approval On -->
          <VCol cols="12" sm="4" xl="4" xxl="2">
            <AppDateTimePicker v-model="searchQuery.approvedOn" :config="{
              mode: 'range',
              enableTime: false,
              dateFormat: 'Y-m-d',
            }" placeholder="Select Approved On" clearable @update:model-value="handleSearch" />
          </VCol>
        </VRow>
      </VCardText>

      <VDivider />

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer v-model:items-per-page="itemsPerPage" v-model:model-value="selectedRows" v-model:page="page"
        :items="pins" item-value="id" :items-length="totalPins" :headers="headers" :loading="isLoading"
        class="text-no-wrap" @update:options="updateOptions">

        <!-- Region -->
        <template #[`item.region`]="{ item }">
          <span >{{ region[item.region] }}</span>
        </template>

        <!-- SFTP File -->
        <template #[`item.needSFTP`]="{ item }">
          <span class="align-center">
            <VIcon v-if="item.needSFTP" icon="tabler-check" color="success"/>
            <VIcon v-else icon="tabler-x"/>
          </span>
        </template>
        <!-- Submitted On -->
        <template #[`item.createdDateTime`]="{ item }">
          {{ dateUtil.format(item.createdDateTime) }}
        </template>
        <!-- Approval On -->
        <template #[`item.approvedDateTime`]="{ item }">
          {{ dateUtil.format(item.approvedDateTime) }}
        </template>

        <!-- Status -->
        <template #[`item.status`]="{ item }">
          <span class="d-flex align-center">
            <VIcon :icon="resolvePinStatusVariant(item.displayStatus, 'icon')"
                   :color="resolvePinStatusVariant(item.displayStatus, 'color')"
                   :size="resolvePinStatusVariant(item.displayStatus, 'size')"
                   :style ="resolvePinStatusVariant(item.displayStatus, 'iconStyle') || {}"/>
            <span class="ms-1">{{item.displayStatus}}</span>
            <VTooltip v-if="item.displayStatus === 'Rejected' && item.reason" location="top">
              <template #activator="{ props }">
                <VIcon
                    v-bind="props"
                    icon="mdi-help-circle-outline"
                    size="15"
                    class="ms-1"
                />
              </template>
              <span>{{ item.reason }}</span>
            </VTooltip>
          </span>
        </template>

        <!-- Actions -->
        <template #[`item.actions`]="{ item }">
          <div class="flex-center gap-2">
              <IconBtn v-can="['Approve', 'RGPinGeneration']"
                       :disabled="item.displayStatus !== 'Pending'">
                <VIcon :class="item.displayStatus === 'Pending' ? 'hover:text-primary' : 'text-disable'"
                       icon="mdi-text-box-edit"
                       @click="handleApprove(item)"
                       v-tooltip="'Review & Approve'"/>
              </IconBtn>
          </div>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination :page="page" :items-per-page="itemsPerPage" :total-items="totalPins"
            @update:page="handlePageChange">
            <div class="d-flex gap-3">
              <AppSelect 
                :model-value="itemsPerPage" 
                :items="paginationOptions" 
                @update:model-value="handleItemsPerPageChange" 
              />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
  </section>
</template>

<style lang="scss" scoped>
.d-flex.align-center .v-icon {
  margin-bottom: 1px;
}
</style>
