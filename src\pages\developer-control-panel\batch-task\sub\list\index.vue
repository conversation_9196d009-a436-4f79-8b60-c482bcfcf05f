<script setup>
import { useRouter, useRoute } from "vue-router";
import { dateUtil } from "@/utils/day";
import { computed, watch } from "vue";
import {useSubTaskStatus} from "@/business/developer-control-panel/batch-task/sub/useSubTaskStatus.js";
import {useSubTaskType} from "@/business/developer-control-panel/batch-task/sub/useSubTaskType.js";
import {PAGINATION_OPTIONS as paginationOptions} from "@/utils/constants.js";
import AppAutocomplete from "@/components/AppAutocomplete.vue";
import {confirmDialog} from "@/utils/dialog.js";
import message from "@/utils/message.js";

definePage({
  alias: '/developer-control-panel/batch-task/main/list/sub',
  meta: {
    navActiveLink: 'developer-control-panel-batch-task-main-list',
    breadcrumb: 'Sub Task List',
  },
})

const router = useRouter();
const route = useRoute();
const mainTaskId = computed(() => route.query.mainTaskId);

const headers = ref([
  { title: '', key: 'data-table-select', fixed: true, width: 50},
  {title: 'Id', key: 'id'},
  {title: 'Main Task Id', key: 'mainTaskId'},
  {title: 'Task Type', key: 'type'},
  {title: 'Status', key: 'status'},
  {title: 'Retry', key: 'retry'},
  {title: 'Error Message', key: 'errorMsg'},
  {title: 'Task Config', key: 'taskConfig'},
  {title: 'Version', key: 'version'},
  {title: 'File Path', key: 'filePath'},
  {title: 'Created On', key: 'createdDateTime'},
  {title: 'Last Modified', key: 'updatedDateTime'},
  {title: 'Actions', key: 'actions', sortable: false, fixed: true, width: 120 }
]);



//*********************************************** custom query params ***********************************************
const customGetQueryParams = () => {
  const params = {}
  params.page = searchQuery.value.page
  params.size = searchQuery.value.size
  params.taskId = searchQuery.value.taskId
  params.typeInList = searchQuery.value.typeInList
  params.statusInList = searchQuery.value.statusInList
  params.mainTaskId = searchQuery.value.mainTaskId

  if (searchQuery.value.sortBy && searchQuery.value.orderBy) {
    params.orderItemList = [{
      column: searchQuery.value.sortBy,
      asc: searchQuery.value.orderBy === 'asc',
    }]
  } else {
    params.orderItemList = null
  }
  // process creation time
  if (searchQuery.value.createdDateTime) {
    const [start, end] = searchQuery.value.createdDateTime.split('to')
    if (start) {
      params.gmtCreateGe = dateUtil.format(start, 'YYYY-MM-DDTHH:mm:ss.SSSZ')
      params.gmtCreateLe = dateUtil.format(start, 'YYYY-MM-DDT23:59:59.SSSZ')
    }
    if (end) {
      params.gmtCreateLe = dateUtil.format(end, 'YYYY-MM-DDT23:59:59.SSSZ')
    }
  }
  return params
}

//*********************************************** use useTable hook to handle table data ***********************************************
const {
  searchQuery,
  page,
  itemsPerPage,
  items: subTasks,
  total: totalSubTasks,
  isLoading,
  handlePageChange,
  handleItemsPerPageChange,
  handleSearch,
  handleSortChange,
  loadData,
} = useTable({
  fetchData() {
    return $api("api/pin-generator/v1/batch-task/sub-task/page", {
      method: "POST",
      data: customGetQueryParams()
    });
  },
  createDefaultQuery: () => ({
    taskId: null,
    typeInList: null,
    statusInList: null,
    createdDateTime: null,
    updatedDateTime: null,
    mainTaskId: mainTaskId.value,
    page: 1,
    size: 10,
    sortBy: null,
    orderBy: null
  }),
  rowKey: "id"
});

const fetchSubTasks = (id) => {
  // Update the search query mainTaskId and trigger data reload
  searchQuery.value.mainTaskId = id;
  loadData();
}

watch(() => mainTaskId.value, () => {
  if (mainTaskId.value) {
    fetchSubTasks(mainTaskId.value);
  }
}, { immediate: true });

//*********************************************** Handle task config expand ***********************************************
// save expanded row status
const expanded = ref([])

// check if the current item is in expanded status
const isExpanded = (item) => {
  return expanded.value.includes(item.id)
}

// check if error can be expanded
const canExpandError = (item) => {
  return item.errorMsg && item.errorMsg.trim() !== ""
}

// check if config can be expanded
const canExpandConfig = (item) => {
  return item.taskConfig && item.taskConfig.trim() !== ""
}

// trigger expand operation
const toggleExpand = (item) => {
  const index = expanded.value.indexOf(item.id)
  if (index === -1) {
    expanded.value.push(item.id)
  } else {
    expanded.value.splice(index, 1)
  }
}

const handleRowClick = (event, { item }) => {
  // ignore general hitting button event
  if (event.target.closest('.v-btn') || event.target.closest('button')) return

  // only the column 'taskConfig' can be expanded
  if (event.target.closest('[data-column="taskConfig"]') && canExpandConfig(item)) {
    toggleExpand(item)
  } else if (event.target.closest('[data-column="errorMsg"]') && canExpandError(item)) {
    toggleExpand(item)
  }
}

//*********************************************** Handle sub task retry ***********************************************
const statusActionMap = {
  retryableStatus: ['ER', 'TO']
}
const isRetryableTask = (status) => statusActionMap.retryableStatus.includes(status)

const manualRetrySubTask = (item) => {
  let isConfirmed = false;

  confirmDialog({
    title: 'Confirm retry sub task',
    text: 'Are you sure you want to retry this task? This operation cannot be undone.',
    confirmButtonText: 'CONFIRM RETRY',
    confirmButtonColor: 'primary',
    confirmButtonTextColor: '#000000',
    cancelButtonText: 'CANCEL',
    async onConfirm(close) {
      if(isConfirmed) return
      isConfirmed = true
      try {
        await $api(`/api/pin-generator/v1/batch-task/sub-task/retry`, {
          method: "PUT",
          params: {
            id: item.id
          }
        });
        message.success('Task retried successfully');
        close()
        loadData()
      } catch (error) {
        message.error('Failed to retry task')
      } finally {
        isConfirmed = false
      }
    }
  })
}

const { subTaskTypeList } = useSubTaskType()
const { subTaskStatusList } = useSubTaskStatus()

</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            Sub Task
          </div>
        </VCardTitle>
      </VCardItem>

      <VCardText class="px-0">
        <VRow>
          <!-- 👉 Search Task Id -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField
                v-model="searchQuery.taskId"  placeholder="Task Id" clearable
                @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Search Main Task Id -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField
                v-model="searchQuery.mainTaskId"  placeholder="Main Task Id" clearable
                @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Search Task Type -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete v-model="searchQuery.typeInList"  placeholder="Task Type"
             :items="subTaskTypeList" clearable clear-icon="tabler-x" multiple filterable chips closable-chips
             @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Select Task Status -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete v-model="searchQuery.statusInList" placeholder="Task Status"
             :items="subTaskStatusList" clearable clear-icon="tabler-x" multiple filterable chips closable-chips
             @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Select Created On   -->
          <VCol cols = "12" sm = "4" xl = "3" xxl = "2">
            <AppDateTimePicker v-model="searchQuery.createdDateTime" :config="{
              mode: 'range',
              enableTime: false,
              dateFormat: 'Y-m-d'
            }" placeholder="Created On" clearable @update:model-value="handleSearch"/>
          </VCol>
        </VRow>
      </VCardText>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer :items="subTasks" item-value="id" :items-length="totalSubTasks"
          :headers="headers" :loading="isLoading" class="text-no-wrap"
          v-model:expanded="expanded" @click:row="handleRowClick" show-select @update:sort-by="handleSortChange"
      >
        <!-- Expanded-row-->
        <template #expanded-row="{ item }">
          <tr class="expanded-row">
            <td :colspan="6"></td>
            <td :colspan="1">
              <div class="expanded-content">
                <pre class="error-message">{{ item.errorMsg }}</pre>
              </div>
            </td>
            <td :colspan="1">
              <div class="expanded-content">
                <highlightjs language="json" :code="formatJson(item.taskConfig)"/>
              </div>
            </td>
            <td :colspan="5"></td>
          </tr>
        </template>

        <!-- ERROR MESSAGE -->
        <template #[`item.errorMsg`]="{ item }">
          <div class="d-flex align-center w-240px" data-column="errorMsg">
            <span>{{ item.errorMsg ? "Error Detail" : "N/A" }}</span>
            <VIcon
                v-if="canExpandError(item)" :icon="isExpanded(item) ? 'tabler-chevron-up' : 'tabler-chevron-down'"
                size="20" class="ms-2" color="grey-400"
            />
          </div>
        </template>

        <!-- TASK CONFIG -->
        <template #[`item.taskConfig`]="{ item }">
          <div class="d-flex align-center w-240px" data-column="taskConfig">
            <span>{{ item.taskConfig ? "Config Detail" : "N/A" }}</span>
            <VIcon
                v-if="canExpandConfig(item)" :icon="isExpanded(item) ? 'tabler-chevron-up' : 'tabler-chevron-down'"
                size="20" class="ms-2" color="grey-400"
            />
          </div>
        </template>

        <!-- FILE PATH -->
        <template #[`item.finalFilePath`]="{ item }">
          <span>{{ item.finalFilePath || "N/A" }}</span>
        </template>

        <!-- CREATED ON -->
        <template #[`item.createdDateTime`]="{ item }">
          {{ dateUtil.format(item.createdDateTime) }}
        </template>

        <!-- LAST MODIFIED -->
        <template #[`item.updatedDateTime`]="{ item }">
          {{ dateUtil.format(item.updatedDateTime) }}
        </template>

        <!-- Actions -->
        <template #[`item.actions`]="{ item }">
          <div class="actions-grid">
            <div class="action-item">
              <IconBtn v-if="isRetryableTask(item.status)" v-can="['Edit', 'BatchTask']" class="retry-button"
                       @click.stop="manualRetrySubTask(item)" v-tooltip="'Retry Task'">
                <VIcon icon="tabler-rotate-clockwise" />
              </IconBtn>
            </div>
          </div>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination
              :page="page"
              :items-per-page="itemsPerPage"
              :total-items="totalSubTasks"
              @update:page="handlePageChange"
          >
            <div class="d-flex gap-3">
              <AppSelect
                  :model-value="itemsPerPage"
                  :items="paginationOptions"
                  @update:model-value="handleItemsPerPageChange"
              />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
  </section>
</template>

<style scoped lang="scss">
.expanded-content :deep(.hljs) {
  background: transparent;
}

.error-message {
  white-space: pre-wrap;
  word-break: break-word;
  overflow-y: auto;
  background: transparent !important;
  max-width: 225px;
  padding: clamp(2px, calc(0.3em + 1px), 6px);
  margin-bottom: min(1.5%, calc(0.3em + 4px)) !important;
  color: #AAAAAA !important;
  font-family: monospace, monospace !important;
  font-size: 13.5px !important;
  font-weight: 400 !important;
}

.actions-grid {
  display: grid;
  grid-template-columns: 40px 40px;
  gap: 8px;
}

.action-item {
  display: flex;
  justify-content: center;
}

.custom-link {
  color: rgb(var(--v-theme-primary));
  text-decoration: none;
  cursor: pointer;
  font-weight: 400;
  padding: 0 8px;
  min-height: 36px;
  display: inline-flex;
  align-items: center;

  &:hover {
    opacity: 0.8;
    text-decoration: none;
    background: rgba(var(--v-theme-primary), 0.05);
    border-radius: 4px;
  }
}

// Add divider style
:deep(.v-theme--dark.v-divider--vertical) {
  height: 22px;
  opacity: 1;
}

// Add icon animation
.v-icon {
  transition: transform 0.3s ease;

  &.rotated {
    transform: rotate(180deg);
  }
}

:deep(.v-data-table) {
  .v-table__wrapper {
    overflow: auto;
  }

  tr:has(.tabler-chevron-up) .v-data-table-column--last-fixed {
    border-bottom: none;
  }

  .v-data-table-column--last-fixed {
    right: 0;
    border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));

  }
}

.retry-button {
  :deep(.v-icon) {
    color: rgba(var(--v-theme-grey-400), 1);
    transition: color 0.2s ease, transform 0.5s ease;
  }

  &:hover {
    :deep(.v-icon) {
      color: rgba(var(--v-theme-success), 1);
      transform: rotate(360deg);
    }
  }
}
</style>