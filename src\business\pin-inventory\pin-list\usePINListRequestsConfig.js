import { onMounted } from 'vue'

export const useProductNames = () => {
  const productNames = ref([])

  const loadData = async () => {
    try {
      const res = await $api('/api/pin-inventory/v1/lac/pin-list/products', {
        method: 'GET',
      })

      if (Array.isArray(res.data)) {
        productNames.value = res.data.map((item) => ({
          title: item.productName,
          value: item.productId,
        }))
      }
    } catch (error) {
      message.error('Failed to load product names')
    }
  }

  onMounted(async () => {
    await loadData()
  })

  return productNames
}

export const usePINCardTypes = () => {
  const pinCardTypes = ref([])

  const loadData = async () => {
    try {
      const res = await $api(
        '/api/pin-inventory/v1/lac/pin-list/pin-card-types',
        {
          method: 'GET',
        }
      )

      if (Array.isArray(res.data)) {
        pinCardTypes.value = res.data.map((item) => ({
          title: item.cardTypeDescription,
          value: item.id,
        }))
      }
    } catch (error) {
      message.error('Failed to load PIN card types')
    }
  }

  onMounted(async () => {
    await loadData()
  })

  return pinCardTypes
}

export const useRedemptionRegions = () => {
  const redemptionRegions = ref([])

  const loadData = async () => {
    try {
      const res = await $api(
        '/api/pin-inventory/v1/lac/pin-list/redemption-regions',
        {
          method: 'GET',
        }
      )

      if (Array.isArray(res.data)) {
        redemptionRegions.value = res.data.map((item) => ({
          title: item.regionName,
          value: item.regionId,
        }))
      }
    } catch (error) {
      message.error('Failed to load redemption regions')
    }
  }

  onMounted(async () => {
    await loadData()
  })

  return redemptionRegions
}

export const usePINStatuses = () => {
  return [
    { title: 'Uploaded Pending Approval', value: '-2' },
    { title: 'Pending Activation', value: '-1' },
    { title: 'Unused', value: '0' },
    { title: 'Used', value: '1' },
    { title: 'Block', value: '2' },
    { title: 'Partial', value: '3' },
    { title: 'Void', value: '5' },
    { title: 'Adjusted', value: '100' },
  ]
}

export const useRedemptionCurrencies = () => {
  const redemptionRegions = ref([])

  const loadData = async () => {
    try {
      const res = await $api(
        '/api/pin-inventory/v1/lac/pin-list/redemption-currencies',
        {
          method: 'GET',
        }
      )

      if (Array.isArray(res.data)) {
        redemptionRegions.value = res.data
      }
    } catch (error) {
      message.error('Failed to load redemption regions')
    }
  }

  onMounted(async () => {
    await loadData()
  })

  return redemptionRegions
}

export const useRedemptionSources = () => {
  const redemptionSources = ref([])

  const loadData = async () => {
    try {
      const res = await $api(
        '/api/pin-inventory/v1/lac/pin-list/redemption-sources',
        {
          method: 'GET',
        }
      )

      if (Array.isArray(res.data)) {
        redemptionSources.value = res.data.map((item) => ({
          title: item.name,
          value: item.id,
        }))
      }
    } catch (error) {
      message.error('Failed to load redemption sources')
    }
  }

  onMounted(async () => {
    await loadData()
  })

  return redemptionSources
}

export const useTimezones = () => {
  return [
    { title: 'UTC+0', value: 0 },
    { title: 'UTC+1', value: 1 },
    { title: 'UTC+2', value: 2 },
    { title: 'UTC+3', value: 3 },
    { title: 'UTC+4', value: 4 },
    { title: 'UTC+5', value: 5 },
    { title: 'UTC+6', value: 6 },
    { title: 'UTC+7', value: 7 },
    { title: 'UTC+8', value: 8 },
    { title: 'UTC+9', value: 9 },
    { title: 'UTC+10', value: 10 },
    { title: 'UTC+11', value: 11 },
    { title: 'UTC+12', value: 12 },
    { title: 'UTC-1', value: -1 },
    { title: 'UTC-2', value: -2 },
    { title: 'UTC-3', value: -3 },
    { title: 'UTC-4', value: -4 },
    { title: 'UTC-5', value: -5 },
    { title: 'UTC-6', value: -6 },
    { title: 'UTC-7', value: -7 },
    { title: 'UTC-8', value: -8 },
    { title: 'UTC-9', value: -9 },
    { title: 'UTC-10', value: -10 },
    { title: 'UTC-11', value: -11 },
  ]
}
