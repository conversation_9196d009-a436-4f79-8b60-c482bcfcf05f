import { beforeEach, describe, expect, it, vi } from 'vitest'
import { nextTick, onMounted, ref } from 'vue'

import CommissionSchemeManagers from '@/pages/payment-partner-commission/commission-scheme-managers/index.vue'
import { mount } from '@vue/test-utils'
import { vuetify } from '@test/unit/setup/vuetify'

// Mock useMerchant hook
vi.mock('@/business/payment-partner-commission/useMerchant', () => ({
  useMerchant: () => ({
    merchantList: ref([
      { id: 101, title: 'Test Merchant', value: 101 },
      { id: 102, title: 'Premium Merchant', value: 102 }
    ]),
    loadMerchantList: vi.fn()
  })
}))

// Use global mockCookies and mockStorage
const { mockCookies, mockStorage } = global

// Mock useTable hook
vi.mock('@/hooks/useTable', () => ({
  useTable: ({ fetchData, createDefaultQuery }) => {
    const searchQuery = ref(createDefaultQuery())
    const page = ref(1)
    const itemsPerPage = ref(10)
    const items = ref([])
    const total = ref(0)
    const isLoading = ref(false)

    const getQueryParams = () => ({
      ...searchQuery.value,
      page: page.value,
      size: itemsPerPage.value
    })

    const loadData = async () => {
      isLoading.value = true
      try {
        const res = await fetchData()
        items.value = res.data.records
        total.value = res.data.total
      } finally {
        isLoading.value = false
      }
    }

    // Add onMounted hook
    onMounted(() => {
      loadData()
    })

    return {
      searchQuery,
      page,
      itemsPerPage,
      items,
      total,
      isLoading,
      handlePageChange: async (newPage) => {
        page.value = newPage
        await loadData()
      },
      handleItemsPerPageChange: async (newSize) => {
        itemsPerPage.value = newSize
        await loadData()
      },
      handleSearch: async () => {
        page.value = 1
        await loadData()
      },
      getQueryParams,
      handleSortChange: vi.fn(),
      loadData
    }
  }
}))

describe('CommissionSchemeManagers.vue', () => {
  let wrapper
  // Move mockCommissionSchemes outside so all tests can use it
  const mockCommissionSchemes = {
    data: {
      records: [
        {
          id: 1,
          name: 'Test Commission Scheme',
          merchantName: 'Test Merchant',
          merchantId: 101,
          createdDateTime: '2023-05-15T08:30:00Z',
          updatedDateTime: '2023-05-16T10:20:00Z'
        },
        {
          id: 2,
          name: 'Premium Commission Scheme',
          merchantName: 'Premium Merchant',
          merchantId: 102,
          createdDateTime: '2023-06-20T09:15:00Z',
          updatedDateTime: '2023-06-21T11:45:00Z'
        }
      ],
      total: 2
    }
  }

  beforeEach(() => {
    // Clear all mocks
    vi.clearAllMocks()
    
    // Clear storage
    Object.keys(mockCookies).forEach(key => delete mockCookies[key])
    Object.keys(mockStorage).forEach(key => delete mockStorage[key])

    // Modify API mock to always return the same data
    global.$api = vi.fn().mockResolvedValue(mockCommissionSchemes)

    // Mount component
    wrapper = mount(CommissionSchemeManagers, {
      global: {
        plugins: [vuetify],
        stubs: {
          AppTextField: true,
          AppAutocomplete: true,
          VCard: true,
          VCardItem: true,
          VCardTitle: true,
          VCardText: true,
          VBtn: true,
          VRow: true,
          VCol: true,
          VSpacer: true,
          VDivider: true,
          IconBtn: true,
          VIcon: true,
          TablePagination: true,
          AppSelect: true,
          VDataTableServer: {
            template: `
              <div class="v-data-table">
                <div v-if="$attrs.items && $attrs.items.length">
                  <div v-for="item in $attrs.items" :key="item.id" class="scheme-row">
                    {{ item.name }} - {{ item.merchantName }}
                  </div>
                </div>
                <slot name="bottom"></slot>
              </div>
            `,
            props: ['items', 'loading', 'headers', 'itemsLength']
          }
        },
        mocks: {
          $api: global.$api
        }
      }
    })
  })

  it('should load commission scheme data', async () => {
    // Clear previous API call records
    global.$api.mockClear()
    
    // Ensure API call returns correct data
    global.$api.mockResolvedValueOnce(mockCommissionSchemes)
    
    // Wait for component mount and data loading
    await nextTick()
    // Wait longer to ensure async operations complete
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // Verify data is loaded correctly - note using .value
    const items = wrapper.vm.items.value
    expect(items).toBeDefined()
    expect(items.length).toBe(2)
    expect(items[0]).toMatchObject({
      name: 'Test Commission Scheme',
      merchantName: 'Test Merchant'
    })

    // Add debug information
    console.log('items:', items)
    console.log('API calls:', global.$api.mock.calls)
  })

  it('should set search parameters correctly', async () => {
    global.$api.mockClear()
    global.$api.mockResolvedValueOnce(mockCommissionSchemes)
    
    wrapper.vm.searchQuery.value.commissionSchemeNameLike = 'Premium'
    await wrapper.vm.handleSearch()
    await nextTick()

    expect(global.$api).toHaveBeenCalledWith('api/admin-api/v1/commission-scheme/page-query', {
      method: 'POST',
      data: expect.objectContaining({
        commissionSchemeNameLike: 'Premium',
        page: 1,
        size: 10
      })
    })
  })

  it('should handle pagination changes correctly', async () => {
    global.$api.mockClear()
    global.$api.mockResolvedValueOnce(mockCommissionSchemes)
    
    await wrapper.vm.handlePageChange(2)
    await nextTick()

    expect(wrapper.vm.page.value).toBe(2)
    expect(global.$api).toHaveBeenCalledWith('api/admin-api/v1/commission-scheme/page-query', {
      method: 'POST',
      data: expect.objectContaining({
        page: 2
      })
    })
  })

  it('should handle items per page changes correctly', async () => {
    // Clear previous API call records
    global.$api.mockClear()
    
    // Ensure API call returns correct data
    global.$api.mockResolvedValueOnce(mockCommissionSchemes)
    
    // Trigger items per page change
    await wrapper.vm.handleItemsPerPageChange(20)
    
    // Wait for async operations
    await nextTick()

    // Verify items per page update
    expect(wrapper.vm.itemsPerPage.value).toBe(20)
    
    // Verify API call includes updated items per page
    expect(global.$api).toHaveBeenCalledWith('api/admin-api/v1/commission-scheme/page-query', {
      method: 'POST',
      data: expect.objectContaining({
        size: 20
      })
    })
  })

  it('should handle add button click correctly', async () => {
    // Mock console.log
    const consoleSpy = vi.spyOn(console, 'log')
    
    // Trigger add button click
    await wrapper.vm.handleAdd()
    
    // Verify console.log is called
    expect(consoleSpy).toHaveBeenCalledWith('handleAdd')
  })

  it('should handle edit button click correctly', async () => {
    // Mock console.log
    const consoleSpy = vi.spyOn(console, 'log')
    
    // Trigger edit button click
    const item = { id: 1, name: 'Test Commission Scheme' }
    await wrapper.vm.handleEdit(item)
    
    // Verify console.log is called
    expect(consoleSpy).toHaveBeenCalledWith('handleEdit', item)
  })
}) 