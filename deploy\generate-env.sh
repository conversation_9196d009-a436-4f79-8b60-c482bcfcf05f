#!/usr/bin/env bash

declare -x SERVICE_NAME=paychn-forter
declare -xr BUILD=PREBUILD
declare -x TEST_DIR=Paychn.Forter.Test
declare -x SERVICE_ENV=dev

#dotnet nuget locals all --clear
sed -e "s;%SERVICE_NAME%;$SERVICE_NAME;g" \
    -e "s;%SERVICE_ENV%;$SERVICE_ENV;g" \
    ./env-test.json > env-tmp.json 

while read -r name value; do
    echo "$name = $value" >> test.env
done< <(jq -r '.[] | "\(.name) \(.value)"' env-tmp.json)
    
sed -e "s;%ENV%;ZGD;g" ./secret.json > temp.json 
for k in $(jq -c '.[]' temp.json); do  
  name=$(echo "$k" | jq  -r '.name') ; 
  valueFrom=$(echo "$k" | jq  -r '.valueFrom') ; 
  secret=$(aws secretsmanager get-secret-value --secret-id "$valueFrom" --region ap-southeast-1 | jq -r '.SecretString' | tr -d '\r\n\t'); 
  echo "$name = $secret" >> test.env
done

mv test.env "../$TEST_DIR/Configs/Test.env"
rm env-tmp.json temp.json

