# Frontend Project Deployment Guide (Based on rollout-dev.yaml, Dockerfile, nginx.conf, sonar-project.properties)

---

## 1. Overall Deployment Process

1. **Code Development and Submission**
   - Developers complete frontend code development and submit via PR to the code repository (e.g., Bitbucket).

2. **Code Quality Check**
   - The CI/CD pipeline automatically triggers SonarQube, reading `sonar-project.properties` to analyze code quality.

3. **Build Docker Image**
   - CI/CD or local build runs `docker build` using the `Dockerfile`:
     - Uses a Node image to build frontend code
     - Uses an Nginx image to serve static files
     - Copies `nginx.conf` as the Nginx configuration
   - The resulting image is pushed to the image registry (e.g., AWS ECR).

4. **K8s Deployment and Release**
   - Ops or CI/CD tools use `rollout-dev.yaml` to deploy to the K8s cluster:
     - Pulls the image and starts the Pod
     - Nginx starts and provides HTTPS service
     - K8s checks service health via `/healthcheck`
     - Argo Rollouts manages progressive delivery (canary release) and rollback

5. **External Service Access**
   - Users access the service via domain name; Nginx serves static resources and proxies API requests.

---

## 2. Role of the Four Configuration Files

### 1. `sonar-project.properties`
- **Purpose**: Configures SonarQube code quality checks.
- **Content**: Specifies project key, encoding, and base directory.
- **Usage**: Used in CI/CD pipelines to automatically check code quality and detect bugs or code smells.

### 2. `nginx.conf`
- **Purpose**: Defines Nginx server behavior.
- **Content**:
  - Configures HTTPS certificates
  - Serves static frontend files
  - Proxies `/api` to backend services
  - Health check endpoint `/healthcheck`
  - Security headers
- **Usage**: Nginx reads this config on container startup to provide secure web service.

### 3. `Dockerfile`
- **Purpose**: Defines how to build the frontend Docker image.
- **Content**:
  - Uses Node image to build frontend code
  - Uses Nginx image to serve the build output
  - Copies nginx.conf as the Nginx config
- **Usage**: Used in CI/CD or local builds to ensure consistent environments for K8s deployment.

### 4. `rollout-dev.yaml`
- **Purpose**: Kubernetes (Argo Rollouts) deployment config, declaring how to deploy and upgrade the app in K8s.
- **Content**:
  - Canary release strategy (progressive delivery)
  - Image address, resource limits, health checks, certificate mounts, etc.
- **Usage**: Used by CI/CD or ops with `kubectl apply -f` to deploy to K8s, enabling automated, rollback-capable releases.

---

## 3. Step-by-Step Guide for New Projects

### 1. Code Quality Check
- Configure `sonar-project.properties`.
- Set `sonar.projectKey` (must be unique for each project):
```
sonar.projectKey=ZGQ-DEPLOY-GOLD-ADMIN-PORTAL-V2
```
- Run SonarQube scan in CI/CD.

### 2. Build Docker Image
- Ensure `Dockerfile` and `nginx.conf` are configured for your project needs.
- Update the `location /api` block in `nginx.conf` to match your actual API endpoint:
```conf
    location /api {
        proxy_pass https://console-v2.zgold-dev.razer.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $http_x_forwarded_proto;
    }
```

### 3. Configure K8s Deployment File
- Copy and modify `rollout-dev.yaml` as needed.
- For production, pay attention to these settings:
```yaml
spec:
  replicas: 1 # Set to 2 for production
  revisionHistoryLimit: 3
  strategy:
    canary:
      steps:
        # - pause: {}  # Pause is not needed in dev/test, but required in production
        - setWeight: 100
        # Refer to https://argo-rollouts.readthedocs.io/en/stable/features/canary/
        # Example:
        # - pause: {}
        # - setWeight: 33
        # - pause: { duration: 10s }
```

## 4. Common Issues and Troubleshooting

- **API proxy issues**: Check the `/api` proxy config in `nginx.conf`.
- **Canary release not working**: Check the canary config in `rollout-dev.yaml`.
- **Rollback**: Use Argo Rollouts or `kubectl` to roll back to a previous version.

---

If you encounter more issues, consult your actual CI/CD pipeline and K8s cluster documentation, or ask your DevOps team for help. 