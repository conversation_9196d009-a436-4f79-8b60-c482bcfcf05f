<script setup>
import { useRouter } from 'vue-router'
import RoleForm from '@/business/user/role/form.vue'
import { dateUtil } from '@/utils/day'
import { watchEffect } from 'vue';
import { $api } from '@/utils/request';
import { treeToList } from '@/utils/helpers';
definePage({
  meta: {
    navActiveLink: 'user-role',
    breadcrumb: 'Create New Role & Assign Permissions',
  },
})

const assignPermissionCopy = ref([])
const formRef = ref(null)
const isSaving = ref(false)


const router = useRouter()
const userFormRef = ref(null)

// form data
const formModel = reactive({
  name: '',
  userGroupId: '',
  description: '',
  assignPermission: [],
  statusId: 1
})

const handleCancel = () => {
  userFormRef.value?.resetForm()
  router.back()
}

const handleSave = async () => {
  // validate form
  const { valid, errors } = await formRef.value?.validate()
  if (!valid) return;
  if (isSaving.value) return;
  try {
    isSaving.value = true
    const params = formRef.value.getParams()
    const res = await $api('/api/admin-api/v1/role', {
      method: 'POST',
      body: params
    })
    message.success('Role created successfully')
    router.back()
  } finally {
    isSaving.value = false
  }
}

</script>

<template>
  <VCard>
    <VCardItem class="pb-4 px-0">
      <VCardTitle>
        Create New Role & Assign Permissions
      </VCardTitle>
    </VCardItem>
    
    <RoleForm 
      ref="formRef"
      v-model="formModel"
      type="add"
    />
    
    <VRow class="d-flex mt-3">
      <VCol cols="12" class="d-flex justify-end">
        <VBtn variant="outlined" color="default" @click="handleCancel">
          CANCEL
        </VBtn>
        <VBtn color="primary" class="ml-2" style="width: 120px;" @click="handleSave">
          SAVE
        </VBtn>
      </VCol>
    </VRow>
  </VCard>
</template>
