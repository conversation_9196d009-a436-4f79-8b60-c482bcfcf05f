<script setup lang="jsx">
import { ref } from "vue";
import { isTextOverflow } from "@/utils/helpers";
import { dateUtil } from "@/utils/day";
import { useTable } from "@/hooks/useTable";
import {PAGINATION_OPTIONS as paginationOptions} from "@/utils/constants.js";
import AppAutocomplete from "@/components/AppAutocomplete.vue";
import {useMainTaskType} from "@/business/developer-control-panel/batch-task/main/useMainTaskType.js";
import {useMainTaskStatus} from "@/business/developer-control-panel/batch-task/main/useMainTaskStatus.js";
import message from "@/utils/message.js";
import {confirmDialog} from "@/utils/dialog.js";
import {useRouter} from "vue-router";
import {hasPermission} from "@/directives/can.js";


// keys are the fields from backend
const headers = ref([
  { title: '', key: 'data-table-select', fixed: true, width: 50},
  { title: "ID", key: "id", fixed: true},
  { title: "TYPE", key: "type", sortable: false},
  { title: "BATCH NAME", key: "batchName", sortable: true},
  { title: "STATUS", key: "status", sortable: false},
  { title: "RETRY", key: "retry"},
  { title: "ERROR MESSAGE", key: "errorMsg", sortable: false},
  { title: "TASK CONFIG", key: "taskConfig", sortable: false},
  { title: "VERSION", key: "version"},
  { title: "FILE PATH", key: "finalFilePath", sortable: false},
  { title: "SFTP FILE PATH", key: "finalSftpFilePath", sortable: false},
  { title: "CREATED ON", key: "createdDateTime"},
  { title: "LAST MODIFIED", key: "updatedDateTime"},
  { title: "ACTIONS", key: "actions", sortable: false, fixed: true, width: 120 },
]);

//*********************************************** define constant variables ***********************************************
const statusActionMap = {
  stoppableStatus: ['IN', 'PR', 'PF', 'PN', 'PU'],
  retryableStatus: ['PF']
}
const isStoppableTask = (status) => statusActionMap.stoppableStatus.includes(status)
const isRetryableTask = (status) => statusActionMap.retryableStatus.includes(status)

//*********************************************** custom query params ***********************************************
const customGetQueryParams = () => {
  const params = {}
  params.page = searchQuery.value.page
  params.size = searchQuery.value.size
  params.taskId = searchQuery.value.taskId
  params.typeInList = searchQuery.value.typeInList
  params.statusInList = searchQuery.value.statusInList

  if (searchQuery.value.sortBy && searchQuery.value.orderBy) {
    params.orderItemList = [{
      column: searchQuery.value.sortBy,
      asc: searchQuery.value.orderBy === 'asc',
    }]
  } else {
    params.orderItemList = null
  }
  // process creation time
  if (searchQuery.value.createdDateTime) {
    const [start, end] = searchQuery.value.createdDateTime.split('to')
    if (start) {
      params.gmtCreateGe = dateUtil.format(start, 'YYYY-MM-DDTHH:mm:ss.SSSZ')
      params.gmtCreateLe = dateUtil.format(start, 'YYYY-MM-DDT23:59:59.SSSZ')
    }
    if (end) {
      params.gmtCreateLe = dateUtil.format(end, 'YYYY-MM-DDT23:59:59.SSSZ')
    }
  }
  return params
}

//*********************************************** use useTable hook to handle table data ***********************************************
const {
  searchQuery,
  page,
  itemsPerPage,
  items: mainTasks,
  total: totalMainTasks,
  isLoading,
  handlePageChange,
  handleItemsPerPageChange,
  handleSearch,
  handleSortChange,
  loadData,
} = useTable({
  fetchData() {
    return $api("api/pin-generator/v1/batch-task/main-task/page", {
      method: "POST",
      data: customGetQueryParams()
    });
  },
  createDefaultQuery: () => ({
    taskId: null,
    typeInList: null,
    statusInList: null,
    createdDateTime: null,
    updatedDateTime: null,
    page: 1,
    size: 10,
    sortBy: null,
    orderBy: null
  }),
  rowKey: "id"
});

const { mainTaskTypeList } = useMainTaskType()
const { mainTaskStatusList } = useMainTaskStatus()

//*********************************************** create main dump task handle function ***********************************************
const createMainDumpTask = () => {
  let isConfirmed = false;
  confirmDialog({
    title: 'Create Dump Task?',
    text: 'Once created, system performance will be affected.',
    confirmButtonText: 'CREATE',
    confirmButtonColor: 'primary',
    confirmButtonTextColor: '#000000',
    cancelButtonText: 'CANCEL',
    async onConfirm(close) {
      if (isConfirmed) return;
      isConfirmed = true;
      try {
        await $api("api/pin-generator/v1/batch-task/main-task/dump", {
          method: "POST"
        });
        message.success('Dump task created successfully');
        close();
        loadData(); // Refresh the table after creating
      } catch (error) {
        message.error('Failed to create dump task');
        console.error(error);
      } finally {
        isConfirmed = false;
      }
    }
  });
};

//*********************************************** Handle task config expand ***********************************************
// save expanded row status
const expanded = ref([])

// check if the current item is in expanded status
const isExpanded = (item) => {
  return expanded.value.includes(item.id)
}

// check if error can be expanded
const canExpandError = (item) => {
  return item.errorMsg && item.errorMsg.trim() !== ""
}

// check if config can be expanded
const canExpandConfig = (item) => {
  return item.taskConfig && item.taskConfig.trim() !== ""
}

// trigger expand operation
const toggleExpand = (item) => {
  const index = expanded.value.indexOf(item.id)
  if (index === -1) {
    expanded.value.push(item.id)
  } else {
    expanded.value.splice(index, 1)
  }
}

const handleRowClick = (event, { item }) => {
  // ignore general hitting button event
  if (event.target.closest('.v-btn') || event.target.closest('button')) return

  // only the column 'taskConfig' can be expanded
  if (event.target.closest('[data-column="batchName"]') && canExpandBatchName(item)) {
    toggleExpand(item)
  } else if (event.target.closest('[data-column="taskConfig"]') && canExpandConfig(item)) {
    toggleExpand(item)
  } else if (event.target.closest('[data-column="errorMsg"]') && canExpandError(item)) {
    toggleExpand(item)
  }
}

//*********************************************** Handle batch name expand ***********************************************
const canExpandBatchName = (item) => {
  return item.batchName && item.batchName.length > 18
}

const truncateBatchName = (name) => {
  if (!name) return "N/A";
  if (name.length <= 18) return name;
  return name.substring(0, 15) + ' ...';
}

//*********************************************** Handle batch name copy ***********************************************
const canCopyToClipboard = 'clipboard' in navigator;

const copyBatchName = (name) => {
  if (canCopyToClipboard) {
    navigator.clipboard.writeText(name)
      .then(() => {
        message.success('Batch name copied to clipboard');
      })
      .catch(() => {
        message.error('Failed to copy batch name');
      });
  }
}


//*********************************************** Handle main task stop ***********************************************
const manualStopMainTask = (item) => {
  let isConfirmed = false;
  const stopReason = ref('');
  const reasonError = ref(false);

  confirmDialog({
    title: 'Stop Main Task?',
    text: 'This action cannot be undone.',
    render: () => {
      return (
          <div class="px-4 mb-4">
            <AppTextarea
                v-model={stopReason.value}
                label="Stop Reason*"
                placeholder="Type a reason"
                error={reasonError.value}
                error-messages={reasonError.value ? "Please enter stop reason" : ""}
                maxlength="500"
                counter
                rows="3"
                class="mb-1"
                onInput={() => { reasonError.value = false }}
            />
            <div class="text-caption text-grey">Maximum 500 characters</div>
          </div>
      );
    },
    confirmButtonText: 'STOP',
    confirmButtonColor: '#FD4949',
    confirmButtonTextColor: '#000000',
    cancelButtonText: 'CANCEL',
    async onConfirm(close) {
      if(!stopReason.value || stopReason.value.trim() === '') {
        reasonError.value = true;
        return; // stop the dialog
      }

      if (isConfirmed) return
      isConfirmed = true
      try {
        await $api(`api/pin-generator/v1/batch-task/main-task/stop`, {
          method: "PUT",
          body: {
            id: item.id,
            message: stopReason.value
          }
        });
        message.success('Task stopped successfully');
        close()
        loadData()
      } catch (error) {
        message.error('Failed to stop task')
      } finally {
        isConfirmed = false
      }
    }
  })
}


//*********************************************** Handle main task retry ***********************************************
const manualRetryMainTask = (item) => {
  let isConfirmed = false;

  confirmDialog({
    title: 'Confirm retry main task',
    text: 'Are you sure you want to retry this task? This operation cannot be undone.',
    confirmButtonText: 'CONFIRM RETRY',
    confirmButtonColor: 'primary',
    confirmButtonTextColor: '#000000',
    cancelButtonText: 'CANCEL',
    async onConfirm(close) {
      if(isConfirmed) return
      isConfirmed = true
      try {
        await $api(`/api/pin-generator/v1/batch-task/main-task/retry`, {
          method: "PUT",
          params: {
            id: item.id
          }
        });
        message.success('Task retried successfully');
        close()
        loadData()
      } catch (error) {
        message.error('Failed to retry task')
      } finally {
        isConfirmed = false
      }
    }
  })
}

//*********************************************** Handle main task retry ***********************************************
const router = useRouter()

const viewSubTasks = async (item) => {
  router.push({
    path: '/developer-control-panel/batch-task/main/list/sub',
    query: { mainTaskId: item.id }
  });
}

</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            Main Task
            <VSpacer />
            <div class="d-flex align-center gap-4">
              <VBtn v-can="['Add', 'BatchTask']" @click="createMainDumpTask"> CREATE DUMP TASK </VBtn>
            </div>
          </div>
        </VCardTitle>
      </VCardItem>

      <VCardText class="px-0">
        <VRow>
          <!-- 👉 Search Task Id -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField
                v-model="searchQuery.taskId"  placeholder="Task Id" clearable
                @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Select Task Type -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete v-model="searchQuery.typeInList"  placeholder="Task Type"
                :items="mainTaskTypeList" clearable clear-icon="tabler-x" multiple filterable chips closable-chips
                @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Select Task Status -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete v-model="searchQuery.statusInList" placeholder="Task Status"
                 :items="mainTaskStatusList" clearable clear-icon="tabler-x" multiple filterable chips closable-chips
                 @update:model-value="handleSearch"
            />
          </VCol>

          <!-- 👉 Select Created On   -->
          <VCol cols = "12" sm = "4" xl = "3" xxl = "2">
            <AppDateTimePicker v-model="searchQuery.createdDateTime" :config="{
              mode: 'range',
              enableTime: false,
              dateFormat: 'Y-m-d'
            }" placeholder="Created On" clearable @update:model-value="handleSearch"/>
          </VCol>
        </VRow>
      </VCardText>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer :items="mainTasks" item-value="id"
        :items-length="totalMainTasks" :headers="headers" :loading="isLoading" class="text-no-wrap"
        v-model:expanded="expanded" @click:row="handleRowClick" show-select @update:sort-by="handleSortChange">
        <!-- Expanded-row-->
        <template #expanded-row="{ item }">
          <tr class="expanded-row">
            <td :colspan="3"></td>
            <td :colspan="1">
              <div class="expanded-content" v-if="canExpandBatchName(item) && isExpanded(item)">
                <div class="full-batch-name">
                  <!-- {{ item.batchName }} -->
                  <div>{{ item.batchName }}</div>
                  <VBtn
                    v-if="canCopyToClipboard"
                    size="small"
                    variant="text"
                    class="mt-4 copy-batch-button"
                    prepend-icon="tabler-copy"
                    @click="copyBatchName(item.batchName)"
                  >
                    Copy
                  </VBtn> 
                </div>
              </div>
            </td>
            <td :colspan="2"></td>
            <td :colspan="1">
              <div class="expanded-content">
                <pre class="error-message">{{ item.errorMsg }}</pre>
              </div>
            </td>
            <td :colspan="1">
              <div class="expanded-content">
                <highlightjs language="json" :code="formatJson(item.taskConfig)"/>
              </div>
            </td>
            <td :colspan="6"></td>
          </tr>
        </template>

        <!-- Task ID -->
        <template #[`item.id`]="{ item }">
          <AppText  @click.prevent="viewSubTasks(item)" :auth="['View', 'BatchTask']"
          >{{ item.id }}</AppText>
        </template>

        <!-- Batch Name -->
        <template #[`item.batchName`]="{ item }">
          <div class="d-flex align-center batch-name-cell" data-column="batchName">
            <span>{{ truncateBatchName(item.batchName) }}</span>
            <VIcon
                v-if="canExpandBatchName(item)" 
                :icon="isExpanded(item) ? 'tabler-chevron-up' : 'tabler-chevron-down'"
                size="20" class="ms-2" color="grey-400"
            />
          </div>
        </template>

        <!-- ERROR MESSAGE -->
        <template #[`item.errorMsg`]="{ item }">
          <div class="d-flex align-center w-240px" data-column="errorMsg">
            <span>{{ item.errorMsg ? "Error Detail" : "N/A" }}</span>
            <VIcon
                v-if="canExpandError(item)" :icon="isExpanded(item) ? 'tabler-chevron-up' : 'tabler-chevron-down'"
                size="20" class="ms-2" color="grey-400"
            />
          </div>
        </template>

        <!-- TASK CONFIG -->
        <template #[`item.taskConfig`]="{ item }">
          <div class="d-flex align-center w-270px" data-column="taskConfig">
            <span>{{ item.taskConfig ? "Config Detail" : "N/A" }}</span>
            <VIcon
                v-if="canExpandConfig(item)" :icon="isExpanded(item) ? 'tabler-chevron-up' : 'tabler-chevron-down'"
                size="20" class="ms-2" color="grey-400"
            />
          </div>
        </template>

        <!-- FILE PATH -->
        <template #[`item.finalFilePath`]="{ item }">
          <span>{{ item.finalFilePath || "N/A" }}</span>
        </template>

        <!-- SFTP FILE PATH -->
        <template #[`item.finalSftpFilePath`]="{ item }">
          <span>{{ item.finalSftpFilePath || "N/A" }}</span>
        </template>

        <!-- CREATED ON -->
        <template #[`item.createdDateTime`]="{ item }">
          {{ dateUtil.format(item.createdDateTime) }}
        </template>

        <!-- LAST MODIFIED -->
        <template #[`item.updatedDateTime`]="{ item }">
          {{ dateUtil.format(item.updatedDateTime) }}
        </template>

        <!-- Actions -->
        <template #[`item.actions`]="{ item }">
          <div class="actions-grid">
            <div class="action-item">
              <IconBtn v-if="isStoppableTask(item.status)" v-can="['Edit', 'BatchTask']" class="stop-button"
                       @click.stop="manualStopMainTask(item)" v-tooltip="'Stop Task'">
                <VIcon icon="tabler-square-filled" />
              </IconBtn>
            </div>

            <div class="action-item">
              <IconBtn v-if="isRetryableTask(item.status)" v-can="['Edit', 'BatchTask']" class="retry-button"
                       @click.stop="manualRetryMainTask(item)" v-tooltip="'Retry Task'">
                <VIcon icon="tabler-rotate-clockwise" />
              </IconBtn>
            </div>
          </div>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination
              :page="page"
              :items-per-page="itemsPerPage"
              :total-items="totalMainTasks"
              @update:page="handlePageChange"
          >
            <div class="d-flex gap-3">
              <AppSelect
                  :model-value="itemsPerPage"
                  :items="paginationOptions"
                  @update:model-value="handleItemsPerPageChange"
              />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
  </section>

</template>

<style lang="scss" scoped>
.expanded-content :deep(.hljs) {
  background: transparent;
}

.error-message {
  white-space: pre-wrap;
  word-break: break-word;
  overflow-y: auto;
  background: transparent !important;
  max-width: 225px;
  padding: clamp(2px, calc(0.3em + 1px), 6px);
  margin-bottom: min(1.5%, calc(0.3em + 4px)) !important;
  color: #AAAAAA !important;
  font-family: monospace, monospace !important;
  font-size: 13.5px !important;
  font-weight: 400 !important;
}

.actions-grid {
  display: grid;
  grid-template-columns: 40px 40px;
  gap: 8px;
}

.action-item {
  display: flex;
  justify-content: center;
}

// Add divider style
:deep(.v-theme--dark.v-divider--vertical) {
  height: 22px;
  opacity: 1;
}


// Add icon animation
.v-icon {
  transition: transform 0.3s ease;

  &.rotated {
    transform: rotate(180deg);
  }
}

:deep(.v-data-table) {
  .v-table__wrapper {
    overflow: auto;
  }

  tr:has(.tabler-chevron-up) .v-data-table-column--last-fixed {
    border-bottom: none;
  }

  .v-data-table-column--last-fixed {
    right: 0;
    border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));

  }
}

.stop-button {
  :deep(.v-icon) {
    color: rgba(var(--v-theme-grey-400), 1);
    transition: color 0.2s ease;
  }

  &:hover {
    :deep(.v-icon) {
      color: rgba(var(--v-theme-error), 1);
    }
  }
}

.retry-button {
  :deep(.v-icon) {
    color: rgba(var(--v-theme-grey-400), 1);
    transition: color 0.2s ease, transform 0.5s ease;
  }

  &:hover {
    :deep(.v-icon) {
      color: rgba(var(--v-theme-success), 1);
      transform: rotate(360deg);
    }
  }
}

.batch-name-cell {
  display: flex;
  width: 180px;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  align-items: center;
}

.full-batch-name {
  font-family: monospace, monospace;
  white-space: pre-wrap;
  word-break: break-word;
  color: #AAAAAA;
  font-size: 13.5px;
  font-weight: 400;
  padding: 8px;
}

.copy-batch-button {
  font-family: monospace, monospace;
  font-weight: Semibold;
  
  :deep(.v-icon) {
    color: rgba(var(--v-theme-success), 1.0);
    transition: color 0.2s ease;
  }

  &:hover {
    :deep(.v-icon) {
      color: rgba(var(--v-theme-success), 0.8);
    }
  }
}

</style>