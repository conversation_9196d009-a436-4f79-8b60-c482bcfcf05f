<template>
  <div class="low-code-navigation">
    <!-- 导航栏 -->
    <div class="brand-section">
      <!-- 品牌区域 -->
      <div class="d-flex align-center">
        <div class="brand-icon">
          <VIcon size="20" color="white">mdi-cube-outline</VIcon>
        </div>
        <h2 class="brand-title">Low-Code Platform</h2>
      </div>

      <!-- 导航标签 -->
      <div class="navigation-tabs">
        <VTabs
          v-model="currentTab"
          color="primary"
          slider-color="primary"
          @update:model-value="handleTabChange"
        >
          <VTab
            v-for="tab in navigationTabs"
            :key="tab.value"
            :value="tab.value"
            class="navigation-tab"
          >
            <VIcon class="mr-2" size="18">{{ tab.icon }}</VIcon>
            {{ tab.label }}
          </VTab>
        </VTabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// ===== Props & Emits =====
const props = defineProps({
  modelValue: {
    type: String,
    default: 'dashboard'
  }
})

const emit = defineEmits(['update:modelValue'])

// ===== 状态 =====
const router = useRouter()
const route = useRoute()

// 导航标签配置
const navigationTabs = [
  {
    value: 'scenario',
    label: 'Scenario',
    icon: 'mdi-view-dashboard',
    path: '/low-code-engine'
  },
  {
    value: 'metadata-manager',
    label: 'Metadata Manager',
    icon: 'mdi-database-settings',
    path: '/low-code-engine/meta-manager'
  },
  {
    value: 'index-manager',
    label: 'Index Manager',
    icon: 'mdi-database-search',
    path: '/low-code-engine/index-manager'
  },
  {
    value: 'page-editor',
    label: 'Page Editor',
    icon: 'mdi-pencil',
    path: '/low-code-engine/editor'
  },
]

// 当前标签
const currentTab = ref(props.modelValue)

// ===== 计算属性 =====
const currentTabConfig = computed(() => {
  return navigationTabs.find(tab => tab.value === currentTab.value)
})

// ===== 监听器 =====
watch(() => props.modelValue, (newValue) => {
  currentTab.value = newValue
})

watch(currentTab, (newValue) => {
  emit('update:modelValue', newValue)
})

// 根据路由自动设置当前标签
watch(() => route.path, (newPath) => {
  const matchedTab = navigationTabs.find(tab => 
    newPath === tab.path || 
    (tab.path !== '/low-code-engine' && newPath.startsWith(tab.path))
  )
  if (matchedTab && matchedTab.value !== currentTab.value) {
    currentTab.value = matchedTab.value
  }
}, { immediate: true })

// ===== 方法 =====
function handleTabChange(tabValue) {
  const tabConfig = navigationTabs.find(tab => tab.value === tabValue)
  if (tabConfig && route.path !== tabConfig.path) {
    router.push(tabConfig.path)
  }
}
</script>

<style scoped>
.low-code-navigation {
  background: #1a1a1a;
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid #333;
}

.brand-section {
  background: #1a1a1a;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.brand-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-title {
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  margin: 0;
  margin-left: 12px;
}

.navigation-tabs {
  background: #1a1a1a;
  padding: 0 24px;
  margin-left: auto;
  height: 60px;
}

.navigation-tab {
  text-transform: none;
  font-weight: 500;
  min-height: 44px;
  font-size: 0.9rem;
  padding: 0 20px;
}

:deep(.v-tabs) {
  height: 100%;
  background: transparent;
  border-block-end: none !important;
}

:deep(.v-tab) {
  color: rgba(255, 255, 255, 0.7);
  min-width: auto;
  padding: 0 16px;
}

:deep(.v-tab--selected) {
  color: #4caf50;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 6px;
}

:deep(.v-tabs-slider) {
  display: none;
}

:deep(.v-tab.v-btn) {
  height: 100% !important;
}

</style> 