{"executionRoleArn": "arn:aws:iam::903306222264:role/%ROLE%", "taskRoleArn": "arn:aws:iam::903306222264:role/%ROLE%", "containerDefinitions": [{"logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "%LOG_NAME%", "awslogs-region": "%REGION%", "awslogs-stream-prefix": "%LOG_STREAM%"}}, "portMappings": [{"hostPort": 443, "protocol": "tcp", "containerPort": 443}], "environment": "%ENVIRONMENT%", "secrets": "%SECRET%", "mountPoints": [], "volumesFrom": [], "image": "903306222264.dkr.ecr.%REGION%.amazonaws.com/%REPOSITORY_NAME%:%IMAGE_ENV%_v%BUILD_NUMBER%", "essential": true, "name": "%NAME%"}], "placementConstraints": [], "memory": "%MEMORY%", "family": "%FAMILY%", "requiresCompatibilities": ["FARGATE", "EC2"], "networkMode": "awsvpc", "cpu": "%CPU%", "volumes": []}