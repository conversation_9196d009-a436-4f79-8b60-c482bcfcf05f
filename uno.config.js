// uno.config.ts

import {
  defineConfig,
  presetAttributify,
  presetTypography,
  transformerDirectives,
  transformerVariantGroup,
} from "unocss"

import presetRemToPx from '@unocss/preset-rem-to-px'
import presetWind3 from '@unocss/preset-wind3'

export default defineConfig({
  shortcuts: {
    "flex-center": "flex justify-center items-center",
    "flex-x-center": "flex justify-center",
    "flex-y-center": "flex items-center",
    "wh-full": "w-full h-full",
    "flex-x-between": "flex items-center justify-between",
    "flex-x-end": "flex items-center justify-end",
    "absolute-lt": "absolute left-0 top-0",
    "absolute-rt": "absolute right-0 top-0 ",
    "fixed-lt": "fixed left-0 top-0",
    "b1-red": "b-1 border-solid b-red",
    "text-primary": "text-[#44D62C]",
    'text-error': 'text-[#FD4949]',
    'text-disable': 'text-[#BBBBBB]',
    'border-primary': 'border-1 border-solid border-[#44D62C]',
    'border-error': 'border-1 border-solid border-[#FD4949] rounded'
  },
  rules: [
    // 多行文本隐藏相关类
    [/^line-(\d+)$/, ([_, n]) => ({
      'overflow': 'hidden',
      'text-overflow': 'ellipsis',
      'display': '-webkit-box',
      '-webkit-line-clamp': n,
      '-webkit-box-orient': 'vertical',
      'word-break': 'break-all'
    })],
    [/^w-\[(.+)\]$/, ([_, value]) => ({ width: value })],
    [/^h-\[(.+)\]$/, ([_, value]) => ({ height: value })],
    [/^m-\[(.+)\]$/, ([_, value]) => ({ margin: value })],
    [/^p-\[(.+)\]$/, ([_, value]) => ({ padding: value })],
    [/^mt-\[(.+)\]$/, ([_, value]) => ({ "margin-top": value })],
    [/^mb-\[(.+)\]$/, ([_, value]) => ({ "margin-bottom": value })],
    [/^ml-\[(.+)\]$/, ([_, value]) => ({ "margin-left": value })],
    [/^mr-\[(.+)\]$/, ([_, value]) => ({ "margin-right": value })],
    [/^pt-\[(.+)\]$/, ([_, value]) => ({ "padding-top": value })],
    [/^pb-\[(.+)\]$/, ([_, value]) => ({ "padding-bottom": value })],
    [/^pl-\[(.+)\]$/, ([_, value]) => ({ "padding-left": value })],
    [/^pr-\[(.+)\]$/, ([_, value]) => ({ "padding-right": value })],
    [/^bg-\[(.+)\]$/, ([_, value]) => ({ "background-color": value })],
    [/^text-\[(.+)\]$/, ([_, value]) => ({ "color": value })],
    [/^border-\[(.+)\]$/, ([_, value]) => ({ "border-color": value })],
    [/^border-t-\[(.+)\]$/, ([_, value]) => ({ "border-top-color": value })],
    [/^border-b-\[(.+)\]$/, ([_, value]) => ({ "border-bottom-color": value })],
    [/^border-l-\[(.+)\]$/, ([_, value]) => ({ "border-left-color": value })],
    [/^border-r-\[(.+)\]$/, ([_, value]) => ({ "border-right-color": value })],
    [/^border-\[(.+)\]$/, ([_, value]) => ({ "border-color": value })],
    [/^border-t-\[(.+)\]$/, ([_, value]) => ({ "border-top-color": value })],
    [/^border-b-\[(.+)\]$/, ([_, value]) => ({ "border-bottom-color": value })],
    [/^border-l-\[(.+)\]$/, ([_, value]) => ({ "border-left-color": value })],  
  ],
  theme: {
    colors: {
    },
  },
  presets: [
    presetWind3(),
    presetAttributify(),
    presetTypography(),
       // unocss 默认rem，转成px
       presetRemToPx({
        baseFontSize: 16,
    })
  ],
  transformers: [transformerDirectives(), transformerVariantGroup()],
})


