/**
 * 分页组件相关hooks
 */

import { PAGINATION_COMPONENTS } from '../editor/pagination.js'

/**
 * 使用标准TablePagination组件的hook
 * @returns {Object} 分页配置
 */
export function useTablePagination() {
  return {
    paginationComponent: PAGINATION_COMPONENTS.TABLE_PAGINATION
  }
}

/**
 * 使用低代码专用TablePaginationLowCode组件的hook
 * @returns {Object} 分页配置
 */
export function useTablePaginationLowCode() {
  return {
    paginationComponent: PAGINATION_COMPONENTS.TABLE_PAGINATION_LOW_CODE
  }
}

/**
 * 自定义数据绑定的分页hook
 * @param {Object} dataBinding - 自定义数据绑定映射
 * @returns {Object} 分页配置
 */
export function useCustomDataBinding(dataBinding = {}) {
  return {
    paginationComponent: PAGINATION_COMPONENTS.TABLE_PAGINATION,
    paginationConfig: {
      dataBinding: {
        page: 'page',
        itemsPerPage: 'itemsPerPage', 
        totalItems: 'totalCount',
        ...dataBinding
      }
    }
  }
}

/**
 * 带额外Props的分页hook
 * @param {Object} extraProps - 额外的Props配置
 * @returns {Object} 分页配置
 */
export function useCustomPaginationProps(extraProps = {}) {
  return {
    paginationComponent: PAGINATION_COMPONENTS.TABLE_PAGINATION,
    paginationConfig: {
      extraProps: {
        color: 'primary',
        showFirstLastPage: true,
        totalVisible: 7,
        ...extraProps
      }
    }
  }
}

/**
 * 创建分页配置的通用hook
 * @param {Object} options - 分页选项
 * @param {string} options.component - 分页组件类型
 * @param {Object} options.dataBinding - 数据绑定配置
 * @param {Object} options.extraProps - 额外Props
 * @returns {Object} 分页配置
 */
export function usePaginationConfig(options = {}) {
  const {
    component = PAGINATION_COMPONENTS.TABLE_PAGINATION,
    dataBinding = {},
    extraProps = {}
  } = options

  return {
    paginationComponent: component,
    paginationConfig: {
      dataBinding: {
        page: 'page',
        itemsPerPage: 'itemsPerPage',
        totalItems: 'totalCount',
        ...dataBinding
      },
      extraProps
    }
  }
}

/**
 * 获取推荐的分页配置
 * 这是最常用的配置，推荐在大多数场景使用
 * @returns {Object} 推荐的分页配置
 */
export function useRecommendedPagination() {
  return useTablePagination()
}

// 预设配置示例
export const PAGINATION_PRESETS = {
  // 标准配置
  standard: useTablePagination(),
  
  // 低代码配置
  lowCode: useTablePaginationLowCode(),
  
  // 自定义用户数据绑定
  userList: useCustomDataBinding({
    totalItems: 'totalUsers'
  }),
  
  // 带样式的分页
  styled: useCustomPaginationProps({
    color: 'success',
    totalVisible: 10
  })
} 