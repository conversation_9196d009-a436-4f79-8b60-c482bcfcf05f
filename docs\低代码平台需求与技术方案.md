# 低代码平台需求与技术方案

## 一、需求分析

### 1. 目标用户
- 普通前端开发者（有一定Vue基础）
- 希望提升页面开发效率，减少重复劳动
- 需要导出高质量、可维护的Vue3 + Vuetify + unocss代码

### 2. 核心功能
- 拖拽式页面搭建（支持常用表单、表格、布局、按钮等Vuetify组件）
- 属性、事件、样式可视化配置
- 元数据实时预览与编辑
- 一键导出Vue3 + Vuetify + unocss代码到本地指定目录
- 支持导出的代码可直接在现有项目中二次开发
- 支持数据源绑定、API联调
- 支持页面/组件级权限配置（可选）

### 3. 非功能需求
- 导出的代码风格、目录结构与现有项目保持一致
- 生成代码可读性强，易于维护
- 平台本身易于扩展，支持自定义组件

---

## 二、技术选型
- 前端框架：Vue3（Composition API）
- UI组件库：Vuetify（与现有项目一致）
- 原子化CSS：unocss
- 状态管理：Pinia（推荐，轻量且与Vue3兼容）
- 拖拽库：vue-draggable-next、sortablejs等
- 代码生成：自定义模板引擎（如ejs、handlebars等）
- 后端：Node.js + Express/Koa（负责元数据存储、API代理等）
- 数据库：MongoDB（用于存储元数据）

---

## 三、开发步骤

1. **元数据结构设计**
   - 设计能描述Vuetify组件树、属性、事件、样式的JSON结构
   - 兼容unocss样式描述

2. **可视化搭建器开发**
   - 拖拽式组件面板（支持Vuetify常用组件）
   - 属性/事件/样式配置面板
   - 实时预览区

3. **元数据渲染引擎**
   - 根据元数据动态渲染Vue3 + Vuetify组件
   - 支持unocss样式应用

4. **后端API开发**
   - 元数据的增删改查
   - 数据源API代理
   - 文件上传（如有需要）

5. **代码生成与导出**
   - 将元数据转为Vue3 + Vuetify + unocss代码
   - 支持导出到本地指定目录，目录结构与现有项目一致
   - 可选：支持导出为单文件组件（.vue）或多文件结构

6. **联调与测试**
   - 搭建器与后端API联调
   - 导出代码在现有项目中测试可用性

7. **文档与培训**
   - 编写平台使用文档
   - 培训开发者如何使用低代码平台和导出的代码

---

## 四、整体流程图

```mermaid
graph TD
A[开发者拖拽搭建页面] --> B[生成/编辑元数据]
B --> C[前端调用后端API保存元数据]
C --> D[后端存储元数据到数据库]
B --> E[实时预览/调试]
B --> F[一键导出Vue3+Vuetify+unocss代码]
F --> G[代码保存到本地项目目录]
G --> H[开发者二次开发/集成]
```

---

## 五、建议
- 元数据结构要兼容现有项目的组件和样式体系，便于导出代码后无缝集成。
- 代码生成模块要灵活，支持自定义模板，方便后续升级或适配不同项目规范。
- 可视化搭建器建议先支持常用组件，后续逐步扩展。
- 导出代码前可提供"预览代码"功能，让开发者提前看到生成效果。

---

## 六、主流低代码元数据结构设计方案（以用户模块为例）

### 1. 设计要点

1. 组件树结构：页面由组件树描述，支持嵌套、递归。
2. 属性配置：每个组件有独立的props，支持UI属性、校验、数据绑定等。
3. 事件与动作：支持事件（如onClick）与动作（如API调用、页面跳转）。
4. 数据源绑定：支持组件与后端API、静态数据、变量等数据源绑定。
5. 权限与可见性：支持组件级权限、条件渲染。
6. 样式与布局：支持class、style、unocss等灵活样式配置。
7. 页面级元信息：如页面标题、路由、权限等。

### 2. 页面元数据结构示例

```json
{
  "id": "user-list-page",
  "name": "用户管理",
  "path": "/user/list",
  "title": "用户列表",
  "permission": ["admin", "user_manager"],
  "layout": "default",
  "components": [
    // 组件树
  ]
}
```

### 3. 组件树结构示例（表格+搜索表单+操作按钮）

```json
{
  "type": "VContainer",
  "props": {
    "fluid": true
  },
  "children": [
    {
      "type": "VCard",
      "props": {},
      "children": [
        {
          "type": "VCardTitle",
          "props": {},
          "children": [
            {
              "type": "VText",
              "props": { "text": "用户列表" }
            },
            {
              "type": "VSpacer"
            },
            {
              "type": "VBtn",
              "props": {
                "color": "primary",
                "text": "新建用户"
              },
              "events": {
                "click": [
                  { "action": "openDialog", "params": { "dialogId": "user-create-dialog" } }
                ]
              },
              "permission": ["admin"]
            }
          ]
        },
        {
          "type": "VCardText",
          "children": [
            {
              "type": "VForm",
              "props": { "inline": true },
              "children": [
                {
                  "type": "VTextField",
                  "props": {
                    "label": "用户名",
                    "model": "search.username"
                  }
                },
                {
                  "type": "VBtn",
                  "props": {
                    "color": "primary",
                    "text": "搜索"
                  },
                  "events": {
                    "click": [
                      { "action": "callApi", "params": { "apiId": "user-list" } }
                    ]
                  }
                }
              ]
            },
            {
              "type": "VDataTable",
              "props": {
                "items": "{{userList}}",
                "headers": [
                  { "text": "用户名", "value": "username" },
                  { "text": "邮箱", "value": "email" },
                  { "text": "角色", "value": "role" },
                  { "text": "操作", "value": "actions", "sortable": false }
                ]
              },
              "children": [
                {
                  "type": "template",
                  "slot": "item.actions",
                  "children": [
                    {
                      "type": "VBtn",
                      "props": { "icon": "mdi-pencil" },
                      "events": {
                        "click": [
                          { "action": "openDialog", "params": { "dialogId": "user-edit-dialog", "rowData": "{{item}}" } }
                        ]
                      }
                    },
                    {
                      "type": "VBtn",
                      "props": { "icon": "mdi-delete", "color": "red" },
                      "events": {
                        "click": [
                          { "action": "callApi", "params": { "apiId": "user-delete", "rowData": "{{item}}" } }
                        ]
                      },
                      "permission": ["admin"]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 4. 数据源与API描述示例

```json
{
  "apis": [
    {
      "id": "user-list",
      "type": "http",
      "method": "GET",
      "url": "/api/user/list",
      "params": {
        "username": "{{search.username}}"
      },
      "responseMapping": {
        "userList": "data.list"
      }
    },
    {
      "id": "user-delete",
      "type": "http",
      "method": "POST",
      "url": "/api/user/delete",
      "params": {
        "userId": "{{rowData.id}}"
      }
    }
  ]
}
```

### 5. 变量与状态描述示例

```json
{
  "variables": {
    "search": {
      "username": ""
    },
    "userList": []
  }
}
```

### 6. 结构说明与扩展性

- **type**：组件类型（与Vuetify组件一一对应）
- **props**：组件属性（支持动态绑定，如`{{变量}}`）
- **children**：子组件数组，支持递归嵌套
- **events**：事件与动作绑定，支持多种动作（API调用、弹窗、跳转等）
- **permission**：权限控制，支持数组（角色/权限码）
- **dataSource/items**：数据绑定，支持变量、API、静态数据
- **slot/template**：插槽支持，满足复杂表格、卡片等场景
- **apis/variables**：页面级API与变量声明，便于统一管理

### 7. 如何覆盖所有场景

1. 表单、表格、弹窗、布局等常用组件均可描述
2. 支持事件驱动与多种动作类型
3. 支持复杂嵌套、插槽、动态渲染
4. 支持权限、条件渲染、样式灵活配置
5. 支持多数据源、变量、API联动

### 8. 总结

- 该元数据结构兼容主流低代码平台设计思想，适配Vue3 + Vuetify + unocss项目
- 具备良好的扩展性和可维护性
- 可根据实际业务场景灵活扩展（如流程、审批、图表等） 