import { isEmpty, isNullOrUndefined, isEmptyArray, isObject, isArray, isToday, debounce, isPlainObject, listToTree, treeToList } from '@/utils/helpers';

describe('helpers', () => {
  test('isEmpty checks for empty values', () => {
    expect(isEmpty(null)).toBe(true);
    expect(isEmpty('')).toBe(true);
    expect(isEmpty([])).toBe(true);
    expect(isEmpty({})).toBe(false);
    expect(isEmpty(0)).toBe(false); // 0 is not empty
    expect(isEmpty(false)).toBe(false); // false is not empty
  });

  test('isNullOrUndefined checks for null or undefined', () => {
    expect(isNullOrUndefined(null)).toBe(true);
    expect(isNullOrUndefined(undefined)).toBe(true);
    expect(isNullOrUndefined('')).toBe(false);
    expect(isNullOrUndefined(0)).toBe(false);
  });

  test('isEmptyArray checks for empty arrays', () => {
    expect(isEmptyArray([])).toBe(true);
    expect(isEmptyArray([1])).toBe(false);
    expect(isEmptyArray([null])).toBe(false); // Array with null is not empty
  });

  test('isObject checks for objects', () => {
    expect(isObject({})).toBe(true);
    expect(isObject([])).toBe(false);
    expect(isObject(null)).toBe(false); // null is not an object
    expect(isObject(42)).toBe(false); // number is not an object
  });

  test('isArray checks for arrays', () => {
    expect(isArray([])).toBe(true);
    expect(isArray({})).toBe(false);
    expect(isArray(null)).toBe(false); // null is not an array
    expect(isArray(42)).toBe(false); // number is not an array
  });

  test('isToday checks if date is today', () => {
    const today = new Date();
    expect(isToday(today)).toBe(true);
    
    const yesterday = new Date();
    yesterday.setDate(today.getDate() - 1);
    expect(isToday(yesterday)).toBe(false);
    
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);
    expect(isToday(tomorrow)).toBe(false);
  });


  test('debounce function works correctly', () => {
    vi.useFakeTimers();
    const mockFn = vi.fn();
    const debouncedFn = debounce(mockFn, 1000);

    debouncedFn();
    debouncedFn();
    expect(mockFn).not.toBeCalled();

    vi.advanceTimersByTime(1000);
    expect(mockFn).toBeCalledTimes(1);
  });

  test('isPlainObject checks for plain objects', () => {
    expect(isPlainObject({})).toBe(true);
    expect(isPlainObject(new Date())).toBe(false);
    expect(isPlainObject([])).toBe(false);
    expect(isPlainObject(null)).toBe(false);
  });

  test('listToTree converts list to tree structure', () => {
    const list = [
      { id: 1, parentId: null, name: 'Node 1' },
      { id: 2, parentId: 1, name: 'Node 1.1' },
      { id: 3, parentId: 1, name: 'Node 1.2' },
      { id: 4, parentId: null, name: 'Node 2' },
    ];
    const tree = listToTree(list);
    expect(tree).toEqual([
      {
        id: 1,
        parentId: null,
        name: 'Node 1',
        children: [
          { id: 2, parentId: 1, name: 'Node 1.1', children: [] },
          { id: 3, parentId: 1, name: 'Node 1.2', children: [] },
        ],
      },
      { id: 4, parentId: null, name: 'Node 2', children: [] },
    ]);
  });

  test('treeToList converts tree structure to list', () => {
    const tree = [
      {
        id: 1,
        parentId: null,
        name: 'Node 1',
        children: [
          { id: 2, parentId: 1, name: 'Node 1.1', children: [] },
          { id: 3, parentId: 1, name: 'Node 1.2', children: [] },
        ],
      },
      { id: 4, parentId: null, name: 'Node 2', children: [] },
    ];
    const list = treeToList(tree);
    console.log(list); // 打印实际输出
    expect(list).toEqual([
      { id: 1, parentId: null, name: 'Node 1' },
      { id: 2, parentId: 1, name: 'Node 1.1' },
      { id: 3, parentId: 1, name: 'Node 1.2' },
      { id: 4, parentId: null, name: 'Node 2' },
    ]);
  });
}); 