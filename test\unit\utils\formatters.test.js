import { avatarText, kFormatter, formatDate, formatDateToMonthShort, prefixWithPlus } from '@/utils/formatters';

describe('formatters', () => {
  test('avatarText returns correct initials', () => {
    expect(avatarText('<PERSON>e')).toBe('JD');
    expect(avatarText('')).toBe('');
  });

  test('kFormatter formats numbers correctly', () => {
    expect(kFormatter(10000)).toBe('10k');
    expect(kFormatter(999)).toBe('999');
  });

  test('formatDate formats date correctly', () => {
    expect(formatDate('2023-01-01')).toBe('Jan 1, 2023');
  });

  test('formatDateToMonthShort formats date correctly', () => {
    expect(formatDateToMonthShort('2023-01-01')).toBe('Jan 1');
  });

  test('prefixWithPlus adds plus sign correctly', () => {
    expect(prefixWithPlus(5)).toBe('+5');
    expect(prefixWithPlus(-5)).toBe(-5);
  });
}); 