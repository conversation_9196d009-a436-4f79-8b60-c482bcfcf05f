# 低代码组件隔离方案

## 问题描述

在低代码平台开发中，通用组件如果直接注入低代码上下文，会导致：
- 组件复用性降低
- 组件职责不清晰
- 非低代码环境下难以使用
- 测试复杂度增加

## 解决方案

### 方案1：组合式函数 + 包装组件（推荐）

#### 1. 创建低代码数据绑定 Hook

`src/hooks/useLowCodeData.js`：提供统一的低代码上下文数据访问接口

```javascript
import { inject, computed } from 'vue'

export function useLowCodeData(fallbackProps = {}) {
  const renderContext = inject('lowCodeRenderContext', null)
  
  const useReactiveData = (key, fallbackValue) => {
    return computed(() => {
      if (renderContext?.data && renderContext.data.hasOwnProperty(key)) {
        return renderContext.data[key]
      }
      return fallbackValue
    })
  }

  const useReactiveDataBatch = (mapping) => {
    const result = {}
    for (const [key, fallbackValue] of Object.entries(mapping)) {
      result[key] = useReactiveData(key, fallbackValue)
    }
    return result
  }

  return {
    renderContext,
    useReactiveData,
    useReactiveDataBatch,
    isLowCodeContext: !!renderContext
  }
}
```

#### 2. 保持原组件纯净

`src/components/TablePagination.vue`：移除所有低代码相关逻辑

```vue
<script setup>
import { watch } from 'vue'

const props = defineProps({
  page: { type: Number, required: true },
  itemsPerPage: { type: Number, required: true },
  totalItems: { type: Number, required: true }
})

const emit = defineEmits(['update:page'])

// 纯净的组件逻辑，只依赖 props
const updatePage = value => emit('update:page', value)
</script>
```

#### 3. 创建低代码包装组件

`src/components/TablePaginationLowCode.vue`：专门处理低代码数据绑定

```vue
<script setup>
import { computed } from 'vue'
import TablePagination from './TablePagination.vue'
import { useLowCodeData } from '@/hooks/useLowCodeData'

const props = defineProps({
  page: { type: Number, required: true },
  itemsPerPage: { type: Number, required: true },
  totalItems: { type: Number, required: true },
  dataMapping: {
    type: Object,
    default: () => ({
      page: 'page',
      itemsPerPage: 'itemsPerPage', 
      totalItems: 'totalUsers'
    })
  }
})

const emit = defineEmits(['update:page'])

// 使用低代码数据绑定
const { useReactiveDataBatch } = useLowCodeData()

const reactiveData = useReactiveDataBatch({
  [props.dataMapping.page]: props.page,
  [props.dataMapping.itemsPerPage]: props.itemsPerPage,
  [props.dataMapping.totalItems]: props.totalItems
})

const finalPage = computed(() => reactiveData[props.dataMapping.page].value)
const finalItemsPerPage = computed(() => reactiveData[props.dataMapping.itemsPerPage].value)
const finalTotalItems = computed(() => reactiveData[props.dataMapping.totalItems].value)

const handlePageUpdate = (newPage) => emit('update:page', newPage)
</script>

<template>
  <TablePagination
    :page="finalPage"
    :items-per-page="finalItemsPerPage"
    :total-items="finalTotalItems"
    @update:page="handlePageUpdate"
  >
    <template #default>
      <slot />
    </template>
  </TablePagination>
</template>
```

#### 4. 渲染器自动组件映射

`src/business/low-code-engine/renderer/utils.js`：在渲染器中自动使用包装版本

```javascript
export const getComponentByName = (name, context) => {
  // 低代码环境组件映射
  const lowCodeComponentMap = {
    'TablePagination': 'TablePaginationLowCode',
    // 可扩展更多组件
  }
  
  // 优先使用低代码包装版本
  if (lowCodeComponentMap[name] && dynamicCustomComponentMap[lowCodeComponentMap[name]]) {
    return dynamicCustomComponentMap[lowCodeComponentMap[name]]
  }
  
  // 常规组件查找逻辑...
}
```

## 优势

### 1. 组件职责清晰
- **原组件**：专注业务逻辑，纯 props 驱动
- **包装组件**：专门处理低代码数据绑定
- **Hook**：提供可复用的低代码数据访问能力

### 2. 高度可复用
- 原组件可在任何环境使用
- 包装组件专用于低代码环境
- Hook 可用于其他需要低代码支持的组件

### 3. 易于测试
- 原组件测试简单，只需测试 props 和 events
- 包装组件可单独测试数据绑定逻辑
- 低代码渲染器可测试组件映射逻辑

### 4. 易于维护
- 关注点分离，修改不同逻辑时影响范围有限
- 新增低代码支持的组件只需创建对应包装组件
- 渲染器配置简单，易于扩展

## 使用方式

### 普通Vue页面中使用

```vue
<template>
  <TablePagination
    :page="currentPage"
    :items-per-page="pageSize"
    :total-items="totalCount"
    @update:page="handlePageChange"
  />
</template>
```

### 低代码元数据中使用

```json
{
  "type": "TablePagination",
  "props": {
    "page": "{{page}}",
    "itemsPerPage": "{{itemsPerPage}}",
    "totalItems": "{{totalUsers}}"
  },
  "events": {
    "update:page": [{ "action": "handlePageChange" }]
  }
}
```

渲染器会自动将 `TablePagination` 替换为 `TablePaginationLowCode`，实现响应式数据绑定。

## 扩展新组件

1. 创建包装组件 `ComponentNameLowCode.vue`
2. 在 `lowCodeComponentMap` 中添加映射关系
3. 配置数据映射规则

这样既保证了组件的纯净性，又满足了低代码平台的需求。 