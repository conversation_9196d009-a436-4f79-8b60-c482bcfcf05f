export const usePin = () => {
    const razerGoldPINGroups = ref([])
    const statuses = ref([])
    const region = ref({})
    const loadData = async () => {
    const res = await $api('/api/admin-api/v1/pin-generate-request/config', {
        method: 'GET',
    })
    razerGoldPINGroups.value = Array.isArray(res.data.razerGoldPINGroups) 
        ? res.data.razerGoldPINGroups.map(item => ({
            ...item,
            title: item.name,
            value: item.id
          }))
        : [];
    
      region.value = Array.isArray(res.data.razerGoldPINGroups) ?  res.data.razerGoldPINGroups.reduce((acc, item) => ({...acc, [item.id]: item.name}), {}) : {}
      statuses.value = res.data?.statuses || []
    }
    onMounted(async () => {
      await loadData()
    })
    return { razerGoldPINGroups, statuses, region }
  }