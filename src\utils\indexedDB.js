import { dateUtil } from "./day";

/**
 * IndexedDBManager
 * 用于简化 IndexedDB 的常用操作
 *
 * @example
 * // 创建实例
 * const db = new IndexedDBManager('testDB', 'testStore');
 * // 新增数据
 * db.add({ name: '张三', age: 20 }).then(id => console.log('新增id:', id));
 * // 查询所有
 * db.getAll().then(list => console.log('所有数据:', list));
 * // 更新
 * db.put(1, { age: 21 });
 * // 删除
 * db.delete(1);
 * // 分页
 * db.getPage(1, 5, 'createdAt').then(list => console.log(list));
 */
class IndexedDBManager {
  /**
   * 构造函数，初始化数据库名和对象仓库名
   * @param {string} dbName 数据库名
   * @param {string} storeName 对象仓库名
   * @param {string} keyPath 主键路径
   * @param {Function} callback 回调函数，用于创建索引
   */
  constructor(dbName, storeName, keyPath, callback) {
    this.dbName = dbName;
    this.storeName = storeName;
    this.db = null;
    this.version = 2; // 数据库版本号
    this.keyPath = keyPath || 'id';
    this.callback = callback;
  }

  /**
   * 初始化数据库，创建对象仓库和索引（如不存在）
   * @returns {Promise<IDBDatabase>}
   */
  async init() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);
      request.onupgradeneeded = (event) => {
        this.db = event.target.result;
        if (!this.db.objectStoreNames.contains(this.storeName)) {
          const store = this.db.createObjectStore(this.storeName, { keyPath: this.keyPath, autoIncrement: true });
          // 创建索引
       
          store.createIndex('createdAt', 'createdAt', { unique: false });
          this.callback && this.callback(store);
        }
      };
      request.onsuccess = (event) => {
        this.db = event.target.result;
        resolve(this.db);
      };
      request.onerror = (event) => {
        reject(event.target.error);
      };
    });
  }

  /**
   * 确保数据库已初始化
   * @returns {Promise<IDBDatabase>}
   */
  async ensureDB() {
    if (!this.db) {
      await this.init();
    }
    return this.db;
  }

  /**
   * 新增一条数据
   * @param {Object} data 要添加的数据
   * @param {Function} [callback] 可选回调
   * @returns {Promise<number>} 新增数据的主键id
   * @example
   * db.add({ name: '张三', age: 20 }).then(id => console.log(id));
   */
  async add(data, callback) {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(this.storeName, 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.add({ ...data, createdAt: dateUtil.formatToUTC8(new Date()) });
      request.onsuccess = () => {
        if (callback) callback(null, request.result);
        resolve(request.result)
      }
      request.onerror = (event) => {
        if (callback) callback(event.target.error, null);
        reject(event.target.error)
      }
    });
  }

  /**
   * 更新一条数据（根据id）
   * @param {number} id 主键id
   * @param {Object} data 要更新的数据
   * @param {Function} [callback] 可选回调
   * @returns {Promise<number>}
   * @example
   * db.put(1, { age: 21 }).then(id => console.log(id));
   */
  async put(id, data, callback) { 
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => { 
      const transaction = db.transaction(this.storeName, 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.get(id);
      request.onsuccess = (event) => {
        const existingData = event.target.result;
        if (existingData) {
          const newData = { ...existingData, ...data, id};
          const putRequest = store.put(newData);
          putRequest.onsuccess = () => {
            if (callback) callback(null, putRequest.result);
            resolve(putRequest.result);
          };
          putRequest.onerror = (event) => {
            if (callback) callback(event.target.error, null);
            reject(event.target.error);
          };
        } else {
          const err = new Error(`No data found with id ${id}`);
          if (callback) callback(err, null);
          reject(err);
        }
      };
      request.onerror = (event) => {
        if (callback) callback(event.target.error, null);
        reject(event.target.error);
      };
    });
  }

  /**
   * 根据主键id获取一条数据
   * @param {number} id 主键id
   * @param {Function} [callback] 可选回调
   * @returns {Promise<Object>}
   * @example
   * db.get(1).then(data => console.log(data));
   */
  async get(id, callback) {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(this.storeName, 'readonly');
      const store = transaction.objectStore(this.storeName);
      const request = store.get(id);
      request.onsuccess = () => {
        if (callback) callback(null, request.result);
        resolve(request.result);
      }
      request.onerror = (event) => {
        if (callback) callback(event.target.error, null);
        reject(event.target.error);
      }
    });
  }

  /**
   * 根据主键id删除一条数据
   * @param {number} id 主键id
   * @param {Function} [callback] 可选回调
   * @returns {Promise<number>}
   * @example
   * db.delete(1).then(() => console.log('已删除'));
   */
  async delete(id, callback) {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(this.storeName, 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.delete(id);
      request.onsuccess = () => {
        if (callback) callback(null, request.result);
        resolve(request.result);
      }
      request.onerror = (event) => {
        if (callback) callback(event.target.error, null);
        reject(event.target.error);
      }
    });
  }

  /**
   * 获取所有数据
   * @param {Function} [callback] 可选回调
   * @returns {Promise<Array>}
   * @example
   * db.getAll().then(list => console.log(list));
   */
  getAll(callback) {
    return new Promise((resolve, reject) => {
      this.ensureDB().then(db => {
        const transaction = db.transaction(this.storeName, 'readonly');
        const store = transaction.objectStore(this.storeName);
        const request = store.getAll();
        request.onsuccess = () => {
          if (callback) callback(null, request.result);
          resolve(request.result);
        }
        request.onerror = (event) => {
          if (callback) callback(event.target.error, null);
          reject(event.target.error);
        }
      }).catch(reject);
    });
  }

  /**
   * 清空对象仓库
   * @param {Function} [callback] 可选回调
   * @returns {Promise<undefined>}
   * @example
   * db.clear().then(() => console.log('已清空'));
   */
  clear(callback) {
    return new Promise((resolve, reject) => {
      this.ensureDB().then(db => {
        const transaction = db.transaction(this.storeName, 'readwrite');
        const store = transaction.objectStore(this.storeName);
        const request = store.clear();
        request.onsuccess = () => {
          if (callback) callback(null, request.result);
          resolve(request.result);
        }
        request.onerror = (event) => {
          if (callback) callback(event.target.error, null);
          reject(event.target.error);
        }
      }).catch(reject);
    });
  }

  /**
   * 通过索引字段查询单条数据
   * @param {string} indexName 索引名
   * @param {*} value 索引值
   * @param {Function} [callback] 可选回调
   * @returns {Promise<Object>}
   * @example
   * db.getByIndex('createdAt', 1720425600000).then(data => console.log(data));
   */
  async getByIndex(indexName, value, callback) {  
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => { 
      const transaction = db.transaction([this.storeName], "readonly");
      const store = transaction.objectStore(this.storeName);
      const index = store.index(indexName);
      const request = index.get(value);
      request.onsuccess = function (event) { 
        if (callback) callback(null, event.target.result);
        resolve(event.target.result);
      };
      request.onerror = function (event) { 
        if (callback) callback(event.target.error, null);
        reject(event.target.error);
      };
    });
  }

  /**
   * 分页查询
   * @param {number} page 页码（从1开始）
   * @param {number} pageSize 每页条数
   * @param {string} [indexName] 可选，索引名（如按createdAt排序分页）
   * @param {IDBKeyRange} [range] 可选，范围查询
   * @param {Function} [callback] 可选，回调
   * @returns {Promise<Array>}
   * @example
   * db.getPage(1, 5, 'createdAt').then(list => console.log(list));
   */
  getPage(page = 1, pageSize = 10, indexName, range, callback) {
    return new Promise(async (resolve, reject) => {
      try {
        const db = await this.ensureDB();
        const transaction = db.transaction(this.storeName, 'readonly');
        const store = transaction.objectStore(this.storeName);
        const source = indexName ? store.index(indexName) : store;
        const result = [];
        let skipped = 0;
        let got = 0;
        const offset = (page - 1) * pageSize;
        const request = source.openCursor(range);

        request.onsuccess = function (event) {
          const cursor = event.target.result;
          if (cursor && got < pageSize) {
            if (skipped < offset) {
              skipped++;
              cursor.continue();
            } else {
              result.push(cursor.value);
              got++;
              cursor.continue();
            }
          } else {
            if (callback) callback(null, result);
            resolve(result);
          }
        };
        request.onerror = function (event) {
          if (callback) callback(event.target.error, null);
          reject(event.target.error);
        };
      } catch (err) {
        if (callback) callback(err, null);
        reject(err);
      }
    });
  }

  /**
   * 批量操作（批量add/put/delete）
   * @param {Array} data 数据数组
   * @param {string} operate 操作类型："add"|"put"|"delete"
   * @param {Function} [callback] 可选回调
   * @returns {Promise<Array>}
   * @example
   * db.batch([{name:'a'},{name:'b'}], 'add').then(res => console.log(res));
   */
  async batch(data, operate = 'add', callback) { 
    if (!Array.isArray(data)) {
      return Promise.reject(new Error('Data must be an array'));
    }
    if (!['add', 'put', 'delete'].includes(operate)) {
      return Promise.reject(new Error('Invalid operation type. Use "add", "put", or "delete".'));
    }
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => { 
      const transaction = db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const results = [];
      data.forEach(item => {
        const request = store[operate](item);
        request.onsuccess = function (event) {
          results.push(event.target.result);
        };
        request.onerror = function (event) {
          results.push(event.target.error);
        };
      });
      transaction.oncomplete = () => {
        if (callback) callback(null, results);
        resolve(results);
      };
      transaction.onerror = (event) => {
        if (callback) callback(event.target.error, null);
        reject(event.target.error);
      };
    });
  }

  /**
   * 批量新增
   * @param {Array} data 数据数组
   * @param {Function} [callback] 可选回调
   * @returns {Promise<Array>}
   * @example
   * db.batchAdd([{name:'a'},{name:'b'}]).then(res => console.log(res));
   */
  batchAdd(data, callback) {
    return this.batch(data, 'add', callback);
  }

  /**
   * 批量更新
   * @param {Array} data 数据数组
   * @param {Function} [callback] 可选回调
   * @returns {Promise<Array>}
   * @example
   * db.batchPut([{id:1, name:'a'}]).then(res => console.log(res));
   */
  batchPut(data, callback) {
    return this.batch(data, 'put', callback);
  }

  /**
   * 批量删除
   * @param {Array} data 主键数组
   * @param {Function} [callback] 可选回调
   * @returns {Promise<Array>}
   * @example
   * db.batchDelete([1,2,3]).then(res => console.log(res));
   */
  batchDelete(data, callback) {
    return this.batch(data, 'delete', callback);
  }

  /**
   * 关闭数据库连接
   * @example
   * db.close();
   */
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }

  /**
   * 删除整个数据库
   * @returns {Promise<void>}
   * @example
   * db.deleteDatabase().then(() => console.log('数据库已删除'));
   */
  async deleteDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.deleteDatabase(this.dbName);
      request.onsuccess = () => {
        this.db = null;
        resolve();
      };
      request.onerror = (event) => {
        reject(event.target.error);
      };
    });
  }
}

export default IndexedDBManager; 