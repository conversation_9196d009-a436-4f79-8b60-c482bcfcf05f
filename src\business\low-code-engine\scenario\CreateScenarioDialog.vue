<template>
  <VDialog v-model="isDialogVisible" max-width="700px" persistent>
    <VCard>
      <VCardTitle>
        <div class="d-flex justify-space-between align-center">
          <span class="text-h5">Create New Scenario</span>
          <VBtn icon variant="text" size="small" @click="closeDialog">
            <VIcon>mdi-close</VIcon>
          </VBtn>
        </div>
      </VCardTitle>

      <VCardText>
        <VForm ref="form" @submit.prevent="handleSubmit">
          <VRow>
            <VCol cols="12">
              <VTextField v-model="formData.scenario" label="Scenario Name*" :rules="[v => !!v || 'Name is required']"
                placeholder="e.g., Customer Relationship Management" />
            </VCol>

            <!-- ID will be auto-generated by backend -->

            <VCol cols="12">
              <VTextarea v-model="formData.description" label="Description*" rows="3"
                :rules="[v => !!v || 'Description is required']"
                placeholder="A brief description of the business purpose of this scenario" />
            </VCol>

            <VCol cols="12" md="6">
              <VMenu v-model="iconMenuVisible" :close-on-content-click="false" max-width="400px" offset="4"
                transition="scale-transition" :class="{ 'icon-menu-open': iconMenuVisible }">
                <template #activator="{ props }">
                  <VTextField v-bind="props" :model-value="selectedIconDisplay" label="Icon" readonly clearable
                    @click:clear="clearIcon" placeholder="Select an icon..." class="icon-selector-input"
                    append-inner-icon="mdi-chevron-down">
                    <template #prepend-inner v-if="formData.icon">
                      <VIcon :icon="formData.icon" size="20" class="mr-2" />
                    </template>
                  </VTextField>
                </template>

                <VCard class="icon-selector-card">
                  <VCardText class="pa-2">
                    <VTextField v-model="iconSearchText" label="Search icons..." prepend-inner-icon="mdi-magnify"
                      variant="outlined" density="compact" clearable class="mb-2" />

                    <div class="icon-selector-container">
                      <template v-for="group in filteredGroupedIcons" :key="group.name">
                        <div class="icon-group">
                          <div class="icon-group-title">
                            {{ group.name }}
                          </div>
                          <div class="icon-grid">
                            <div v-for="icon in group.icons" :key="icon.value" class="icon-item"
                              :class="{ 'icon-item-selected': formData.icon === icon.value }" @click="selectIcon(icon)">
                              <VIcon :icon="icon.value" size="24" />
                              <div class="icon-name">{{ icon.title }}</div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                  </VCardText>
                </VCard>
              </VMenu>
            </VCol>

            <VCol cols="12" md="6">
              <VSelect v-model="formData.color" :items="colorOptions" label="Color">
                <template #prepend-inner>
                  <VAvatar v-if="formData.color" :color="formData.color" size="12" class="mr-1" />
                  <VIcon v-else icon="mdi-palette" />
                </template>
                <template #item="{ props, item }">
                  <VListItem v-bind="props">
                    <template #prepend>
                      <VAvatar :color="item.raw" size="20" class="mr-2" />
                    </template>
                  </VListItem>
                </template>
              </VSelect>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VCardActions class="gap-2">
        <VSpacer />
        <VBtn variant="outlined" color="secondary" @click="closeDialog" class="w-120px">
          Cancel
        </VBtn>
        <VBtn color="primary" variant="elevated" @click="handleSubmit">
          Create Scenario
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const isDialogVisible = ref(props.modelValue)
const form = ref(null)

const initialFormData = {
  scenario: '',
  description: '',
  icon: '',
  color: 'primary',
}

const formData = ref({ ...initialFormData })

// Get grouped MDI icons
const getGroupedMdiIcons = () => {
  return {
    'User Management': [
      'mdi-account-group-outline',
      'mdi-account-outline',
      'mdi-account-multiple-outline',
      'mdi-account-circle-outline',
      'mdi-account-supervisor-outline',
      'mdi-account-box-outline',
      'mdi-account-card-details-outline',
      'mdi-badge-account-outline'
    ],
    'Business Management': [
      'mdi-shopping-outline',
      'mdi-cart-outline',
      'mdi-store-outline',
      'mdi-briefcase-outline',
      'mdi-handshake-outline',
      'mdi-domain',
      'mdi-office-building-outline',
      'mdi-factory'
    ],
    'Finance & Accounting': [
      'mdi-finance',
      'mdi-cash-multiple',
      'mdi-credit-card-outline',
      'mdi-bank-outline',
      'mdi-currency-usd',
      'mdi-currency-eur',
      'mdi-currency-cny',
      'mdi-wallet-outline',
      'mdi-calculator-variant-outline'
    ],
    'Data Analytics': [
      'mdi-chart-line',
      'mdi-chart-bar',
      'mdi-chart-pie',
      'mdi-analytics',
      'mdi-trending-up',
      'mdi-trending-down',
      'mdi-chart-timeline-variant',
      'mdi-graph-outline'
    ],
    'System Administration': [
      'mdi-cogs',
      'mdi-settings-outline',
      'mdi-database-outline',
      'mdi-server-outline',
      'mdi-monitor-dashboard',
      'mdi-console',
      'mdi-api',
      'mdi-cloud-outline'
    ],
    'Office Collaboration': [
      'mdi-puzzle-outline',
      'mdi-folder-outline',
      'mdi-file-document-outline',
      'mdi-clipboard-text-outline',
      'mdi-calendar-outline',
      'mdi-clock-outline',
      'mdi-bell-outline',
      'mdi-bookmark-outline'
    ],
    'Communication': [
      'mdi-email-outline',
      'mdi-message-text-outline',
      'mdi-phone-outline',
      'mdi-video-outline',
      'mdi-forum-outline',
      'mdi-chat-outline',
      'mdi-comment-outline',
      'mdi-share-variant-outline'
    ],
    'Logistics & Transport': [
      'mdi-truck-delivery-outline',
      'mdi-package-variant-closed',
      'mdi-warehouse',
      'mdi-map-marker-outline',
      'mdi-airplane-outline',
      'mdi-ship-wheel',
      'mdi-train',
      'mdi-bike',
      'mdi-car-outline',
      'mdi-bus-outline',
      'mdi-subway-outline',
      'mdi-gas-station-outline',
      'mdi-parking',
      'mdi-traffic-light-outline'
    ],
    'Education & Training': [
      'mdi-school-outline',
      'mdi-book-outline',
      'mdi-certificate-outline',
      'mdi-presentation',
      'mdi-graduation-cap',
      'mdi-library-outline',
      'mdi-pencil-outline',
      'mdi-notebook-outline'
    ],
    'Healthcare & Medical': [
      'mdi-hospital-building',
      'mdi-medical-bag',
      'mdi-heart-pulse',
      'mdi-pill',
      'mdi-stethoscope',
      'mdi-tooth-outline',
      'mdi-ambulance',
      'mdi-bandage'
    ],
    'Entertainment & Media': [
      'mdi-movie-outline',
      'mdi-music-outline',
      'mdi-camera-outline',
      'mdi-gamepad-variant-outline',
      'mdi-television-play',
      'mdi-headphones',
      'mdi-microphone-outline',
      'mdi-radio'
    ],
    'Tools & Security': [
      'mdi-wrench-outline',
      'mdi-hammer-screwdriver',
      'mdi-toolbox-outline',
      'mdi-flash-outline',
      'mdi-shield-check-outline',
      'mdi-lock-outline',
      'mdi-key-outline',
      'mdi-qrcode'
    ],
    'Shopping & Retail': [
      'mdi-tag-outline',
      'mdi-barcode',
      'mdi-receipt-outline',
      'mdi-gift-outline',
      'mdi-shopping-cart-outline'
    ],
    'Food & Beverage': [
      'mdi-food-outline',
      'mdi-silverware-fork-knife',
      'mdi-coffee-outline',
      'mdi-glass-wine',
      'mdi-cake-variant-outline'
    ],
    'Sports & Fitness': [
      'mdi-dumbbell',
      'mdi-run',
      'mdi-soccer',
      'mdi-basketball',
      'mdi-tennis'
    ],
    'Technology & Innovation': [
      'mdi-robot-outline',
      'mdi-chip',
      'mdi-test-tube',
      'mdi-microscope',
      'mdi-rocket-outline'
    ]
  }
}

// Convert grouped icons to flat options list with group headers
const createIconOptions = () => {
  const groupedIcons = getGroupedMdiIcons()
  const options = []

  Object.entries(groupedIcons).forEach(([groupName, icons]) => {
    // Add group header
    options.push({
      value: null,
      title: groupName,
      isHeader: true
    })

    // Add icons from this group
    icons.forEach(icon => {
      options.push({
        value: icon,
        title: icon.replace('mdi-', '').replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        group: groupName,
        isHeader: false
      })
    })
  })

  return options
}

const iconSearchText = ref('')
const iconMenuVisible = ref(false)

// Computed property: display selected icon name
const selectedIconDisplay = computed(() => {
  if (!formData.value.icon) return ''

  const groupedIcons = getGroupedMdiIcons()
  for (const [, icons] of Object.entries(groupedIcons)) {
    const found = icons.find(icon => icon === formData.value.icon)
    if (found) {
      return found.replace('mdi-', '').replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }
  }
  return formData.value.icon
})

// Computed property: filtered grouped icons
const filteredGroupedIcons = computed(() => {
  const groupedIcons = getGroupedMdiIcons()
  const searchTerm = iconSearchText.value.toLowerCase()

  if (!searchTerm) {
    // When no search term, show all groups
    return Object.entries(groupedIcons).map(([groupName, icons]) => ({
      name: groupName,
      icons: icons.map(icon => ({
        value: icon,
        title: icon.replace('mdi-', '').replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      }))
    }))
  }

  // When there's a search term, only show groups with matching icons
  const filtered = []
  Object.entries(groupedIcons).forEach(([groupName, icons]) => {
    const matchedIcons = icons.filter(icon => {
      const title = icon.replace('mdi-', '').replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      return title.toLowerCase().includes(searchTerm) || icon.toLowerCase().includes(searchTerm)
    })

    if (matchedIcons.length > 0) {
      filtered.push({
        name: groupName,
        icons: matchedIcons.map(icon => ({
          value: icon,
          title: icon.replace('mdi-', '').replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        }))
      })
    }
  })

  return filtered
})

// Select icon
function selectIcon(icon) {
  formData.value.icon = icon.value
  iconMenuVisible.value = false
}

// Clear icon selection
function clearIcon() {
  formData.value.icon = ''
}

const colorOptions = ref(['primary', 'success', 'warning', 'error', 'info'])

watch(() => props.modelValue, (newValue) => {
  isDialogVisible.value = newValue
  if (newValue) {
    formData.value = { ...initialFormData }
  }
})

function closeDialog() {
  isDialogVisible.value = false
  emit('update:modelValue', false)
}

async function handleSubmit() {
  const { valid } = await form.value.validate()
  if (valid) {
    emit('submit', { ...formData.value })
    closeDialog()
  }
}
</script>

<style scoped>
.field-item {
  border-left: 3px solid #1976d2;
  padding-left: 12px;
  margin-left: 8px;
}

/* Icon selector styles */
.icon-selector-card {
  background: #1a1a1a !important;
  border: 1px solid #333 !important;
  max-width: 400px;
  max-height: 480px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;
  border-radius: 8px !important;
}

.icon-selector-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 4px;
}

.icon-group {
  margin-bottom: 16px;
}

.icon-group-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: rgba(76, 175, 80, 0.9);
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 8px 12px;
  background: linear-gradient(90deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
  border-top: 1px solid rgba(76, 175, 80, 0.2);
  border-bottom: 1px solid rgba(76, 175, 80, 0.2);
  border-radius: 4px;
  position: relative;
  margin-bottom: 8px;
}

.icon-group-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: linear-gradient(180deg, #4caf50, #81c784);
  border-radius: 0 2px 2px 0;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
  padding: 0 4px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid transparent;
  min-height: 80px;
  justify-content: center;
}

.icon-item:hover {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

.icon-item-selected {
  background: rgba(76, 175, 80, 0.2) !important;
  border-color: rgba(76, 175, 80, 0.6) !important;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.3) !important;
}

.icon-item .v-icon {
  margin-bottom: 4px;
  color: rgba(255, 255, 255, 0.7);
}

.icon-item:hover .v-icon,
.icon-item-selected .v-icon {
  color: #4caf50;
}

.icon-name {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.icon-item:hover .icon-name,
.icon-item-selected .icon-name {
  color: #4caf50;
}

/* Scrollbar styles */
.icon-selector-container::-webkit-scrollbar {
  width: 6px;
}

.icon-selector-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.icon-selector-container::-webkit-scrollbar-thumb {
  background: rgba(76, 175, 80, 0.3);
  border-radius: 3px;
}

.icon-selector-container::-webkit-scrollbar-thumb:hover {
  background: rgba(76, 175, 80, 0.5);
}

/* Ensure VMenu overlay styles are correct */
:deep(.v-overlay__content) {
  box-shadow: none !important;
}

/* Search box style optimization */
:deep(.icon-selector-card .v-text-field) {
  background: rgba(255, 255, 255, 0.05) !important;
}

:deep(.icon-selector-card .v-text-field .v-field__outline) {
  --v-field-border-opacity: 0.3;
}

:deep(.icon-selector-card .v-text-field .v-field__outline:hover) {
  --v-field-border-opacity: 0.6;
}

/* Main text field style optimization */
.icon-selector-input {
  cursor: pointer;
}

.icon-selector-input :deep(.v-field__input) {
  cursor: pointer;
}

.icon-selector-input :deep(.v-field__append-inner) {
  cursor: pointer;
}

.icon-selector-input :deep(.v-field__append-inner .v-icon) {
  transition: transform 0.2s ease;
  color: rgba(255, 255, 255, 0.6);
}

.icon-selector-input:hover :deep(.v-field__append-inner .v-icon) {
  color: rgba(255, 255, 255, 0.8);
}

/* Rotate arrow when menu is open */
.icon-menu-open .icon-selector-input :deep(.v-field__append-inner .v-icon) {
  transform: rotate(180deg);
  color: rgba(76, 175, 80, 0.8);
}

/* Input field focus styles */
.icon-selector-input :deep(.v-field--focused .v-field__outline) {
  --v-field-border-opacity: 1;
  --v-field-border-width: 2px;
}

/* Clear button styles */
.icon-selector-input :deep(.v-field__clearable .v-icon) {
  color: rgba(255, 255, 255, 0.5);
  transition: color 0.2s ease;
}

.icon-selector-input :deep(.v-field__clearable .v-icon:hover) {
  color: rgba(255, 255, 255, 0.8);
}
</style>
