import { setupLayouts } from 'virtual:generated-layouts'
import { createRouter, createWebHistory } from 'vue-router/auto'
import { redirects, routes } from './additional-routes'
import { setupGuards } from './guards'

function recursiveLayouts(route) {
  if (route.children) {
    for (let i = 0; i < route.children.length; i++)
      route.children[i] = recursiveLayouts(route.children[i])
    
    return route
  }
  
  return setupLayouts([route])[0]
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to) {
    if (to.hash)
      return { el: to.hash, behavior: 'smooth', top: 60 }
    
    return { top: 0 }
  },
  extendRoutes: pages => {
    // 对路由进行去重,根据path作为唯一标识
    const uniqueRoutes = [...pages, ...routes].reduce((acc, route) => {
      const existingRoute = acc.find(r => r.path === route.path && route.name)
      if (!existingRoute) {
        acc.push(route)
      }
      return acc
    }, [])

    return [
      ...redirects,
      ...uniqueRoutes.map(route => recursiveLayouts(route)),
    ]
  },
})

setupGuards(router)
export { router }
export default function (app) {
  app.use(router)
}
