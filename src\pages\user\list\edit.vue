<script setup>
import { useRouter, useRoute } from 'vue-router'
import UserForm from '@/business/user/list/form.vue'
import { dateUtil } from '@/utils/day'
import { useUserDetail, getRoleList } from '@/business/user/list/useUserDetail'

definePage({
  meta: {
    navActiveLink: 'user-list',
    breadcrumb: 'Edit User',
  },
})

const router = useRouter()
const userFormRef = ref(null)
const isSaving = ref(false)
const route = useRoute()
const userId = computed(() => route.query.id)
const { userData } = useUserDetail(route.query.id)



// form data
const formModel = ref({
  id: '',
  email: '',
  name: '',
  statusId: true,
  roleList: [],
})

watch(userData, () => {
  formModel.value.id = userData.value.accountId
  formModel.value.email = userData.value.accountEmail
  formModel.value.name = userData.value.accountName
  formModel.value.statusId = userData.value.accountStatusId
  formModel.value.lastLoginDateTime = userData.value.lastLoginDateTime
  formModel.value.roleList = getRoleList(userData.value.userGroupList).map(item => item.id)
})

const handleCancel = () => {
  userFormRef.value?.resetForm()
  router.back()
}

const handleSave = async () => {
  if (isSaving.value) return
  const params = userFormRef.value?.getParams()
  // delete email and name from params
  delete params.email
  delete params.name
  try {
    isSaving.value = true
    await $api('/api/admin-api/v1/account', {
      method: 'PUT',
      body: params
    })
    message.success('User updated successfully')
    router.back()
  } finally {
    isSaving.value = false
  }
}
</script>

<template>
  <VCard>
    <VCardItem class="pb-4 px-0">
      <VCardTitle>
        Edit User
      </VCardTitle>
    </VCardItem>

    <UserForm ref="userFormRef" v-model="formModel" type="edit" />

    <VRow class="d-flex mt-3">
      <VCol cols="12" class="d-flex justify-end">
        <VBtn variant="outlined" color="default" @click="handleCancel">
          CANCEL
        </VBtn>
        <VBtn color="primary" class="ml-2" style="width: 120px;" @click="handleSave">
          SAVE
        </VBtn>
      </VCol>
    </VRow>
  </VCard>
</template>
