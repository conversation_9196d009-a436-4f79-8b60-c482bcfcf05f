<template>
  <div class="low-code-layout">
    <!-- 导航区域 -->
    <LowCodeNavigation v-model="currentNavigation" />
    
    <!-- 内容区域 -->
    <div class="content-area">
      <slot />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import LowCodeNavigation from './LowCodeNavigation.vue'

// ===== Props =====
const props = defineProps({
  navigationValue: {
    type: String,
    default: 'dashboard'
  }
})

// ===== 状态 =====
const currentNavigation = ref(props.navigationValue)
</script>

<style scoped>
.low-code-layout {
  min-height: 100vh;
  background: #0f0f0f;
  color: white;
}

.content-area {
  min-height: calc(100vh - 68px); /* 减去导航高度 */
  overflow-y: auto;
  background: #0f0f0f;
  padding: 0;
}
</style> 