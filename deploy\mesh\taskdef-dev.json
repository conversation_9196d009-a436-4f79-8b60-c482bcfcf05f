{"executionRoleArn": "arn:aws:iam::903306222264:role/%ROLE%", "taskRoleArn": "arn:aws:iam::903306222264:role/%ROLE%", "containerDefinitions": [{"logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "%LOG_NAME%", "awslogs-region": "%REGION%", "awslogs-stream-prefix": "%LOG_STREAM%"}}, "portMappings": [{"hostPort": 80, "protocol": "tcp", "containerPort": 80}], "command": ["npm", "run", "docker-start"], "cpu": 0, "environment": "%ENVIRONMENT%", "secrets": "%SECRET%", "mountPoints": [], "memory": "%MEMORY_INT%", "volumesFrom": [], "image": "903306222264.dkr.ecr.%REGION%.amazonaws.com/%REPOSITORY_NAME%:%IMAGE_ENV%_v%BUILD_NUMBER%", "dependsOn": [{"containerName": "envoy", "condition": "HEALTHY"}], "essential": true, "name": "%NAME%"}, {"logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "%LOG_NAME%", "awslogs-region": "%REGION%", "awslogs-stream-prefix": "%LOG_STREAM%"}}, "portMappings": [], "cpu": 0, "environment": [{"name": "APPMESH_VIRTUAL_NODE_NAME", "value": "mesh/%MESH_NAME%/virtualNode/%SERVICE_DISCOVERY%_vn"}, {"name": "ENVOY_LOG_LEVEL", "value": "warning"}, {"name": "ENABLE_ENVOY_XRAY_TRACING", "value": "0"}, {"name": "XRAY_DAEMON_PORT", "value": "2000"}], "memory": 500, "volumesFrom": [], "image": "903306222264.dkr.ecr.%REGION%.amazonaws.com/%MESH_TYPE%-envoy:v1.25.4.0-%SERVICE_ENV%", "healthCheck": {"retries": 3, "command": ["CMD-SHELL", "curl -s http://localhost:9901/server_info | grep state | grep -q LIVE"], "timeout": 2, "interval": 30, "startPeriod": 10}, "essential": true, "user": "1337", "name": "envoy"}], "placementConstraints": [], "memory": "%MEMORY%", "family": "%FAMILY%", "requiresCompatibilities": ["FARGATE"], "networkMode": "awsvpc", "cpu": "%CPU%", "proxyConfiguration": {"type": "APPMESH", "containerName": "envoy", "properties": [{"name": "ProxyIngressPort", "value": "15000"}, {"name": "AppPorts", "value": "80"}, {"name": "EgressIgnoredIPs", "value": "*************,***************"}, {"name": "IgnoredGID", "value": ""}, {"name": "EgressIgnoredPorts", "value": ""}, {"name": "IgnoredUID", "value": "1337"}, {"name": "ProxyEgressPort", "value": "15001"}]}, "volumes": []}