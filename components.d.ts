/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddEditAddressDialog: typeof import('./src/components/AddEditAddressDialog.vue')['default']
    AddEditPermissionDialog: typeof import('./src/components/AddEditPermissionDialog.vue')['default']
    AddEditRoleDialog: typeof import('./src/components/AddEditRoleDialog.vue')['default']
    AddPaymentMethodDialog: typeof import('./src/components/AddPaymentMethodDialog.vue')['default']
    AppAutocomplete: typeof import('./src/components/AppAutocomplete.vue')['default']
    AppBarSearch: typeof import('./src/components/AppBarSearch.vue')['default']
    AppCardActions: typeof import('./src/components/AppCardActions.vue')['default']
    AppCardCode: typeof import('./src/components/AppCardCode.vue')['default']
    AppCheckboxTable: typeof import('./src/components/AppCheckboxTable.vue')['default']
    AppCollapsible: typeof import('./src/components/AppCollapsible.vue')['default']
    AppCombobox: typeof import('./src/components/AppCombobox.vue')['default']
    AppDateTimePicker: typeof import('./src/components/AppDateTimePicker.vue')['default']
    AppDrawerHeaderSection: typeof import('./src/components/AppDrawerHeaderSection.vue')['default']
    AppLoadingIndicator: typeof import('./src/components/AppLoadingIndicator.vue')['default']
    AppPricing: typeof import('./src/components/AppPricing.vue')['default']
    AppSearchHeader: typeof import('./src/components/AppSearchHeader.vue')['default']
    AppSelect: typeof import('./src/components/AppSelect.vue')['default']
    AppStepper: typeof import('./src/components/AppStepper.vue')['default']
    AppTextarea: typeof import('./src/components/AppTextarea.vue')['default']
    AppTextField: typeof import('./src/components/AppTextField.vue')['default']
    AppTree: typeof import('./src/components/AppTree.vue')['default']
    Breadcrumb: typeof import('./src/components/Breadcrumb.vue')['default']
    BuyNow: typeof import('./src/components/BuyNow.vue')['default']
    CardAddEditDialog: typeof import('./src/components/CardAddEditDialog.vue')['default']
    CardStatisticsHorizontal: typeof import('./src/components/CardStatisticsHorizontal.vue')['default']
    CardStatisticsVertical: typeof import('./src/components/CardStatisticsVertical.vue')['default']
    CardStatisticsVerticalSimple: typeof import('./src/components/CardStatisticsVerticalSimple.vue')['default']
    ConfirmDialog: typeof import('./src/components/ConfirmDialog.vue')['default']
    CreateAppDialog: typeof import('./src/components/CreateAppDialog.vue')['default']
    CustomCheckboxes: typeof import('./src/components/CustomCheckboxes.vue')['default']
    CustomCheckboxesWithIcon: typeof import('./src/components/CustomCheckboxesWithIcon.vue')['default']
    CustomCheckboxesWithImage: typeof import('./src/components/CustomCheckboxesWithImage.vue')['default']
    CustomizerSection: typeof import('./src/components/CustomizerSection.vue')['default']
    CustomRadios: typeof import('./src/components/CustomRadios.vue')['default']
    CustomRadiosWithIcon: typeof import('./src/components/CustomRadiosWithIcon.vue')['default']
    CustomRadiosWithImage: typeof import('./src/components/CustomRadiosWithImage.vue')['default']
    DialogCloseBtn: typeof import('./src/components/DialogCloseBtn.vue')['default']
    DropZone: typeof import('./src/components/DropZone.vue')['default']
    EnableOneTimePasswordDialog: typeof import('./src/components/EnableOneTimePasswordDialog.vue')['default']
    ErrorHeader: typeof import('./src/components/ErrorHeader.vue')['default']
    I18n: typeof import('./src/components/I18n.vue')['default']
    MoreBtn: typeof import('./src/components/MoreBtn.vue')['default']
    Notifications: typeof import('./src/components/Notifications.vue')['default']
    PaymentProvidersDialog: typeof import('./src/components/PaymentProvidersDialog.vue')['default']
    PricingPlanDialog: typeof import('./src/components/PricingPlanDialog.vue')['default']
    ProductDescriptionEditor: typeof import('./src/components/ProductDescriptionEditor.vue')['default']
    ReferAndEarnDialog: typeof import('./src/components/ReferAndEarnDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollToTop: typeof import('./src/components/ScrollToTop.vue')['default']
    ShareProjectDialog: typeof import('./src/components/ShareProjectDialog.vue')['default']
    Shortcuts: typeof import('./src/components/Shortcuts.vue')['default']
    TablePagination: typeof import('./src/components/TablePagination.vue')['default']
    TheCustomizer: typeof import('./src/components/TheCustomizer.vue')['default']
    ThemeSwitcher: typeof import('./src/components/ThemeSwitcher.vue')['default']
    TiptapEditor: typeof import('./src/components/TiptapEditor.vue')['default']
    TwoFactorAuthDialog: typeof import('./src/components/TwoFactorAuthDialog.vue')['default']
    UserInfoEditDialog: typeof import('./src/components/UserInfoEditDialog.vue')['default']
    UserUpgradePlanDialog: typeof import('./src/components/UserUpgradePlanDialog.vue')['default']
  }
}
