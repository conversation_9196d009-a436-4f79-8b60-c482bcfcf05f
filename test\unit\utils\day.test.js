import dateUtil from '@/utils/day';

describe('dateUtil', () => {
  test('format formats date correctly', () => {
    const date = new Date('2023-01-01T00:00:00Z');
    expect(dateUtil.format(date)).toBe('2023-01-01 08:00:00'); // 根据时区调整
  });

  test('isValid checks if date is valid', () => {
    expect(dateUtil.isValid('2023-01-01')).toBe(true);
    expect(dateUtil.isValid('invalid')).toBe(false);
  });
}); 