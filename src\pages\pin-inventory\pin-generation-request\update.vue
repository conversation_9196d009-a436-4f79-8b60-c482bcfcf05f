<script setup lang="jsx">
import { useRouter, useRoute } from 'vue-router'
import PinForm from '@/business/pin-generator/form.vue'
import { watch } from 'vue';
definePage({
  meta: {
    navActiveLink: 'approve-pin',
    breadcrumb: 'Approve PIN',
  },
})

const router = useRouter()
const pinRequestFormRef = ref(null)
const isSaving = ref(false)
const route = useRoute();
const pinId = route.query.id;
const isShowModal = ref(false)
const type = ref('approve')
const selectedReason = ref(null)

// form data
const formModel = ref({
    needSFTP: false,
    razerGoldPINGroup: null,
    quantity: null,
    status: null
})

const reasons = [
    'Forecast mismatch',
    'Incorrect Razer Gold pin group',
    'Purchase order quantity mismatch',
]

const handleClick = (type) => {
  let isInProgress = false
  const isReject = type === 'reject'
  let text = isReject 
    ? <div>
        Once rejected, user will need to resubmit it for approval.
        <br/>
        This action cannot be undone.
      </div>
    : <div>
        Once approved, user will be able to request the generated RG PINs.
        <br/>
        This action cannot be undone.
      </div>

  confirmDialog({
    title: `${type === 'reject' ? 'Reject' : 'Approve'} RG PIN Generation Request?`,
    text,
    width: 418,
    render: () => {
      return (
          <div class="px-6 mb-10" style={{display: isReject ? 'block' : 'none'}}>
            <AppSelect
              items={reasons}
              label="Reject Reason*"
              placeholder="Type a reason"
              v-model={selectedReason.value}
              item-text="text"
              item-value="value"
              rules={[requiredValidator]}
            />
          </div>
      );
    },
    confirmButtonText: `${isReject ? 'REJECT' : 'APPROVE'}`,
    confirmButtonColor: isReject ? '#FD4949' : '#44D62C',
    confirmButtonTextColor: '#000000',
    cancelButtonText: 'CANCEL',
    async onConfirm(close) {
      if (isInProgress) return isInProgress = true
      try {
        const params = {
            requestId: Number (pinId),
            operation: isReject ? 'REJECTED' : 'APPROVED',
        }
        if (isReject && !selectedReason.value) {
          throw new Error('Must select reason!')
        }
        if (isReject) {
          params.reason = selectedReason.value
        }
        await $api('/api/admin-api/v1/request/status', {
            method: 'PUT',
            body: params
        })
        message.success('Successfully')
        router.push('/pin-inventory/pin-generation-request')
        close()
      } catch (error) {
        console.error(error)
        message.error(error._data?.message || error.message)
      } finally {
        isInProgress = false
      }
    }
  })
}

const getPinDetail = async () => {
  try {
    const res = await $api(`/api/admin-api/v1/pin-generate-request?requestId=${pinId}`)
    formModel.value = res.data
  } catch (error) {
    console.error("Error loading data:", error)
  }
}

watch([pinId], () => {
  getPinDetail()
}, { immediate: true })
</script>

<template>
  <VCard>
    <VCardItem class="pb-4 px-0">
      <VCardTitle>
        Approve PIN
      </VCardTitle>
    </VCardItem>
    
    <PinForm
      ref="pinRequestFormRef"
      v-model="formModel"
      :type="type"
    />
    
    <VRow class="d-flex mt-3" v-if="formModel.status === 'PENDING'">
      <VCol cols="5" class="d-flex justify-end">
        <VBtn variant="outlined" color="default" @click="handleClick('reject')">
          REJECT
        </VBtn>
        <VBtn color="primary" class="ml-2" style="width: 120px;" data-testid="save-button" @click="handleClick('approve')">
          APPROVE
        </VBtn>
      </VCol>
    </VRow>
  </VCard>
</template>
