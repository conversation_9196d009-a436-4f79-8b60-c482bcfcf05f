server {
    listen       443 ssl;
    server_name  localhost;

    #access_log  /var/log/nginx/host.access.log  main;

    ssl_certificate     /etc/certs/ssl_cert;
    ssl_certificate_key /etc/certs/ssl_key;

    add_header "Content-Security-Policy" "frame-ancestors 'none'";
    add_header "X-Frame-Options" "deny";
    add_header "X-Content-Type-Options" "nosniff";
    add_header "Referrer-Policy" "strict-origin-when-cross-origin";
    add_header "Strict-Transport-Security" "max-age=31536000; includeSubDomains; preload";
    
    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass https://console-v2.zgold-dev.razer.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $http_x_forwarded_proto;
    }

    location /healthcheck {
        add_header Content-Type text/plain;
        return 200 'Healthy!';
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}