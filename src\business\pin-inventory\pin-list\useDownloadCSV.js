import message from '@/utils/message'
import { $api } from '@/utils/request'

export const useDownloadCSV = async (id) => {
  try {
    const response = await $api('/api/pin-inventory/v1/pin-list/download', {
      method: 'POST',
      body: {
        pinListId: id,
      },
    })

    const a = document.createElement('a')
    a.href = response.data.downloadUrl
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    message.success('Start to download PIN List CSV file')
  } catch (error) {
    message.error(error.message)
  }
}
