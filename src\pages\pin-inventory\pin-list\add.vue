<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import PINListRequestAddForm from '@/business/pin-inventory/pin-list/addForm.vue'
import {initialFormState, useSubmitPINListRequest} from '@/business/pin-inventory/pin-list/useSubmitPINListRequest.js'

definePage({
  meta: {
    navActiveLink: 'pin-inventory-pin-list',
    breadcrumb: 'Create New RG PIN List Request',
  },
})

const router = useRouter()
const pinListFormRef = ref(null)

const formModel = ref(structuredClone(initialFormState))

const { isSubmitting, submitRequest, cancelRequest } =
  useSubmitPINListRequest(pinListFormRef)

const handleSubmit = submitRequest
const handleCancel = cancelRequest
</script>

<template>
  <section>
    <VRow>
      <PINListRequestAddForm
        ref="pinListFormRef"
        v-model="formModel"
        :loading="isSubmitting"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </VRow>
  </section>
</template>

<style scoped lang="scss">
.v-card-title {
  padding-bottom: 16px;
  font-weight: 600;
}

.form-wrapper {
  background: #1e1e1e;
  border-radius: 16px;
  height: 100%;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  max-height: 80vh;
}

:deep(.v-field__input) {
  color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
}

:deep(.v-card-text) {
  overflow-y: auto;
}

.v-row {
  row-gap: 8px;
}

:deep(.v-col) {
  padding-top: 4px;
  padding-bottom: 4px;
}
</style>
