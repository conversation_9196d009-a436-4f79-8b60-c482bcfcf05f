<template>
  <div class="low-code-container">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <VProgressCircular indeterminate size="64" />
      <div class="loading-text">正在加载页面配置...</div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-overlay">
      <VAlert type="error" :text="error" />
      <VBtn @click="retryLoad" color="primary" class="mt-4">
        重试
      </VBtn>
    </div>

    <!-- 页面内容 -->
    <div v-else-if="pageConfig" class="page-content">
      <!-- 页面标题 -->
      <div v-if="pageConfig.name" class="page-header">
        <VCard class="mb-4">
          <VCardTitle>{{ pageConfig.name }}</VCardTitle>
          <VCardSubtitle v-if="pageConfig.module">{{ pageConfig.module }}</VCardSubtitle>
        </VCard>
      </div>

      <!-- 渲染页面组件 -->
      <div class="page-components">
        <template v-for="component in renderedComponents" :key="component.id">
          <component 
            :is="component.component" 
            v-bind="component.props"
            v-on="component.events"
          />
        </template>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <VAlert type="info" text="页面配置不存在" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { pageConfigManager } from '@/business/low-code-engine/utils/pageConfigManager.js'
import { render } from '@/business/low-code-engine/renderer/index.js'

// 响应式数据
const isLoading = ref(true)
const error = ref(null)
const pageConfig = ref(null)
const renderedComponents = ref([])

// 路由
const route = useRoute()
const router = useRouter()

// 计算属性：从路由路径解析页面信息
const pageInfo = computed(() => {
  const path = route.path
  return pageConfigManager.parsePageUrl(path)
})

// 加载页面配置
async function loadPageConfig() {
  if (!pageInfo.value) {
    error.value = '无效的页面路径'
    isLoading.value = false
    return
  }

  try {
    isLoading.value = true
    error.value = null
    
    console.log('加载页面配置:', pageInfo.value)
    
    // 使用配置管理器获取页面配置
    const config = await pageConfigManager.getPageConfig(
      pageInfo.value.scenario, 
      pageInfo.value.pageType
    )
    
    if (!config) {
      throw new Error('页面配置不存在')
    }
    
    // 验证配置
    if (!pageConfigManager.validatePageConfig(config)) {
      throw new Error('页面配置格式无效')
    }
    
    pageConfig.value = config
    console.log('页面配置加载成功:', config)
    
    // 渲染组件
    renderPageComponents()
    
  } catch (err) {
    console.error('加载页面配置失败:', err)
    error.value = err.message || '加载页面配置失败'
    
    // 尝试生成配置作为备选方案
    try {
      console.log('尝试生成页面配置作为备选方案')
      const generatedConfig = await pageConfigManager.generatePageConfig(
        pageInfo.value.scenario, 
        pageInfo.value.pageType
      )
      
      if (generatedConfig) {
        pageConfig.value = generatedConfig
        console.log('页面配置生成成功:', generatedConfig)
        renderPageComponents()
        error.value = null
      }
    } catch (genErr) {
      console.error('生成页面配置也失败:', genErr)
      error.value = `加载失败: ${err.message}, 生成失败: ${genErr.message}`
    }
  } finally {
    isLoading.value = false
  }
}

// 渲染页面组件
function renderPageComponents() {
  if (!pageConfig.value || !pageConfig.value.components) {
    console.warn('页面配置或组件不存在')
    return
  }

  try {
    // 使用渲染器渲染组件
    const rendered = render(pageConfig.value)
    
    if (rendered && Array.isArray(rendered)) {
      renderedComponents.value = rendered
      console.log('组件渲染成功:', rendered)
    } else {
      console.warn('渲染器返回空结果')
      renderedComponents.value = []
    }
  } catch (err) {
    console.error('渲染组件失败:', err)
    error.value = '渲染组件失败: ' + err.message
  }
}

// 重试加载
function retryLoad() {
  // 清除缓存后重试
  if (pageInfo.value) {
    pageConfigManager.clearCache(pageInfo.value.scenario, pageInfo.value.pageType)
  }
  loadPageConfig()
}

// 监听路由变化
watch(() => route.path, () => {
  if (route.path.startsWith('/low-code')) {
    loadPageConfig()
  }
}, { immediate: true })

// 组件挂载时加载配置
onMounted(() => {
  if (route.path.startsWith('/low-code')) {
    loadPageConfig()
  }
})
</script>

<style lang="scss" scoped>
.low-code-container {
  min-height: 100vh;
  padding: 20px;
  
  .loading-overlay {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    
    .loading-text {
      margin-top: 16px;
      font-size: 16px;
      color: #666;
    }
  }
  
  .error-overlay {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
  }
  
  .page-content {
    .page-header {
      margin-bottom: 20px;
    }
    
    .page-components {
      // 组件间距
      > * {
        margin-bottom: 16px;
      }
    }
  }
  
  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
  }
}
</style> 