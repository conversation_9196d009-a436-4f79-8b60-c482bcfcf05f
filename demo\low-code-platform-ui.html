<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Low-Code Platform - Admin Console</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.0/Sortable.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            min-height: 100vh;
        }

        .container {
            max-width: 100%;
            margin: 0;
            padding: 0;
        }

        /* 头部导航 */
        .header {
            background: #2d2d2d;
            height: 60px;
            padding: 0 30px;
            margin-bottom: 0;
          }
          
          .nav {
            height: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #404040;
        }

        .logo {
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo::before {
            content: '';
            width: 32px;
            height: 32px;
            background: #ffa726;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-links {
            display: flex;
            gap: 0;
        }

        .nav-link {
            text-decoration: none;
            color: #cccccc;
            font-weight: 500;
            padding: 16px 20px;
            transition: all 0.2s ease;
            border-bottom: 3px solid transparent;
        }

        .nav-link:hover, .nav-link.active {
            background: #3d3d3d;
            color: #ffffff;
            border-bottom-color: #44D62C;
        }

        /* 主页面内容 */
        .main-content {
            display: none;
        }

        .main-content.active {
            display: block;
        }

        .content-area {
            background: #1a1a1a;
            padding: 16px 0;
            min-height: calc(100vh - 60px);
        }

        .page-header {
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #cccccc;
        }

        /*  Scenario 场景网格 */
        .scenario-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .scenario-card {
            background: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .scenario-editor-card:hover {
            border-color: #44D62C;
            box-shadow: 0 4px 12px rgba(46, 125, 50, 0.15);
            transform: translateY(-2px);
        }

        .scenario-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .scenario-icon {
            width: 48px;
            height: 48px;
            background: #44D62C;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .scenario-actions {
            color: #999;
            cursor: pointer;
            padding: 4px;
        }

        .scenario-actions:hover {
            color: #fff;
        }

        .scenario-info {
            flex: 1;
        }

        .scenario-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .scenario-description {
            color: #cccccc;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 16px;
        }

        .scenario-stats {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #999;
        }

        .stat-item i {
            font-size: 10px;
        }

        .status-active {
            color: #44D62C;
        }

        .status-draft {
            color: #ff9800;
        }

        /* 创建新场景卡片 */
        .create-scenario-card {
            border: 2px dashed #404040;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 160px;
        }

        .create-scenario-card:hover {
            border-color: #44D62C;
            background: rgba(46, 125, 50, 0.05);
        }

        .create-scenario-content {
            text-align: center;
        }

        .create-icon {
            width: 48px;
            height: 48px;
            border: 2px solid #666;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            color: #666;
            font-size: 20px;
        }

        .create-scenario-card:hover .create-icon {
            border-color: #44D62C;
            color: #44D62C;
        }

        .create-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .create-desc {
            color: #cccccc;
            font-size: 14px;
        }



        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 场景上下文样式 */
        .scenario-context {
            background: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 16px 20px;
            margin-bottom: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .context-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .context-icon {
            width: 36px;
            height: 36px;
            background: #44D62C;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .context-details h3 {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin: 0 0 4px 0;
        }

        .context-details p {
            font-size: 12px;
            color: #cccccc;
            margin: 0;
        }

        .context-stats {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .context-stat {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #999;
        }

        .context-stat i {
            font-size: 10px;
        }

        .context-status-active {
            color: #44D62C;
        }

        .btn {
            background: #44D62C;
            color: #000000;
            border: 1px solid transparent;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
        }

        .btn:hover {
            background: #3AC123;
        }

        .btn-secondary {
            background: #000000;
            border: 1px solid #fff;
            color: #ffffff;
        }

        .btn-secondary:hover {
            background: #1a1a1a;
            border-color: #555;
        }

        /* 页面编辑器样式 */
        .editor-container {
            background: #1a1a1a;
            height: calc(100vh - 120px);
            display: flex;
            overflow: hidden;
        }

        .editor-sidebar {
            width: 280px;
            background: #2d2d2d;
            border-right: 1px solid #404040;
            padding: 20px;
            overflow-y: auto;
        }

        .editor-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .editor-toolbar {
            background: #2d2d2d;
            border-bottom: 1px solid #404040;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .editor-canvas {
            flex: 1;
            background: #1a1a1a;
            padding: 20px;
            padding-bottom: 0;
            position: relative;
        }

        .canvas-area {
            background: #2d2d2d;
            border: 2px dashed #404040;
            border-radius: 8px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .empty-state {
            text-align: center;
            color: #cccccc;
        }

        .component-item {
            background: #404040;
            border: 1px solid #555;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: grab;
            transition: all 0.2s ease;
            color: #ffffff;
            font-size: 14px;
        }

        .component-item:hover {
            border-color: #44D62C;
            box-shadow: 0 2px 8px rgba(46, 125, 50, 0.2);
            background: #4d4d4d;
        }

        .component-item:active {
            cursor: grabbing;
        }

        /* Components Grid Layout */
        .components-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .component-item-grid {
            padding: 10px 8px;
            margin-bottom: 0;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            text-align: left;
        }

        .component-item-grid span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
        }

        /* Smart Generation Grid Layout */
        .smart-generation-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .smart-generate-item {
            padding: 10px 8px;
            margin-bottom: 0;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            text-align: left;
            font-weight: 600;
        }

        .smart-generate-item span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
        }

        .section-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
        }

        .properties-panel {
            width: 280px;
            background: #2d2d2d;
            border-left: 1px solid #404040;
            padding: 20px;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #ffffff;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #555;
            border-radius: 4px;
            font-size: 14px;
            background: #404040;
            color: #ffffff;
        }

        .form-input:focus {
            border-color: #44D62C;
            outline: none;
            box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.2);
        }

        .form-input::placeholder {
            color: #999;
        }

        /* 元数据管理器样式 */
        .meta-container {
            background: #1a1a1a;
            height: auto;
            padding: 0;
            overflow: visible;
        }

        .meta-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #404040;
        }

        .scenario-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .scenario-card {
            background: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 6px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .scenario-card:hover, .scenario-card.active {
            border-color: #44D62C;
            box-shadow: 0 2px 8px rgba(46, 125, 50, 0.2);
        }

        .scenario-name {
            font-weight: 600;
            margin-bottom: 8px;
            color: #ffffff;
            font-size: 16px;
        }

        .scenario-desc {
            font-size: 14px;
            color: #cccccc;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .scenario-stats {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #999;
        }

        .field-table {
            background: #000000;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid #404040;
        }

        .field-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .field-table th {
            background: #111111;
            color: white;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
        }

        .field-table td {
            padding: 12px 16px;
            border-bottom: none;
            color: #ffffff;
            font-size: 14px;
        }

        .field-table tr:hover {
            background: #1a1a1a;
        }



        .badge {
            background: #404040;
            color: #ffffff;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
        }

        .badge.required {
            background: #f44336;
            color: white;
        }

        .badge.queryable {
            background: #44D62C;
            color: white;
        }

        code {
            background: #404040;
            color: #44D62C;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .feature-cards {
                grid-template-columns: 1fr;
            }
            
            .editor-container {
                flex-direction: column;
            }
            
            .editor-sidebar, .properties-panel {
                width: 100%;
                max-height: 200px;
            }
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: #2d2d2d;
            border-radius: 8px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid #404040;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #ffffff;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #cccccc;
        }

        .close-btn:hover {
            color: #ffffff;
        }

        .properties-group {
            margin-bottom: 8px;
            border: 1px solid #404040;
            border-radius: 6px;
            background: #262626;
            overflow: hidden;
        }
        .properties-group summary {
            font-weight: 600;
            cursor: pointer;
            padding: 12px 16px;
            color: #ffffff;
            font-size: 14px;
            background: #2d2d2d;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
            -webkit-user-select: none;
            user-select: none;
            transition: background-color 0.2s ease;
        }
        .properties-group summary:hover {
            background: #353535;
        }
        .properties-group summary::-webkit-details-marker {
            display: none;
        }
        .properties-group summary::after {
            content: '\f107';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: #999;
            transition: transform 0.2s ease;
        }
        .properties-group[open] summary::after {
            transform: rotate(180deg);
        }
        .properties-group[open] summary {
            border-bottom-color: #404040;
        }
        .properties-group .form-group {
            padding: 0 16px;
            margin-bottom: 12px;
        }
        .properties-group .form-group:first-child {
            margin-top: 16px;
        }
        .properties-group .form-group:last-child {
            margin-bottom: 16px;
        }
        .form-group-inline {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            margin-bottom: 0;
        }
        .form-group-inline .form-label {
            margin-bottom: 0;
            font-size: 13px;
        }
        .column-list {
            padding: 0 16px;
            margin-bottom: 12px;
        }
        .column-item {
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 4px;
            padding: 10px 12px;
            margin-bottom: 4px;
            cursor: move;
            color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
            transition: all 0.2s ease;
        }
        .column-item:hover {
            background: #252525;
            border-color: #555;
        }
        .column-item i {
            margin-right: 10px;
            color: #999;
        }
        .column-item .fa-grip-vertical {
            cursor: move;
        }
        .column-item.sortable-ghost {
            background: #555;
            border: 1px dashed #44D62C;
            opacity: 0.7;
        }

        /* Toggle Switch Component */
        .toggle-switch {
            width: 42px;
            height: 22px;
            background-color: #2d2d2d;
            border: 1px solid #555;
            border-radius: 11px;
            position: relative;
            cursor: pointer;
            transition: background-color 0.2s ease;
            display: inline-block;
            vertical-align: middle;
        }
        .toggle-switch.active {
            background-color: #44D62C;
            border-color: #44D62C;
        }
        .toggle-switch-handle {
            width: 16px;
            height: 16px;
            background-color: #111;
            border: 1px solid #ccc;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: left 0.2s ease, background-color 0.2s ease;
        }
        .toggle-switch.active .toggle-switch-handle {
            left: 22px;
            background-color: #fff;
            border-color: #fff;
        }

        /* Icon Button Style */
        .btn-icon {
            background: none;
            border: 1px solid #404040;
            border-radius: 4px;
            padding: 6px 8px;
            color: #cccccc;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .btn-icon:hover {
            background: #404040;
            border-color: #555;
            color: #ffffff;
        }

        /* Property Search Style */
        .property-search {
            margin-bottom: 16px;
            padding: 0 4px;
        }

        /* Smart Badge for Column Items */
        .smart-badge {
            background: #44D62C;
            color: #000;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 600;
            margin-left: 8px;
        }

        /* Quick Action Tooltips */
        .quick-actions {
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        .column-item:hover .quick-actions {
            opacity: 1;
        }

        /* Enhanced Form Labels */
        .form-label-enhanced {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
        }
        .form-help {
            color: #999;
            font-size: 11px;
            cursor: help;
        }

        /* Property Level Selector */
        .property-level-selector {
            padding: 0 4px;
        }
        .level-tabs {
            display: flex;
            background: #1a1a1a;
            border-radius: 4px;
            border: 1px solid #404040;
            overflow: hidden;
        }
        .level-tab {
            flex: 1;
            background: none;
            border: none;
            padding: 8px 12px;
            color: #cccccc;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }
        .level-tab:hover {
            background: #2d2d2d;
            color: #ffffff;
        }
        .level-tab.active {
            background: #44D62C;
            color: #000000;
            font-weight: 600;
        }
        .level-tab i {
            font-size: 11px;
        }

        /* Property Level Content */
        .property-level-content {
            animation: fadeIn 0.3s ease;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Selected Component Info */
        .selected-component-info {
            position: relative;
        }
                 .selected-component-info::before {
             content: '';
             position: absolute;
             left: 0;
             top: 0;
             bottom: 0;
             width: 3px;
             background: #44D62C;
             border-radius: 0 2px 2px 0;
         }

         /* Component Wrapper Styles */
         .component-wrapper {
             position: relative;
             margin: 10px 0;
             border: 2px solid transparent;
             border-radius: 4px;
             transition: all 0.2s ease;
             cursor: pointer;
         }
         .component-wrapper:hover {
             border-color: #44D62C;
         }
         .component-wrapper.selected {
             border-color: #44D62C;
             box-shadow: 0 0 0 2px rgba(68, 214, 44, 0.2);
         }

         /* Component Overlay */
         .component-overlay {
             position: absolute;
             top: -2px;
             left: -2px;
             right: -2px;
             height: 28px;
             background: #44D62C;
             color: #000;
             display: flex;
             justify-content: space-between;
             align-items: center;
             padding: 0 8px;
             font-size: 12px;
             font-weight: 600;
             opacity: 0;
             transition: opacity 0.2s ease;
             border-radius: 4px 4px 0 0;
             z-index: 10;
         }
         .component-wrapper:hover .component-overlay {
             opacity: 1;
         }
         .component-wrapper.selected .component-overlay {
             opacity: 1;
         }

         .component-label {
             display: flex;
             align-items: center;
             gap: 4px;
         }
         .component-actions {
             display: flex;
             gap: 4px;
         }
         .component-actions .btn-icon {
             padding: 2px 4px;
             font-size: 10px;
             border: none;
             background: rgba(0,0,0,0.3);
             color: #fff;
         }
         .component-actions .btn-icon:hover {
             background: rgba(0,0,0,0.5);
         }

                 /* Canvas Content */
        .canvas-content {
            padding: 20px 0;
        }

        /* 场景和页面选择器样式 */
        .scenario-page-selector {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #404040;
        }

        .scenario-page-selector .form-group {
            margin-bottom: 16px;
        }

        .scenario-page-selector .form-label {
            font-size: 12px;
            font-weight: 600;
            color: #cccccc;
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .current-context {
            animation: fadeIn 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <nav class="nav">
                <div class="logo">Low-Code Platform</div>
                <div class="nav-links">
                    <a href="#" class="nav-link active" data-page="home"> Scenario</a>
                    <a href="#" class="nav-link" data-page="meta">Metadata Manager</a>
                    <a href="#" class="nav-link" data-page="index">Index Manager</a>
                    <a href="#" class="nav-link" data-page="editor">Page Builder</a>
                </div>
            </nav>
        </header>

        <!-- 主内容区域 -->
        <div class="content-area">
                <!-- 首页内容 -->
                <main class="main-content active" id="home">
                    <div class="page-header">
                        <h1 class="page-title"> Scenario</h1>
                        <p class="page-subtitle">Visual page design platform based on metadata-driven architecture</p>
                    </div>

                    <!-- 场景列表 -->
                    <div class="scenario-grid">
                        <div class="scenario-card scenario-editor-card" onclick="openScenarioEditor('demo-user-management')">
                            <div class="scenario-header">
                                <div class="scenario-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="scenario-actions">
                                    <i class="fas fa-ellipsis-v" onclick="event.stopPropagation(); showScenarioMenu(this)"></i>
                                </div>
                            </div>
                            <div class="scenario-info">
                                <h3 class="scenario-title">User Management</h3>
                                <p class="scenario-description">User management system with authentication and role-based access control</p>
                                <div class="scenario-stats">
                                    <span class="stat-item">
                                        <i class="fas fa-list"></i> 7 fields
                                    </span>
                                    <span class="stat-item">
                                        <i class="fas fa-file-alt"></i> 3 pages
                                    </span>
                                    <span class="stat-item status-active">
                                        <i class="fas fa-circle"></i> Active
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="scenario-card scenario-editor-card" onclick="openScenarioEditor('demo-product-catalog')">
                            <div class="scenario-header">
                                <div class="scenario-icon">
                                    <i class="fas fa-shopping-bag"></i>
                                </div>
                                <div class="scenario-actions">
                                    <i class="fas fa-ellipsis-v" onclick="event.stopPropagation(); showScenarioMenu(this)"></i>
                                </div>
                            </div>
                            <div class="scenario-info">
                                <h3 class="scenario-title">Product Catalog</h3>
                                <p class="scenario-description">Complete product management with categories, inventory tracking and pricing</p>
                                <div class="scenario-stats">
                                    <span class="stat-item">
                                        <i class="fas fa-list"></i> 12 fields
                                    </span>
                                    <span class="stat-item">
                                        <i class="fas fa-file-alt"></i> 5 pages
                                    </span>
                                    <span class="stat-item status-draft">
                                        <i class="fas fa-circle"></i> Draft
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="scenario-card scenario-editor-card" onclick="openScenarioEditor('demo-order-system')">
                            <div class="scenario-header">
                                <div class="scenario-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="scenario-actions">
                                    <i class="fas fa-ellipsis-v" onclick="event.stopPropagation(); showScenarioMenu(this)"></i>
                                </div>
                            </div>
                            <div class="scenario-info">
                                <h3 class="scenario-title">Order Management</h3>
                                <p class="scenario-description">End-to-end order processing with payment integration and fulfillment tracking</p>
                                <div class="scenario-stats">
                                    <span class="stat-item">
                                        <i class="fas fa-list"></i> 15 fields
                                    </span>
                                    <span class="stat-item">
                                        <i class="fas fa-file-alt"></i> 8 pages
                                    </span>
                                    <span class="stat-item status-active">
                                        <i class="fas fa-circle"></i> Active
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- 创建新场景卡片 -->
                        <div class="scenario-card create-scenario-card" onclick="showCreateScenario()">
                            <div class="create-scenario-content">
                                <div class="create-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <h3 class="create-title">Create New Scenario</h3>
                                <p class="create-desc">Start building a new business scenario</p>
                            </div>
                        </div>
                    </div>


                </main>

                                <!-- 页面编辑器 -->
                <main class="main-content" id="editor">
                    <div class="editor-container">
                        <!-- 左侧组件库 -->
                        <div class="editor-sidebar">
                            <!-- 场景和页面选择区域 -->
                            <div class="scenario-page-selector">
                                <div class="form-group">
                                    <label class="form-label">Select Scenario</label>
                                    <select id="scenario-selector" class="form-input" onchange="handleScenarioChange(this.value)">
                                        <option value="">select scanario...</option>
                                        <option value="demo-user-management"> User Management</option>
                                        <option value="demo-product-catalog">Product Catalog</option>
                                        <option value="demo-order-system">Order System</option>
                                        <option value="demo-user-group">User Group</option>
                                    </select>
                                </div>
                                
                                <!-- <div class="form-group">
                                    <label class="form-label">Select Page</label>
                                    <div style="display: flex; gap: 8px;">
                                        <select id="page-selector" class="form-input" style="flex: 1;" onchange="handlePageSelection(this.value)">
                                            <option value="">select page...</option>
                                        </select>
                                        <button class="btn-icon" onclick="showNewPageModal()" title="新建页面">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div> -->
                                
                                <div class="current-context" id="currentContext" style="display: none;">
                                    <div style="background: #2d2d2d; border: 1px solid #404040; border-radius: 6px; padding: 12px; margin-bottom: 16px;">
                                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 6px;">
                                            <i class="fas fa-file-alt" style="color: #44D62C;"></i>
                                            <span id="currentPageName" style="font-weight: 600; color: #fff;"></span>
                                        </div>
                                        <div style="font-size: 12px; color: #ccc;" id="currentScenarioName"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="section-title">
                                <i class="fas fa-cubes"></i>
                                Component Library
                            </div>
                            
                            <div style="margin-bottom: 25px;">
                                <h4 style="color: #cccccc; margin-bottom: 10px; font-size: 14px;">Basic Components</h4>
                                <div class="components-grid">
                                    <div class="component-item component-item-grid" draggable="true" data-component="text-input" ondragstart="handleDragStart(event)">
                                        <i class="fas fa-edit" style="color: #44D62C; margin-right: 6px;"></i>
                                        <span>Text Input</span>
                                </div>
                                    <div class="component-item component-item-grid" draggable="true" data-component="select-dropdown" ondragstart="handleDragStart(event)">
                                        <i class="fas fa-list" style="color: #44D62C; margin-right: 6px;"></i>
                                        <span>Select</span>
                                </div>
                                    <div class="component-item component-item-grid" draggable="true" data-component="button" ondragstart="handleDragStart(event)">
                                        <i class="fas fa-mouse-pointer" style="color: #44D62C; margin-right: 6px;"></i>
                                        <span>Button</span>
                                </div>
                                    <div class="component-item component-item-grid" draggable="true" data-component="data-table" ondragstart="handleDragStart(event)">
                                        <i class="fas fa-table" style="color: #44D62C; margin-right: 6px;"></i>
                                        <span>Table</span>
                                    </div>
                                    <div class="component-item component-item-grid" draggable="true" data-component="form-section" ondragstart="handleDragStart(event)">
                                        <i class="fas fa-layer-group" style="color: #44D62C; margin-right: 6px;"></i>
                                        <span>Form</span>
                                    </div>
                                    <div class="component-item component-item-grid" draggable="true" data-component="card" ondragstart="handleDragStart(event)">
                                        <i class="fas fa-id-card" style="color: #44D62C; margin-right: 6px;"></i>
                                        <span>Card</span>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h4 style="color: #cccccc; margin-bottom: 10px; font-size: 14px;">Page Components</h4>
                                <div class="smart-generation-grid">
                                    <div class="component-item smart-generate-item" style="background: #404040; cursor: pointer;" onclick="generateSmartPage('list')">
                                        <i class="fas fa-rectangle-list" style="color: #44D62C; margin-right: 6px;"></i>
                                        <span>List Page</span>
                                </div>
                                    <div class="component-item smart-generate-item" style="background: #404040; cursor: pointer;" onclick="generateSmartPage('form')">
                                        <i class="fas fa-text-slash" style="color: #44D62C; margin-right: 6px;"></i>
                                        <span>Form Page</span>
                                    </div>
                                    <!-- <div class="component-item smart-generate-item" style="background: #404040; cursor: pointer; grid-column: 1 / -1;" onclick="generateSmartPage('detail')">
                                        <i class="fas fa-magic" style="color: #000; margin-right: 6px;"></i>
                                        <span>Detail Page</span>
                                    </div> -->
                                </div>
                            </div>
                        </div>

                        <!-- 主编辑区域 -->
                        <div class="editor-main">
                            <!-- 场景上下文 -->
                            <div class="scenario-context" id="scenarioContext" style="display: none;">
                                <div class="context-info">
                                    <div class="context-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="context-details">
                                        <h3 class="context-title">User Management</h3>
                                        <p class="context-desc">Editing scenario: demo-user-management</p>
                                    </div>
                                </div>
                                <div class="context-stats">
                                    <span class="context-stat">
                                        <i class="fas fa-list"></i> 7 fields
                                    </span>
                                    <span class="context-stat">
                                        <i class="fas fa-file-alt"></i> 3 pages
                                    </span>
                                    <span class="context-stat context-status-active">
                                        <i class="fas fa-circle"></i> Active
                                    </span>
                                </div>
                            </div>

                            <!-- 工具栏 -->
                            <div class="editor-toolbar">
                                <div style="display: flex; align-items: center; gap: 15px;">
                                    <select class="form-input" style="width: 250px;" onchange="handlePageChange(this.value)">
                                        <option value="">Select Page</option>
                                        <option value="list">List Page (CRUD Table)</option>
                                        <option value="form">Form Page (Add/Edit)</option>
                                        <option value="detail">Detail Page (View)</option>
                                        <option value="custom">Custom Page</option>
                                    </select>
                                    <!-- <button class="btn-icon" style="width: 36px; height: 36px;" onclick="showNewPageModal()" title="New Page">
                                        <i class="fas fa-plus"></i>
                                    </button> -->
                                    <span style="color: #44D62C; font-size: 12px;" id="saveStatus">
                                        <i class="fas fa-circle"></i> Ready
                                    </span>
                                </div>
                                <div style="display: flex; gap: 10px;">
                                    <button class="btn" onclick="savePage()">New Page</button>
                                    <button class="btn btn-secondary"  onclick="previewPage()">Preview</button>
                                    <button class="btn btn-secondary"  onclick="publishPage()">Publish</button>
                                </div>
                            </div>

                            <!-- 画布区域 -->
                            <div class="editor-canvas">
                                <div class="canvas-area" ondrop="drop(event)" ondragover="allowDrop(event)">
                                    <div class="empty-state">
                                        <i class="fas fa-plus-circle" style="font-size: 3rem; color: #666; margin-bottom: 15px;"></i>
                                        <p>Drag components here to start designing</p>
                                        <p style="font-size: 14px; color: #999; margin-top: 10px;">Or auto-generate page templates from metadata</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧属性面板 -->
                        <div class="properties-panel">
                            <div class="section-title" style="display: flex; justify-content: space-between; align-items: center;">
                                <span>
                                <i class="fas fa-cog"></i>
                                Properties
                                </span>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn-icon" title="Reset to Default" onclick="resetProperties()">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="btn-icon" title="Quick Search" onclick="togglePropertySearch()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 快速搜索框 -->
                            <div id="property-search" class="property-search" style="display: none;">
                                <input type="text" placeholder="Search properties..." class="form-input" style="margin-bottom: 16px;" oninput="filterProperties(this.value)">
                            </div>

                            <!-- 层级选择器 -->
                            <div class="property-level-selector" style="margin-bottom: 16px;">
                                <div class="level-tabs">
                                    <button class="level-tab active" onclick="switchPropertyLevel('page')" data-level="page">
                                        <i class="fas fa-file-alt"></i> Page Level
                                    </button>
                                    <button class="level-tab" onclick="switchPropertyLevel('component')" data-level="component">
                                        <i class="fas fa-puzzle-piece"></i> Component Level
                                    </button>
                                </div>
                            </div>

                            <!-- 页面级属性 -->
                            <div id="page-level-properties" class="property-level-content">
                                <!-- General Settings -->
                                <details class="properties-group" open>
                                <summary>
                                    <span><i class="fas fa-info-circle" style="margin-right: 8px; color: #44D62C;"></i>General</span>
                                </summary>
                            <div class="form-group">
                                    <div class="form-label-enhanced">
                                        <label class="form-label">Page ID</label>
                                        <span class="form-help" title="Unique identifier for this page">?</span>
                                    </div>
                                    <input id="properties-page-id" type="text" class="form-input" placeholder="page-unique-id" readonly>
                                </div>
                                <div class="form-group">
                                    <div class="form-label-enhanced">
                                        <label class="form-label">Page Module</label>
                                        <span class="form-help" title="Internal name for developers">?</span>
                                    </div>
                                    <input id="properties-page-name" type="text" class="form-input" placeholder="Enter page name" oninput="handlePropertyChange('name', this.value)">
                                </div>
                                <div class="form-group">
                                    <div class="form-label-enhanced">
                                <label class="form-label">Page Name</label>
                                        <span class="form-help" title="Title shown to users">?</span>
                            </div>
                                    <input id="properties-page-title" type="text" class="form-input" placeholder="Enter page title" oninput="handlePropertyChange('title', this.value)">
                                </div>
                            <div class="form-group">
                                    <div class="form-label-enhanced">
                                <label class="form-label">Page Path</label>
                                        <span class="form-help" title="URL route for this page">?</span>
                            </div>
                                    <input id="properties-page-path" type="text" class="form-input" placeholder="/demo/user/list" oninput="handlePropertyChange('path', this.value)">
                                </div>
                            <div class="form-group">
                                    <div class="form-label-enhanced">
                                        <label class="form-label">Layout</label>
                                        <span class="form-help" title="Choose page layout template">?</span>
                                    </div>
                                    <select id="properties-layout" class="form-input" onchange="handlePropertyChange('layout', this.value)">
                                        <option>default</option>
                                        <option>blank</option>
                                        <option>fullscreen</option>
                                </select>
                            </div>
                            </details>

                            <!-- Page Data -->
                            <details id="properties-data-group" class="properties-group form-group">
                                <summary>
                                    <span><i class="fas fa-database" style="margin-right: 8px; color: #44D62C;"></i>Page Data</span>
                                </summary>
                                <div id="properties-data-list" class="column-list" style="gap: 6px; display: flex; flex-direction: column;">
                                   <!-- Dynamically populated -->
                            </div>
                                <div class="form-group">
                                    <button class="btn btn-sm btn-secondary" style="width: 100%; margin-top: 8px;" onclick="showDataConfigModal()"><i class="fas fa-plus"></i> Add Variable</button>
                                </div>
                            </details>

                            <!-- API Settings -->
                            <details id="properties-apis-group" class="properties-group">
                                <summary>
                                    <span><i class="fas fa-plug" style="margin-right: 8px; color: #44D62C;"></i>APIs</span>
                                </summary>
                                <div id="properties-api-list" class="column-list" style="gap: 6px; display: flex; flex-direction: column;">
                                   <!-- Dynamically populated -->
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-sm btn-secondary" style="width: 100%; margin-top: 8px;" onclick="showApiConfigModal()"><i class="fas fa-plus"></i> Add API</button>
                                </div>
                            </details>


                            
                            <!-- Lifecycle -->
                            <details id="properties-lifecycle-group" class="properties-group">
                                <summary>
                                    <span><i class="fas fa-clock" style="margin-right: 8px; color: #44D62C;"></i>Lifecycle</span>
                                </summary>
                                <div class="form-group">
                                     <label class="form-label">onMounted</label>
                                     <button class="btn btn-secondary" style="width: 100%; text-align: left;">
                                         <i class="fas fa-cogs" style="margin-right: 8px;"></i> Configure Actions
                                     </button>
                                </div>
                            </details>

                            <!-- Page Methods -->
                            <details id="properties-methods-group" class="properties-group">
                                <summary>
                                    <span><i class="fas fa-code" style="margin-right: 8px; color: #44D62C;"></i>Page Methods</span>
                                </summary>
                                <div id="properties-methods-list" class="column-list" style="gap: 6px; display: flex; flex-direction: column;">
                                   <!-- Dynamically populated -->
                                    <div class="column-item">
                                        <span><i class="fas fa-bolt"></i> handleSearch</span>
                                        <i class="fas fa-edit" style="cursor:pointer;"></i>
                                    </div>
                                    <div class="column-item">
                                        <span><i class="fas fa-bolt"></i> deleteItem</span>
                                         <i class="fas fa-edit" style="cursor:pointer;"></i>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-sm btn-secondary" style="width: 100%; margin-top: 8px;"><i class="fas fa-plus"></i> Add Method</button>
                                </div>
                            </details>
                            </div>

                            <!-- 组件级属性 -->
                            <div id="component-level-properties" class="property-level-content" style="display: none;">
                                <!-- Selected Component Info -->
                                <div class="selected-component-info" style="margin-bottom: 16px; padding: 12px; background: #262626; border-radius: 6px; border: 1px solid #404040;">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                        <i class="fas fa-table" style="color: #44D62C;"></i>
                                        <strong style="color: #fff;">Table Component</strong>
                                    </div>
                                    <div style="font-size: 12px; color: #ccc;">Configure table-specific properties and behavior</div>
                                </div>

                                <!-- Filters Settings -->
                                <details id="properties-filters-group" class="properties-group">
                                    <summary>
                                        <span><i class="fas fa-filter" style="margin-right: 8px; color: #44D62C;"></i>Filters</span>
                                    </summary>
                                    <div class="form-group-inline">
                                        <label class="form-label">Show Filter Bar</label>
                                        <div class="toggle-switch active"><div class="toggle-switch-handle"></div></div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" style="margin-top: 10px;">Filter Fields</label>
                                        <div id="properties-filter-fields" class="column-list">
                                            <!-- Dynamically populated -->
                                        </div>
                                        <button class="btn btn-sm btn-secondary" style="width: 100%; margin-top: 8px;" onclick="showAddFieldModal('filter')"><i class="fas fa-plus"></i> Add Field</button>
                                    </div>
                                </details>

                                <!-- Toolbar Settings -->
                                <details id="properties-toolbar-group" class="properties-group">
                                    <summary>
                                        <span><i class="fas fa-tools" style="margin-right: 8px; color: #44D62C;"></i>Toolbar</span>
                                    </summary>
                                     <div id="properties-toolbar-buttons" class="column-list" style="gap: 6px; display: flex; flex-direction: column;">
                                       <!-- Dynamically populated -->
                                    </div>
                                    <div class="form-group">
                                        <button class="btn btn-sm btn-secondary" style="width: 100%; margin-top: 8px;"><i class="fas fa-plus"></i> Add Button</button>
                                    </div>
                                </details>

                                <!-- Table Configuration -->
                                <details id="properties-table-group" class="properties-group" open>
                                    <summary>
                                        <span><i class="fas fa-table" style="margin-right: 8px; color: #44D62C;"></i>Table Configuration</span>
                                    </summary>
                                    <div class="form-group-inline">
                                        <label class="form-label">Enable Bulk Selection</label>
                                        <div class="toggle-switch active"><div class="toggle-switch-handle"></div></div>
                                    </div>
                                    <div class="form-group-inline">
                                        <label class="form-label">Enable Sorting</label>
                                        <div class="toggle-switch active"><div class="toggle-switch-handle"></div></div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" style="margin-top: 10px;">Visible Columns</label>
                                        <div id="properties-visible-columns" class="column-list">
                                            <!-- Dynamically populated -->
                                        </div>
                                        <button class="btn btn-sm btn-secondary" style="width: 100%; margin-top: 8px;" onclick="showAddFieldModal('table')"><i class="fas fa-plus"></i> Add Column</button>
                                    </div>
                                </details>

                                <!-- Pagination Settings -->
                                <details id="properties-pagination-group" class="properties-group">
                                    <summary>
                                        <span><i class="fas fa-list-ol" style="margin-right: 8px; color: #44D62C;"></i>Pagination</span>
                                    </summary>
                                    <div class="form-group-inline">
                                        <label class="form-label">Show Pagination</label>
                                        <div class="toggle-switch active"><div class="toggle-switch-handle"></div></div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Default Page Size</label>
                                        <input type="number" class="form-input" value="10">
                                    </div>
                                </details>
                            </div>
                        </div>
                    </div>
                </main>

                <!-- 元数据管理器 -->
                <main class="main-content" id="meta">
                    <div class="page-header">
                        <h1 class="page-title">Metadata Manager</h1>
                        <p class="page-subtitle">Define business scenarios and field structures</p>
                    </div>
                    
                    <div class="meta-container">
                        <div class="meta-header" style="margin-bottom: 10px;">
                            <h2 style="color: #ffffff;">
                                <i class="fas fa-database" style="margin-right: 10px; color: #44D62C;"></i>
                                Business Scenario
                            </h2>
                        </div>
                        <div style="margin-bottom: 24px;">
                            <div style="margin-bottom: 16px;">
                                <label class="form-label" for="meta-scenario-selector">Select Scenario</label>
                                <select id="meta-scenario-selector" class="form-input" style="max-width: 500px;" onchange="handleScenarioSelection('meta', this.value)">
                                    <option value="demo-user-management">demo-user-management</option>
                                    <option value="demo-product-catalog">demo-product-catalog</option>
                                    <option value="demo-order-system">demo-order-system</option>
                                    <option value="demo-user-group">demo-user-group</option>
                                </select>
                            </div>
                            <div id="meta-selected-scenario-card" class="scenario-card active" style="max-width: 500px;">
                                <div class="scenario-name">demo-user-management</div>
                                <div class="scenario-desc">User management system scenario, including user basic information and permission management</div>
                                <div class="scenario-stats">
                                    <span><i class="fas fa-list"></i> 7 fields</span>
                                    <span><i class="fas fa-key"></i> 2 indexes</span>
                                    <span><i class="fas fa-clock"></i> 2024-01-15</span>
                                </div>
                            </div>
                        </div>

                        <div style="margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
                            <h3 style="color: #ffffff;">Field Configuration - <span id="meta-scenario-title">demo-user-management</span></h3>
                            <div style="display: flex; gap: 10px;">
                                <button class="btn" style="background: #44D62C;" onclick="generateCrudPages()">
                                    <i class="fas fa-magic"></i> Generate CRUD Pages
                                </button>
                                <button class="btn btn-secondary">
                                    <i class="fas fa-plus"></i> Add Field
                                </button>
                            </div>
                        </div>

                        <div class="field-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Field Name</th>
                                        <th>Display Name</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Queryable</th>
                                        <th>Default Value</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>name</code></td>
                                        <td>Name</td>
                                        <td><span class="badge">TEXT</span></td>
                                        <td><span class="badge required">Required</span></td>
                                        <td><span class="badge queryable">Queryable</span></td>
                                        <td>-</td>
                                        <td>
                                            <i class="fas fa-edit" style="color: #44D62C; cursor: pointer; margin-right: 10px;"></i>
                                            <i class="fas fa-trash" style="color: #f44336; cursor: pointer;"></i>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><code>email</code></td>
                                        <td>Email Address</td>
                                        <td><span class="badge">EMAIL</span></td>
                                        <td><span class="badge required">Required</span></td>
                                        <td><span class="badge queryable">Queryable</span></td>
                                        <td>-</td>
                                        <td>
                                            <i class="fas fa-edit" style="color: #44D62C; cursor: pointer; margin-right: 10px;"></i>
                                            <i class="fas fa-trash" style="color: #f44336; cursor: pointer;"></i>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><code>user_group</code></td>
                                        <td>User Group</td>
                                        <td><span class="badge">SELECT</span></td>
                                        <td>-</td>
                                        <td><span class="badge queryable">Queryable</span></td>
                                        <td>-</td>
                                        <td>
                                            <i class="fas fa-edit" style="color: #44D62C; cursor: pointer; margin-right: 10px;"></i>
                                            <i class="fas fa-trash" style="color: #f44336; cursor: pointer;"></i>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><code>role</code></td>
                                        <td>User Role</td>
                                        <td><span class="badge">SELECT</span></td>
                                        <td><span class="badge required">Required</span></td>
                                        <td><span class="badge queryable">Queryable</span></td>
                                        <td>user</td>
                                        <td>
                                            <i class="fas fa-edit" style="color: #44D62C; cursor: pointer; margin-right: 10px;"></i>
                                            <i class="fas fa-trash" style="color: #f44336; cursor: pointer;"></i>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><code>status</code></td>
                                        <td>User Status</td>
                                        <td><span class="badge">BOOLEAN</span></td>
                                        <td>-</td>
                                        <td><span class="badge queryable">Queryable</span></td>
                                        <td>true</td>
                                        <td>
                                            <i class="fas fa-edit" style="color: #44D62C; cursor: pointer; margin-right: 10px;"></i>
                                            <i class="fas fa-trash" style="color: #f44336; cursor: pointer;"></i>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><code>created_on</code></td>
                                        <td>Created On</td>
                                        <td><span class="badge">DATETIME</span></td>
                                        <td>-</td>
                                        <td><span class="badge queryable">Queryable</span></td>
                                        <td>-</td>
                                        <td>
                                            <i class="fas fa-edit" style="color: #44D62C; cursor: pointer; margin-right: 10px;"></i>
                                            <i class="fas fa-trash" style="color: #f44336; cursor: pointer;"></i>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><code>last_modified</code></td>
                                        <td>Last Modified</td>
                                        <td><span class="badge">DATETIME</span></td>
                                        <td>-</td>
                                        <td><span class="badge queryable">Queryable</span></td>
                                        <td>-</td>
                                        <td>
                                            <i class="fas fa-edit" style="color: #44D62C; cursor: pointer; margin-right: 10px;"></i>
                                            <i class="fas fa-trash" style="color: #f44336; cursor: pointer;"></i>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
            </div>
        </main>

                <!-- 索引管理器页面 -->
                <main class="main-content" id="index">
                    <div class="page-header">
                        <h1 class="page-title">Index Manager</h1>
                        <p class="page-subtitle">Manage database indexes for optimized queries and performance</p>
                    </div>
                    
                    <div class="meta-container">
                        <div class="meta-header" style="margin-bottom: 10px;">
                            <h2 style="color: #ffffff;">
                                <i class="fas fa-database" style="margin-right: 10px; color: #44D62C;"></i>
                                Business Scenario
                            </h2>
                        </div>

                        <div style="margin-bottom: 24px;">
                            <div style="margin-bottom: 16px;">
                                <label class="form-label" for="index-scenario-selector">Select Scenario</label>
                                <select id="index-scenario-selector" class="form-input" style="max-width: 500px;" onchange="handleScenarioSelection('index', this.value)">
                                    <option value="demo-user-management">demo-user-management</option>
                                    <option value="demo-product-catalog">demo-product-catalog</option>
                                    <option value="demo-order-system">demo-order-system</option>
                                    <option value="demo-user-group">demo-user-group</option>
                                </select>
                            </div>
                            <div id="index-selected-scenario-card" class="scenario-card active" style="max-width: 500px;">
                                <div class="scenario-name">demo-user-management</div>
                                <div class="scenario-desc">User management system indexes for authentication and role-based queries</div>
                                <div class="scenario-stats">
                                    <span><i class="fas fa-key"></i> 2 indexes</span>
                                    <span><i class="fas fa-list"></i> 7 fields</span>
                                    <span><i class="fas fa-clock"></i> 2024-01-15</span>
                                </div>
                            </div>
                        </div>

                        <div style="margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
                            <h3 style="color: #ffffff;">Index Configuration - <span id="index-scenario-title">demo-user-management</span></h3>
                            <div style="display: flex; gap: 10px;">
                                <button class="btn" style="background: #44D62C;">
                                    <i class="fas fa-magic"></i> SAVE
                                </button>
                                <button class="btn btn-secondary">
                                    <i class="fas fa-plus"></i> Add Index
                                </button>
                            </div>
                        </div>

                        <div class="field-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Index Name</th>
                                        <th>Fields</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Performance Impact</th>
                                        <th>Last Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>idx_username_email</code></td>
                                        <td>username, email</td>
                                        <td><span class="badge">COMPOSITE</span></td>
                                        <td><span class="badge queryable">Active</span></td>
                                        <td><span style="color: #44D62C;">+95%</span></td>
                                        <td>2024-01-15</td>
                                        <td>
                                            <i class="fas fa-edit" style="color: #44D62C; cursor: pointer; margin-right: 10px;"></i>
                                            <i class="fas fa-trash" style="color: #f44336; cursor: pointer;"></i>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><code>idx_user_status</code></td>
                                        <td>status</td>
                                        <td><span class="badge">SINGLE</span></td>
                                        <td><span class="badge queryable">Active</span></td>
                                        <td><span style="color: #44D62C;">+78%</span></td>
                                        <td>2024-01-15</td>
                                        <td>
                                            <i class="fas fa-edit" style="color: #44D62C; cursor: pointer; margin-right: 10px;"></i>
                                            <i class="fas fa-trash" style="color: #f44336; cursor: pointer;"></i>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><code>idx_user_role_created</code></td>
                                        <td>role, created_at</td>
                                        <td><span class="badge">COMPOSITE</span></td>
                                        <td><span class="badge required">Building</span></td>
                                        <td><span style="color: #ff9800;">Pending</span></td>
                                        <td>2024-01-14</td>
                                        <td>
                                            <i class="fas fa-edit" style="color: #44D62C; cursor: pointer; margin-right: 10px;"></i>
                                            <i class="fas fa-trash" style="color: #f44336; cursor: pointer;"></i>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </main>
            </div>

    <!-- 创建场景模态框 -->
    <div class="modal" id="createScenarioModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Create New Scenario</h3>
                <button class="close-btn" onclick="hideCreateScenario()">&times;</button>
            </div>
            <div>
                <div class="form-group">
                    <label class="form-label">Scenario Identifier</label>
                    <input type="text" class="form-input" placeholder="e.g.: demo-product-management">
                </div>
                <div class="form-group">
                    <label class="form-label">Scenario Description</label>
                    <textarea class="form-input" rows="3" placeholder="Describe the business purpose of this scenario"></textarea>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button class="btn btn-secondary" style="margin-right: 10px;" onclick="hideCreateScenario()">Cancel</button>
                    <button class="btn">Create Scenario</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建索引模态框 -->
    <div class="modal" id="createIndexModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Create Database Index</h3>
                <button class="close-btn" onclick="hideCreateIndex()">&times;</button>
            </div>
            <div>
                <div class="form-group">
                    <label class="form-label">Index Name</label>
                    <input type="text" class="form-input" placeholder="e.g.: idx_user_email_status">
                </div>
                <div class="form-group">
                    <label class="form-label">Scenario</label>
                    <select class="form-input">
                        <option>demo-user-management</option>
                        <option>demo-product-catalog</option>
                        <option>demo-order-system</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Index Type</label>
                    <select class="form-input">
                        <option>Single Field Index</option>
                        <option>Composite Index</option>
                        <option>Unique Index</option>
                        <option>Partial Index</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Fields (comma-separated)</label>
                    <input type="text" class="form-input" placeholder="e.g.: email, status">
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button class="btn btn-secondary" style="margin-right: 10px;" onclick="hideCreateIndex()">Cancel</button>
                    <button class="btn">Create Index</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Field Modal -->
    <div class="modal" id="addFieldModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="addFieldModalTitle">Add Fields</h3>
                <button class="close-btn" onclick="hideAddFieldModal()">&times;</button>
            </div>
            <div id="addFieldModalBody" style="max-height: 40vh; overflow-y: auto;">
                <!-- Checkboxes will be populated here -->
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button class="btn btn-secondary" style="margin-right: 10px;" onclick="hideAddFieldModal()">Cancel</button>
                <button class="btn" onclick="confirmAddField()">Add Selected</button>
            </div>
        </div>
    </div>

    <!-- API Config Modal -->
    <div class="modal" id="apiConfigModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="apiConfigModalTitle">API Configuration</h3>
                <button class="close-btn" onclick="hideApiConfigModal()">&times;</button>
            </div>
            <div>
                <div class="form-group">
                    <label class="form-label">ID</label>
                    <input type="text" class="form-input" placeholder="e.g., user-list">
                </div>
                <div class="form-group">
                    <label class="form-label">Method</label>
                    <select class="form-input">
                        <option>GET</option>
                        <option>POST</option>
                        <option>PUT</option>
                        <option>DELETE</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">URL</label>
                    <input type="text" class="form-input" placeholder="/api/v1/users">
                </div>
                 <div class="form-group">
                    <label class="form-label">Params</label>
                    <textarea class="form-input" rows="3" placeholder="e.g., {{ getQueryParams(searchQuery) }}"></textarea>
                </div>
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button class="btn btn-secondary" style="margin-right: 10px;" onclick="hideApiConfigModal()">Cancel</button>
                <button class="btn" onclick="confirmSaveApi()">Save</button>
            </div>
        </div>
    </div>

    <!-- Data Config Modal -->
    <div class="modal" id="dataConfigModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="dataConfigModalTitle">Data Variable Configuration</h3>
                <button class="close-btn" onclick="hideDataConfigModal()">&times;</button>
            </div>
            <div>
                <div class="form-group">
                    <label class="form-label">Variable Name</label>
                    <input type="text" class="form-input" placeholder="e.g., isLoading">
                </div>
                <div class="form-group">
                    <label class="form-label">Initial Value</label>
                    <textarea class="form-input" rows="3" placeholder="e.g., true, 'some text', 123, []"></textarea>
                </div>
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button class="btn btn-secondary" style="margin-right: 10px;" onclick="hideDataConfigModal()">Cancel</button>
                <button class="btn" onclick="confirmSaveData()">Save</button>
            </div>
        </div>
    </div>

    <script>
        // 页面切换功能
        function switchPage(pageName) {
            // 隐藏所有页面
            document.querySelectorAll('.main-content').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(pageName).classList.add('active');
            
            // 更新导航状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelector(`[data-page="${pageName}"]`).classList.add('active');
        }

        // 导航点击事件
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                switchPage(page);
            });
        });



        // 功能卡片点击事件
        document.querySelectorAll('.feature-card[data-target]').forEach(card => {
            card.addEventListener('click', () => {
                const target = card.getAttribute('data-target');
                switchPage(target);
            });
        });

        // 场景选择
        function selectScenario(element) {
            document.querySelectorAll('.scenario-card').forEach(card => {
                card.classList.remove('active');
            });
            element.classList.add('active');
        }

        // 拖拽功能
        let draggedComponent = null;

        function handleDragStart(ev) {
            draggedComponent = ev.target.getAttribute('data-component');
            ev.dataTransfer.effectAllowed = 'copy';
            ev.target.style.opacity = '0.5';
        }

        function allowDrop(ev) {
            ev.preventDefault();
            ev.dataTransfer.dropEffect = 'copy';
        }

        function drop(ev) {
            ev.preventDefault();
            const canvas = ev.currentTarget;
            
            if (draggedComponent) {
                insertComponent(canvas, draggedComponent);
                draggedComponent = null;
            }
        }

        // 组件模板
        const componentTemplates = {
            'text-input': {
                name: 'Text Input',
                icon: 'fas fa-edit',
                html: `
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label class="form-label">Text Input Label</label>
                        <input type="text" class="form-input" placeholder="Enter text here">
                    </div>
                `
            },
            'select-dropdown': {
                name: 'Select Dropdown',
                icon: 'fas fa-list',
                html: `
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label class="form-label">Select Label</label>
                        <select class="form-input">
                            <option>Option 1</option>
                            <option>Option 2</option>
                            <option>Option 3</option>
                        </select>
                    </div>
                `
            },
            'button': {
                name: 'Button',
                icon: 'fas fa-mouse-pointer',
                html: `
                    <button class="btn" style="margin: 10px 0;">
                        <i class="fas fa-check" style="margin-right: 8px;"></i>
                        Button Text
                    </button>
                `
            },
            'data-table': {
                name: 'Data Table',
                icon: 'fas fa-table',
                html: `
                    <div class="field-table" style="margin: 20px 0;">
                        <table>
                            <thead>
                                <tr>
                                    <th>Column 1</th>
                                    <th>Column 2</th>
                                    <th>Column 3</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Sample Data 1</td>
                                    <td>Sample Data 2</td>
                                    <td>Sample Data 3</td>
                                    <td><i class="fas fa-edit" style="color: #44D62C; cursor: pointer;"></i></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `
            },
            'form-section': {
                name: 'Form Section',
                icon: 'fas fa-layer-group',
                html: `
                    <div style="background: #2d2d2d; border: 1px solid #404040; border-radius: 6px; padding: 20px; margin: 20px 0;">
                        <h3 style="color: #ffffff; margin-bottom: 16px;">Form Section Title</h3>
                        <div class="form-group" style="margin-bottom: 16px;">
                            <label class="form-label">Field 1</label>
                            <input type="text" class="form-input" placeholder="Enter value">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Field 2</label>
                            <input type="text" class="form-input" placeholder="Enter value">
                        </div>
                    </div>
                `
            },
            'card': {
                name: 'Card Container',
                icon: 'fas fa-id-card',
                html: `
                    <div style="background: #2d2d2d; border: 1px solid #404040; border-radius: 6px; padding: 20px; margin: 20px 0;">
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                            <div style="width: 48px; height: 48px; background: #44D62C; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-user" style="color: #000; font-size: 20px;"></i>
                            </div>
                            <div>
                                <h4 style="color: #ffffff; margin: 0;">Card Title</h4>
                                <p style="color: #cccccc; margin: 4px 0 0 0; font-size: 14px;">Card description text</p>
                            </div>
                        </div>
                        <p style="color: #ffffff; line-height: 1.5;">Card content goes here. You can add any content inside this card container.</p>
                    </div>
                `
            }
        };

        function insertComponent(canvas, componentType) {
            const template = componentTemplates[componentType];
            if (!template) return;

            // 如果画布是空的，替换空状态
            const emptyState = canvas.querySelector('.empty-state');
            if (emptyState) {
                canvas.innerHTML = `
                    <div class="canvas-content" style="width: 100%; max-width: 800px;">
                        <div class="component-wrapper" data-component="${componentType}" onclick="selectComponent(this)">
                            <div class="component-overlay">
                                <span class="component-label">
                                    <i class="${template.icon}"></i> ${template.name}
                                </span>
                                <div class="component-actions">
                                    <button class="btn-icon" onclick="event.stopPropagation(); editComponent(this.closest('.component-wrapper'))" title="编辑组件">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="event.stopPropagation(); deleteComponent(this.closest('.component-wrapper'))" title="删除组件">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            ${template.html}
                        </div>
                    </div>
                `;
            } else {
                // 添加到现有内容
                const canvasContent = canvas.querySelector('.canvas-content');
                if (canvasContent) {
                    const newComponent = document.createElement('div');
                    newComponent.className = 'component-wrapper';
                    newComponent.setAttribute('data-component', componentType);
                    newComponent.onclick = () => selectComponent(newComponent);
                    newComponent.innerHTML = `
                        <div class="component-overlay">
                            <span class="component-label">
                                <i class="${template.icon}"></i> ${template.name}
                            </span>
                            <div class="component-actions">
                                <button class="btn-icon" onclick="event.stopPropagation(); editComponent(this.closest('.component-wrapper'))" title="编辑组件">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon" onclick="event.stopPropagation(); deleteComponent(this.closest('.component-wrapper'))" title="删除组件">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        ${template.html}
                    `;
                    canvasContent.appendChild(newComponent);
                }
            }

            // 更新保存状态
            const saveStatus = document.getElementById('saveStatus');
            if (saveStatus) {
                saveStatus.innerHTML = '<i class="fas fa-edit"></i> Modified';
                saveStatus.style.color = '#ff9800';
            }
        }

        // 模态框控制
        function showCreateScenario() {
            document.getElementById('createScenarioModal').classList.add('active');
        }

        function hideCreateScenario() {
            document.getElementById('createScenarioModal').classList.remove('active');
        }

        // 当前选中的场景
        let currentScenario = null;
        let currentPageConfig = {
            id: '',
            name: '',
            title: '',
            path: '',
            layout: 'default',
            data: {},
            apis: [],
            toolbar: {
                buttons: []
            },
            filterFields: [],
            tableColumns: []
        };

        let sortableInstances = {};

        // 场景数据
        const scenarios = {
            'demo-user-management': {
                name: 'User Management',
                description: 'User management system with authentication and role-based access control',
                fields: [
                    { name: 'email', displayName: 'Email', type: 'TEXT', queryable: true },
                    { name: 'name', displayName: 'Name', type: 'TEXT', queryable: false },
                    { name: 'user_group', displayName: 'User Group', type: 'SELECT', queryable: true },
                    { name: 'role', displayName: 'Role', type: 'SELECT', queryable: true },
                    { name: 'created_on', displayName: 'Created On', type: 'DATETIME', queryable: true },
                    { name: 'last_modified', displayName: 'Last Modified', type: 'DATETIME', queryable: true },
                    { name: 'status', displayName: 'Status', type: 'SELECT', queryable: true },
                ],
                pages: 3,
                indexes: 2,
                status: 'Active',
                lastModified: '2024-01-15'
            },
            'demo-user-group': {
                name: 'User Group',
                description: 'Manage user groups and their properties.',
                fields: [
                    { name: 'group_name', displayName: 'User Group Name', type: 'TEXT', required: true },
                    { name: 'description', displayName: 'Description', type: 'TEXT', required: false },
                    { name: 'enabled', displayName: 'Enabled', type: 'BOOLEAN', required: false },
                ],
                pages: 2,
                indexes: 1,
                status: 'Active',
                lastModified: '2024-07-10'
            },
            'demo-product-catalog': {
                name: 'Product Catalog',
                description: 'Complete product management with categories, inventory tracking and pricing',
                fields: 12,
                pages: 5,
                indexes: 3,
                status: 'Draft',
                lastModified: '2024-01-10'
            },
            'demo-order-system': {
                name: 'Order Management',
                description: 'End-to-end order processing with payment integration and fulfillment tracking',
                fields: 15,
                pages: 8,
                indexes: 4,
                status: 'Active',
                lastModified: '2024-01-08'
            }
        };

        const indexScenarios = {
            'demo-user-management': {
                description: 'User management system indexes for authentication and role-based queries',
            },
            'demo-product-catalog': {
                description: 'Product catalog indexes for category, inventory, and pricing queries',
            },
            'demo-order-system': {
                description: 'Order system indexes for order processing and tracking queries',
            }
        };

        // 打开场景编辑器
        function openScenarioEditor(scenarioId) {
            currentScenario = scenarios[scenarioId];
            
            // 初始化页面配置
            const baseName = currentScenario.name.toLowerCase().replace(/\s+/g, '-');
            currentPageConfig = {
                id: `${baseName}-list`,
                name: `${currentScenario.name} List`,
                title: `${currentScenario.name} List`,
                path: `/${baseName}/list`,
                layout: 'default',
                data: {
                    searchQuery: {},
                    isLoading: false,
                    accounts: [],
                    totalUsers: 0
                },
                apis: [
                    { id: 'user-list', method: 'POST', url: '/api/admin-api/v1/account/page', params: '{{getQueryParams(searchQuery)}}' },
                    { id: 'user-delete', method: 'DELETE', url: '/api/admin-api/v1/account/:id', params: '' },
                ],
                toolbar: {
                    buttons: [
                        { text: 'CREATE', action: 'navigateToCreate', style: 'primary' }
                    ]
                },
                filterFields: [],
                tableColumns: []
            };
            
            // 更新编辑器标题和场景信息
            updateEditorScenario(scenarioId);
            
            // 切换到编辑器页面
            switchPage('editor');
        }

        // 场景图标映射
        const scenarioIcons = {
            'demo-user-management': 'fas fa-users',
            'demo-product-catalog': 'fas fa-shopping-bag',
            'demo-order-system': 'fas fa-shopping-cart'
        };

        // 更新编辑器场景信息
        function updateEditorScenario(scenarioId) {
            const scenario = scenarios[scenarioId];
            
            // 显示场景上下文
            const contextDiv = document.getElementById('scenarioContext');
            if (contextDiv) {
                contextDiv.style.display = 'flex';
                
                // 更新上下文信息
                const contextIcon = contextDiv.querySelector('.context-icon i');
                const contextTitle = contextDiv.querySelector('.context-title');
                const contextDesc = contextDiv.querySelector('.context-desc');
                const contextStats = contextDiv.querySelectorAll('.context-stat');
                
                if (contextIcon) contextIcon.className = scenarioIcons[scenarioId] || 'fas fa-cube';
                if (contextTitle) contextTitle.textContent = scenario.name;
                if (contextDesc) contextDesc.textContent = `Editing scenario: ${scenarioId}`;
                
                // 更新统计信息
                if (contextStats.length >= 3) {
                    contextStats[0].innerHTML = `<i class="fas fa-list"></i> ${Array.isArray(scenario.fields) ? scenario.fields.length : scenario.fields} fields`;
                    contextStats[1].innerHTML = `<i class="fas fa-file-alt"></i> ${scenario.pages} pages`;
                    contextStats[2].innerHTML = `<i class="fas fa-circle"></i> ${scenario.status}`;
                    contextStats[2].className = `context-stat context-status-${scenario.status.toLowerCase()}`;
                }
            }
            
            // 更新属性面板
            updatePropertiesPanel();
        }

        // 处理页面模板选择
        function handlePageChange(pageType) {
            if (!currentScenario) {
                return;
            }

            const baseName = currentScenario.name;
            const basePath = `/${currentScenario.name.toLowerCase().replace(/\s+/g, '-')}`;
            const baseId = `/${currentScenario.name.toLowerCase().replace(/\s+/g, '-')}`;

            switch(pageType) {
                case 'list':
                    currentPageConfig.id = `${baseId}-list`;
                    currentPageConfig.name = `${baseName} List`;
                    currentPageConfig.title = `${baseName} List`;
                    currentPageConfig.path = `${basePath}/list`;
                    break;
                case 'form':
                    currentPageConfig.id = `${baseId}-form`;
                    currentPageConfig.name = `Create New ${baseName}`;
                    currentPageConfig.title = `Create New ${baseName}`;
                    currentPageConfig.path = `${basePath}/create`;
                    break;
                case 'detail':
                    currentPageConfig.id = `${baseId}-detail`;
                    currentPageConfig.name = `${baseName} Details`;
                    currentPageConfig.title = `${baseName} Details`;
                    currentPageConfig.path = `${basePath}/view`;
                    break;
                default:
                    currentPageConfig.id = `${baseId}-custom`;
                    currentPageConfig.name = `${baseName} Custom Page`;
                    currentPageConfig.title = `${baseName} Custom Page`;
                    currentPageConfig.path = `${basePath}/custom`;
            }
            
            const canvas = document.querySelector('.canvas-area');
            const saveStatus = document.getElementById('saveStatus');
            
            if (pageType && canvas) {
                // 生成页面模板
                generatePageTemplate(pageType, canvas);

                // 更新属性面板
                if(isListPage){
                    currentPageConfig.filterFields = currentScenario.fields.filter(f => f.queryable);
                    currentPageConfig.tableColumns = [...currentScenario.fields];
                    updateFilterProperties();
                    updateTableProperties();
                    updateApiProperties();
                    updateDataProperties();
                    updateToolbarProperties();
                }
                updatePropertiesPanel();
                
                // 更新保存状态
                if (saveStatus) {
                    saveStatus.innerHTML = '<i class="fas fa-edit"></i> Modified';
                    saveStatus.style.color = '#ff9800';
                }
            }
            // 根据页面类型，更新属性面板的可见性
            updatePropertiesVisibility(pageType);
            
            // 如果当前在组件级视图，也需要更新
            if (document.getElementById('component-level-properties').style.display !== 'none') {
                updateSelectedComponentInfo();
            }
        }

        // 生成页面模板
        function generatePageTemplate(pageType, canvas) {
            if (!currentScenario || !currentScenario.fields) {
                canvas.innerHTML = '<p style="color: #ff9800;">Error: Scenario field definitions are missing.</p>';
                return;
            }

            // Align content to the top, not center, and add padding to canvas
            canvas.style.alignItems = 'flex-start';
            canvas.style.padding = '20px';

            let templateContent = '';
            const fields = currentScenario.fields;
            
            switch(pageType) {
                case 'list':
                    const filtersHtml = generateFilters(fields);
                    const tableHtml = generateTable(fields);
                    const paginationHtml = generatePagination();

                    templateContent = `
                        <div style="background: #1a1a1a; border-radius: 6px; width: 100%; padding: 16px;">
                            <!-- Page Header -->
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                                <h1 class="page-title" style="margin: 0;">Users</h1>
                                <button class="btn" style="white-space: nowrap;">
                                    <i class="fas fa-plus" style="margin-right: 5px;"></i> CREATE
                                </button>
                            </div>

                            <!-- Filters Toolbar -->
                            <div style="display: flex; flex-wrap: wrap; gap: 16px; margin-bottom: 24px;">
                                ${filtersHtml}
                            </div>

                            <!-- Table Section -->
                            <div class="field-table">
                                ${tableHtml}
                            </div>

                            <!-- Pagination Section -->
                            <div style="margin-top: 20px;">
                                ${paginationHtml}
                            </div>
                        </div>
                    `;
                    break;
                case 'form':
                    const editableFields = fields.filter(f => !['created_on', 'last_modified'].includes(f.name));
                    
                    const formFieldsHtml = editableFields.map(field => {
                        let fieldHtml = '';
                        const label = field.required ? `${field.displayName}*` : `${field.displayName} (Optional)`;

                        if (field.type === 'BOOLEAN') {
                            fieldHtml = `
                                <div style="display: flex; align-items: center; justify-content: space-between; padding: 10px 0;">
                                    <label class="form-label" style="margin-bottom: 0;">${field.displayName}</label>
                                    <div class="toggle-switch active"><div class="toggle-switch-handle"></div></div>
                                </div>
                            `;
                        } else {
                            let inputHtml = '';
                            const placeholder = `Type a ${field.displayName}`;
                            if (field.type === 'SELECT') {
                                inputHtml = `<select class="form-input"><option value="">Select ${field.displayName}</option></select>`;
                            } else {
                                inputHtml = `<input type="${field.type === 'EMAIL' ? 'email' : 'text'}" class="form-input" placeholder="${placeholder}">`;
                            }
                            fieldHtml = `
                                <div style="margin-bottom: 20px;">
                                    <label class="form-label">${label}</label>
                                    ${inputHtml}
                                </div>
                            `;
                        }
                        return fieldHtml;
                    }).join('');

                    templateContent = `
                        <div style="width: 100%; background: #1a1a1a; border-radius: 6px; padding: 16px;">
                            <h1 class="page-title" style="margin-bottom: 24px;">Create New ${currentScenario.name}</h1>
                            <div style="background: #2d2d2d; border-radius: 8px; padding: 32px; max-width: 800px;">
                                <h3 style="font-weight: 500; font-size: 18px; margin-bottom: 24px;">1. Enter ${currentScenario.name}'s Info</h3>
                                <form>
                                    ${formFieldsHtml}
                            </form>
                            </div>
                            <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px; max-width: 800px;">
                                <button class="btn btn-secondary">CANCEL</button>
                                <button class="btn">SAVE</button>
                            </div>
                        </div>
                    `;
                    break;
                case 'detail':
                    templateContent = `
                        <div style="background: #2d2d2d; border: 1px solid #555; border-radius: 6px; padding: 20px; width: 100%; max-width: 600px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h2 style="color: #ffffff; margin: 0;">${currentScenario.name} Details</h2>
                                <div>
                                    <button style="background: #44D62C; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 8px;">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button style="background: #f44336; color: white; border: none; padding: 8px 16px; border-radius: 4px;">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                <div>
                                    <label style="display: block; color: #cccccc; font-size: 12px; margin-bottom: 4px;">NAME</label>
                                    <p style="color: #ffffff; margin: 0;">Sample User</p>
                                </div>
                                <div>
                                    <label style="display: block; color: #cccccc; font-size: 12px; margin-bottom: 4px;">EMAIL</label>
                                    <p style="color: #ffffff; margin: 0;"><EMAIL></p>
                                </div>
                                <div>
                                    <label style="display: block; color: #cccccc; font-size: 12px; margin-bottom: 4px;">STATUS</label>
                                    <span style="background: #44D62C; padding: 2px 8px; border-radius: 4px; font-size: 12px; color: white;">Active</span>
                                </div>
                                <div>
                                    <label style="display: block; color: #cccccc; font-size: 12px; margin-bottom: 4px;">CREATED</label>
                                    <p style="color: #ffffff; margin: 0;">2024-01-15</p>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                default:
                    templateContent = `
                        <div style="background: #404040; border: 1px solid #555; border-radius: 6px; padding: 20px; max-width: 400px; text-align: center;">
                            <i class="fas fa-plus-circle" style="font-size: 3rem; color: #666; margin-bottom: 15px;"></i>
                            <h4 style="margin-bottom: 15px; color: #ffffff;">Custom Page Template</h4>
                            <p style="color: #cccccc;">Start building your custom page by dragging components from the sidebar</p>
                        </div>
                    `;
            }
            
            canvas.innerHTML = templateContent;
        }

        function generateFilters(fields) {
            const queryableFields = fields.filter(f => f.queryable);
            return queryableFields.map(field => {
                let filterInput = '';
                if (field.type === 'SELECT' || field.type === 'DATETIME') {
                    const placeholder = `Select ${field.displayName}`;
                    filterInput = `<select class="form-input"><option value="">${placeholder}</option></select>`;
                } else {
                    const placeholder = `Search ${field.displayName}`;
                    filterInput = `<input type="text" class="form-input" placeholder="${placeholder}">`;
                }
                
                return `<div style="flex: 1 1 150px;">${filterInput}</div>`;
            }).join('');
        }

        function generateTable(fields) {
            const tableHeaders = ['No', 'Email', 'Name', 'User Groups & Roles', 'Created On', 'Last Modified', 'Status', 'Actions'];
            const headerHtml = tableHeaders.map(h => `<th>${h}</th>`).join('');

            const mockData = [
                { no: 1, email: '<EMAIL>', name: 'KC Chong', groups_roles: 'Razer QA (Super Admin) & Super Admin (QA)', created: '2025-07-03 19:49:42', modified: '2025-07-03 19:49:42', status: true },
                { no: 2, email: '<EMAIL>', name: '', groups_roles: '2 groups & 2 miles <i class="fas fa-chevron-down" style="font-size: 12px; color: #ccc; vertical-align: middle;"></i>', created: '2025-06-30 19:05:58', modified: '2025-06-30 09:21:45', status: true },
                { no: 3, email: '<EMAIL>', name: 'Jun Yong Heng', groups_roles: '2 groups & 3 miles <i class="fas fa-chevron-down" style="font-size: 12px; color: #ccc; vertical-align: middle;"></i>', created: '2025-06-18 15:02:28', modified: '2025-06-19 10:24:12', status: true },
                { no: 4, email: '<EMAIL>', name: 'Li Ting Teh', groups_roles: 'MerchantOnboardGroup & Merchant Onboarding Manager', created: '2025-06-16 17:58:03', modified: '2025-06-16 17:58:03', status: true },
                { no: 5, email: '<EMAIL>', name: 'Charlene Shawna Patrick', groups_roles: 'MerchantOnboardGroup & Merchant Onboarding Manager', created: '2025-06-16 17:57:51', modified: '2025-06-16 17:57:51', status: true },
                { no: 6, email: '<EMAIL>', name: 'Phooi Yee Chai', groups_roles: 'MerchantOnboardGroup & Merchant Onboarding Manager', created: '2025-06-16 17:57:35', modified: '2025-06-16 17:57:35', status: true },
                { no: 7, email: '<EMAIL>', name: 'Desmond Tan', groups_roles: 'MerchantOnboardGroup & Merchant Onboarding Staff', created: '2025-06-16 17:57:19', modified: '2025-06-16 17:57:19', status: true },
                { no: 8, email: '<EMAIL>', name: 'Lee Wen, Ong', groups_roles: 'MerchantOnboardGroup & Merchant Onboarding Staff', created: '2025-06-16 17:56:59', modified: '2025-06-16 17:56:59', status: true },
                { no: 9, email: '<EMAIL>', name: '', groups_roles: 'Super Admin & Super Admin', created: '2025-06-10 13:52:28', modified: '2025-06-30 09:22:21', status: false },
                { no: 10, email: '<EMAIL>', name: '', groups_roles: 'Super Admin & Super Admin', created: '2025-06-10 13:52:24', modified: '2025-06-30 09:22:21', status: false },
            ];

            const bodyHtml = mockData.map(row => `
                <tr style="vertical-align: middle;">
                    <td><input type="checkbox" style="width: 16px; height: 16px; vertical-align: middle; background: transparent; border: 1px solid #555;"></td>
                    <td>${row.no}</td>
                    <td>${row.email}</td>
                    <td>${row.name}</td>
                    <td>${row.groups_roles}</td>
                    <td>${row.created}</td>
                    <td>${row.modified}</td>
                    <td><div class="toggle-switch ${row.status ? 'active' : ''}"><div class="toggle-switch-handle"></div></div></td>
                    <td><i class="fas fa-edit" style="color: #ccc; cursor: pointer; font-size: 16px;"></i></td>
                </tr>
            `).join('');

            return `
                <table>
                    <thead>
                        <tr>
                            <th><input type="checkbox" style="width: 16px; height: 16px; background: transparent; border: 1px solid #555;"></th>
                            ${headerHtml}
                        </tr>
                    </thead>
                    <tbody>
                        ${bodyHtml}
                    </tbody>
                </table>
            `;
        }

        function generatePagination() {
            return `
                <div style="display: flex; justify-content: space-between; align-items: center; color: #ccc; font-size: 14px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span>Items per page:</span>
                        <select class="form-input" style="width: 70px; padding: 4px 8px;">
                            <option>10</option>
                            <option>20</option>
                            <option>50</option>
                        </select>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span>1-10 of 125</span>
                        <div class="pagination-controls" style="display: flex; gap: 5px; align-items: center;">
                            <button style="background:none; border:none; color:#666; cursor:not-allowed;"><i class="fas fa-angle-double-left"></i></button>
                            <button style="background:none; border:none; color:#666; cursor:not-allowed;"><i class="fas fa-angle-left"></i></button>
                            
                            <button class="btn btn-sm" style="width: 32px; height: 32px; background: #44D62C; color: #000; border-color: #44D62C;">1</button>
                            <button class="btn btn-secondary btn-sm" style="width: 32px; height: 32px;">2</button>
                            <button class="btn btn-secondary btn-sm" style="width: 32px; height: 32px;">3</button>
                            <button class="btn btn-secondary btn-sm" style="width: 32px; height: 32px;">4</button>
                            <span style="padding: 0 5px;">...</span>
                            <button class="btn btn-secondary btn-sm" style="width: 32px; height: 32px;">13</button>

                            <button style="background:none; border:none; color:#ccc; cursor:pointer;"><i class="fas fa-angle-right"></i></button>
                            <button style="background:none; border:none; color:#ccc; cursor:pointer;"><i class="fas fa-angle-double-right"></i></button>
                        </div>
                    </div>
                </div>
            `;
        }

        // 保存页面
        function savePage() {
            const saveStatus = document.getElementById('saveStatus');
            if (saveStatus) {
                saveStatus.innerHTML = '<i class="fas fa-check"></i> Saved';
                saveStatus.style.color = '#44D62C';
            }
            
            // 模拟保存延迟
            setTimeout(() => {
                if (saveStatus) {
                    saveStatus.innerHTML = '<i class="fas fa-circle"></i> Ready';
                    saveStatus.style.color = '#44D62C';
                }
            }, 2000);
        }

        // 预览页面
        function previewPage() {
            alert('Preview functionality would open the page in a new window or modal');
        }

        // 发布页面
        function publishPage() {
            alert('Publish functionality would deploy the page to production');
        }

        // 显示场景菜单
        function showScenarioMenu(element) {
            // 这里可以添加场景的右键菜单功能
            // 比如：编辑、复制、删除等
            // alert('Scenario menu: Edit, Duplicate, Delete options would be available here');
        }

        // 索引管理器相关函数
        function selectIndexScenario(element) {
            document.querySelectorAll('.scenario-card').forEach(card => {
                card.classList.remove('active');
            });
            element.classList.add('active');
        }

        function showCreateIndex() {
            document.getElementById('createIndexModal').classList.add('active');
        }

        function hideCreateIndex() {
            document.getElementById('createIndexModal').classList.remove('active');
        }

        // 生成CRUD页面
        function generateCrudPages() {
            const scenarioId = document.getElementById('meta-scenario-selector').value;
            if (!scenarioId) {
                alert('Please select a scenario first.');
                return;
            }
            
            // 切换到编辑器并设置上下文
            openScenarioEditor(scenarioId);
            
            // 将页面模板选择器设置为"List Page"并触发生成
            const pageSelector = document.querySelector('#editor .form-input');
            if(pageSelector){
                 pageSelector.value = 'list';
                 handlePageChange('list');
            }
        }

        // 更新选择的场景
        function handleScenarioSelection(pagePrefix, scenarioId) {
            const scenario = scenarios[scenarioId];
            const cardElement = document.getElementById(`${pagePrefix}-selected-scenario-card`);
            const titleElement = document.getElementById(`${pagePrefix}-scenario-title`);

            if (!scenario || !cardElement || !titleElement) return;

            let description = '';
            let stats = '';

            if (pagePrefix === 'meta' || pagePrefix === 'index') {
                const scenarioData = pagePrefix === 'meta' ? scenario : { ...scenario, description: indexScenarios[scenarioId]?.description || scenario.description };
                const fieldsText = Array.isArray(scenarioData.fields) ? `${scenarioData.fields.length} fields` : `${scenarioData.fields} fields`;
                description = scenarioData.description;
                
                if (pagePrefix === 'meta') {
                    stats = `
                        <span><i class="fas fa-list"></i> ${fieldsText}</span>
                        <span><i class="fas fa-key"></i> ${scenario.indexes} indexes</span>
                        <span><i class="fas fa-clock"></i> ${scenario.lastModified}</span>
                    `;
                } else { // index page
                     stats = `
                        <span><i class="fas fa-key"></i> ${scenario.indexes} indexes</span>
                        <span><i class="fas fa-list"></i> ${fieldsText}</span>
                        <span><i class="fas fa-clock"></i> ${scenario.lastModified}</span>
                    `;
                }
            }

            cardElement.innerHTML = `
                <div class="scenario-name">${scenarioId}</div>
                <div class="scenario-desc">${description}</div>
                <div class="scenario-stats">${stats}</div>
            `;

            titleElement.textContent = scenarioId;
        }

        // 更新属性面板
        function updatePropertiesPanel() {
            const idInput = document.getElementById('properties-page-id');
            const nameInput = document.getElementById('properties-page-name');
            const titleInput = document.getElementById('properties-page-title');
            const pathInput = document.getElementById('properties-page-path');
            const layoutInput = document.getElementById('properties-layout');

            if (idInput) idInput.value = currentPageConfig.id;
            if (nameInput) nameInput.value = currentPageConfig.name;
            if (titleInput) titleInput.value = currentPageConfig.title;
            if (pathInput) pathInput.value = currentPageConfig.path;
            if (layoutInput) layoutInput.value = currentPageConfig.layout;
        }

        // 处理属性变更
        function handlePropertyChange(property, value) {
            if (currentPageConfig.hasOwnProperty(property)) {
                currentPageConfig[property] = value;
            }

            // 实时更新画布
            if (property === 'title') {
                const canvasTitle = document.querySelector('.canvas-area .page-title');
                if (canvasTitle) {
                    canvasTitle.textContent = value;
                }
            }
        }



        function updateFilterProperties() {
            const container = document.getElementById('properties-filter-fields');
            if (!container) return;
            const fields = currentPageConfig.filterFields;
            container.innerHTML = fields.map((field, index) => `
                <div class="column-item">
                    <span><i class="fas fa-grip-vertical"></i> ${field.displayName}</span>
                    <span class="quick-actions">
                        <i class="fas fa-trash" style="cursor:pointer; color:#f44336;" onclick="handleDeleteField('filter', ${index})" title="Remove field"></i>
                    </span>
                </div>
            `).join('');
            makeListSortable('properties-filter-fields', currentPageConfig.filterFields);
        }

        function updateTableProperties() {
            const container = document.getElementById('properties-visible-columns');
            if (!container) return;
            const fields = currentPageConfig.tableColumns;
            container.innerHTML = fields.map((field, index) => `
                 <div class="column-item">
                    <span><i class="fas fa-grip-vertical"></i> ${field.displayName}</span>
                    <span class="quick-actions">
                        <i class="fas fa-trash" style="cursor:pointer; color:#f44336;" onclick="handleDeleteField('table', ${index})" title="Remove column"></i>
                    </span>
                </div>
            `).join('');
            makeListSortable('properties-visible-columns', currentPageConfig.tableColumns);
        }

        function updateApiProperties() {
            const container = document.getElementById('properties-api-list');
            if (!container) return;
            container.innerHTML = currentPageConfig.apis.map((api, index) => `
                <div class="column-item">
                    <span>
                        <span class="badge" style="background-color: #555;">${api.id}</span>
                        <strong style="margin: 0 8px;">${api.method}</strong>
                        <span>${api.url}</span>
                    </span>
                    <span class="quick-actions">
                        <i class="fas fa-edit" style="cursor:pointer;" onclick="showApiConfigModal(${index})" title="Edit API"></i>
                        <i class="fas fa-trash" style="cursor:pointer; color:#f44336; margin-left: 8px;" onclick="handleDeleteApi(${index})" title="Delete API"></i>
                    </span>
                </div>
            `).join('');
        }

        function updateDataProperties() {
            const container = document.getElementById('properties-data-list');
            if (!container) return;
            container.innerHTML = Object.entries(currentPageConfig.data).map(([key, value]) => `
                <div class="column-item">
                    <span>
                        <strong style="margin-right: 8px;">${key}:</strong>
                        <code style="background-color: #2d2d2d;">${JSON.stringify(value)}</code>
                    </span>
                    <span class="quick-actions">
                        <i class="fas fa-edit" style="cursor:pointer;" onclick="showDataConfigModal('${key}')" title="Edit variable"></i>
                        <i class="fas fa-trash" style="cursor:pointer; color:#f44336; margin-left: 8px;" onclick="handleDeleteData('${key}')" title="Delete variable"></i>
                    </span>
                </div>
            `).join('');
        }

        function handleDeleteField(listType, index) {
            if (listType === 'filter') {
                currentPageConfig.filterFields.splice(index, 1);
                updateFilterProperties();
            } else if (listType === 'table') {
                currentPageConfig.tableColumns.splice(index, 1);
                updateTableProperties();
            }
        }

        function handleDeleteApi(index) {
            currentPageConfig.apis.splice(index, 1);
            updateApiProperties();
        }

        function handleDeleteData(key) {
            delete currentPageConfig.data[key];
            updateDataProperties();
        }

        function makeListSortable(elementId, listArray) {
            const el = document.getElementById(elementId);
            if (!el) return;

            if (sortableInstances[elementId]) {
                sortableInstances[elementId].destroy();
            }

            sortableInstances[elementId] = new Sortable(el, {
                animation: 150,
                handle: '.fa-grip-vertical',
                ghostClass: 'sortable-ghost',
                onEnd: (evt) => {
                    const item = listArray.splice(evt.oldIndex, 1)[0];
                    listArray.splice(evt.newIndex, 0, item);
                },
            });
        }

        let modalContext = { 
            listType: '',
            apiIndex: null 
        };

        function showAddFieldModal(listType) {
            modalContext.listType = listType;
            const modalBody = document.getElementById('addFieldModalBody');
            const modalTitle = document.getElementById('addFieldModalTitle');
            
            let existingFieldNames, availableFields;
            if (listType === 'filter') {
                modalTitle.textContent = 'Add Filter Fields';
                existingFieldNames = currentPageConfig.filterFields.map(f => f.name);
                availableFields = currentScenario.fields.filter(f => f.queryable && !existingFieldNames.includes(f.name));
            } else { // table
                modalTitle.textContent = 'Add Table Columns';
                existingFieldNames = currentPageConfig.tableColumns.map(f => f.name);
                availableFields = currentScenario.fields.filter(f => !existingFieldNames.includes(f.name));
            }

            if(availableFields.length === 0) {
                modalBody.innerHTML = '<p style="color: #ccc; text-align: center;">No more fields to add.</p>';
            } else {
                modalBody.innerHTML = availableFields.map(field => `
                    <div class="form-group-inline" style="padding: 4px 0;">
                        <label for="add-field-${field.name}" style="flex-grow: 1; cursor: pointer;">${field.displayName}</label>
                        <input type="checkbox" id="add-field-${field.name}" value="${field.name}" style="width: 18px; height: 18px;">
                    </div>
                `).join('');
            }
            
            document.getElementById('addFieldModal').classList.add('active');
        }

        function hideAddFieldModal() {
            document.getElementById('addFieldModal').classList.remove('active');
        }

        function confirmAddField() {
            const modalBody = document.getElementById('addFieldModalBody');
            const checkboxes = modalBody.querySelectorAll('input[type="checkbox"]:checked');
            const selectedFieldNames = Array.from(checkboxes).map(cb => cb.value);

            const fieldsToAdd = currentScenario.fields.filter(f => selectedFieldNames.includes(f.name));

            if (modalContext.listType === 'filter') {
                currentPageConfig.filterFields.push(...fieldsToAdd);
                updateFilterProperties();
            } else if (modalContext.listType === 'table') {
                currentPageConfig.tableColumns.push(...fieldsToAdd);
                updateTableProperties();
            }

            hideAddFieldModal();
        }

        // 演示功能
        function showDemo() {
            alert('🎉 Demo Features:\n\n1. Create user management scenario\n2. Define field structure\n3. Auto-generate CRUD pages\n4. Configure database indexes\n5. Visual interface adjustment\n6. Save and publish\n\nClick OK to switch to Metadata Manager for examples!');
            switchPage('meta');
        }

        // 添加拖拽效果
        document.querySelectorAll('.component-item[draggable="true"]').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', item.textContent);
                item.style.opacity = '0.5';
            });
            
            item.addEventListener('dragend', (e) => {
                item.style.opacity = '1';
            });
        });

        function showApiConfigModal(index = null) {
            modalContext.apiIndex = index;
            const isEditing = index !== null;
            const modalTitle = document.getElementById('apiConfigModalTitle');
            const form = document.querySelector('#apiConfigModal form');
            const idInput = form.querySelector('input[placeholder="e.g., user-list"]');
            
            modalTitle.textContent = isEditing ? 'Edit API Configuration' : 'Add API Configuration';
            
            // Populate form if editing
            // ... logic to be added ...

            document.getElementById('apiConfigModal').classList.add('active');
        }

        function hideApiConfigModal() {
            document.getElementById('apiConfigModal').classList.remove('active');
        }

         function confirmSaveApi() {
            // ... logic to be added ...
            hideApiConfigModal();
        }

        function showDataConfigModal(key = null) {
            // ... logic to be added ...
             document.getElementById('dataConfigModal').classList.add('active');
        }

        function hideDataConfigModal() {
            document.getElementById('dataConfigModal').classList.remove('active');
        }

         function confirmSaveData() {
            // ... logic to be added ...
            hideDataConfigModal();
        }

        function updateToolbarProperties() {
            const container = document.getElementById('properties-toolbar-buttons');
            if (!container) return;
            container.innerHTML = currentPageConfig.toolbar.buttons.map((btn, index) => `
                <div class="column-item">
                    <span>
                        <i class="fas fa-mouse-pointer" style="margin-right: 8px;"></i>
                        <strong>${btn.text}</strong>
                        <span style="color: #ccc; margin-left: 8px;">=> ${btn.action}</span>
                    </span>
                    <span class="quick-actions">
                        <i class="fas fa-edit" style="cursor:pointer;" title="Edit button"></i>
                        <i class="fas fa-trash" style="cursor:pointer; color:#f44336; margin-left: 8px;" title="Delete button"></i>
                    </span>
                </div>
            `).join('');
        }

        // 属性面板功能
        function togglePropertySearch() {
            const searchDiv = document.getElementById('property-search');
            const isVisible = searchDiv.style.display !== 'none';
            searchDiv.style.display = isVisible ? 'none' : 'block';
            if (!isVisible) {
                searchDiv.querySelector('input').focus();
            }
        }

        function filterProperties(searchTerm) {
            const groups = document.querySelectorAll('.properties-group');
            groups.forEach(group => {
                const text = group.textContent.toLowerCase();
                const shouldShow = text.includes(searchTerm.toLowerCase()) || searchTerm === '';
                group.style.display = shouldShow ? '' : 'none';
            });
        }

        function resetProperties() {
            if (confirm('确定要重置所有属性到默认值吗？')) {
                // 重置到默认配置
                const baseName = currentScenario ? currentScenario.name.toLowerCase().replace(/\s+/g, '-') : 'default';
                currentPageConfig = {
                    id: `${baseName}-list`,
                    name: `${currentScenario ? currentScenario.name : 'Default'} List`,
                    title: `${currentScenario ? currentScenario.name : 'Default'} List`,
                    path: `/${baseName}/list`,
                    layout: 'default',
                    data: {
                        searchQuery: {},
                        isLoading: false,
                        accounts: [],
                        totalUsers: 0
                    },
                    apis: [
                        { id: 'user-list', method: 'POST', url: '/api/admin-api/v1/account/page', params: '{{getQueryParams(searchQuery)}}' },
                        { id: 'user-delete', method: 'DELETE', url: '/api/admin-api/v1/account/:id', params: '' },
                    ],
                    toolbar: {
                        buttons: [
                            { text: 'CREATE', action: 'navigateToCreate', style: 'primary' }
                        ]
                    },
                    filterFields: [],
                    tableColumns: []
                };
                
                // 如果有场景数据，重新初始化字段
                if (currentScenario && currentScenario.fields) {
                    currentPageConfig.filterFields = currentScenario.fields.filter(f => f.queryable);
                    currentPageConfig.tableColumns = [...currentScenario.fields];
                }
                
                updatePropertiesPanel();
                updateFilterProperties();
                updateTableProperties();
                updateApiProperties();
                updateDataProperties();
                updateToolbarProperties();
            }
        }

        // 添加智能建议功能
        function addSmartSuggestions() {
            // 为API列表项添加智能标记
            const apiItems = document.querySelectorAll('#properties-api-list .column-item');
            apiItems.forEach(item => {
                if (item.textContent.includes('POST')) {
                    const badge = document.createElement('span');
                    badge.className = 'smart-badge';
                    badge.textContent = 'CRUD';
                    badge.title = 'Detected CRUD operation';
                    item.querySelector('span').appendChild(badge);
                }
            });

            // 为字段列表项添加类型提示
            const fieldItems = document.querySelectorAll('#properties-filter-fields .column-item, #properties-visible-columns .column-item');
            fieldItems.forEach(item => {
                const fieldText = item.textContent;
                if (fieldText.includes('email') || fieldText.includes('Email')) {
                    const badge = document.createElement('span');
                    badge.className = 'smart-badge';
                    badge.textContent = 'REQ';
                    badge.title = 'Usually required field';
                    item.querySelector('span').appendChild(badge);
                }
            });
        }

        // 在属性更新后调用智能建议
        const originalUpdateApiProperties = updateApiProperties;
        updateApiProperties = function() {
            originalUpdateApiProperties();
            setTimeout(addSmartSuggestions, 100);
        };

        const originalUpdateFilterProperties = updateFilterProperties;
        updateFilterProperties = function() {
            originalUpdateFilterProperties();
            setTimeout(addSmartSuggestions, 100);
        };

        const originalUpdateTableProperties = updateTableProperties;
        updateTableProperties = function() {
            originalUpdateTableProperties();
            setTimeout(addSmartSuggestions, 100);
        };

        // 层级切换功能
        function switchPropertyLevel(level) {
            // 更新标签状态
            document.querySelectorAll('.level-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-level="${level}"]`).classList.add('active');

            // 切换内容显示
            document.getElementById('page-level-properties').style.display = level === 'page' ? 'block' : 'none';
            document.getElementById('component-level-properties').style.display = level === 'component' ? 'block' : 'none';

            // 如果切换到组件级，检查当前选中的组件
            if (level === 'component') {
                updateSelectedComponentInfo();
            }
        }

        // 更新选中组件信息
        function updateSelectedComponentInfo() {
            const componentInfo = document.querySelector('.selected-component-info');
            if (componentInfo) {
                // 根据当前页面类型显示不同的组件信息
                const pageSelector = document.querySelector('#editor .form-input');
                const currentPageType = pageSelector ? pageSelector.value : '';
                
                let componentName = 'Generic Component';
                let componentDescription = 'Configure component properties';
                let componentIcon = 'fas fa-puzzle-piece';

                switch(currentPageType) {
                    case 'list':
                        componentName = 'Table Component';
                        componentDescription = 'Configure table-specific properties and behavior';
                        componentIcon = 'fas fa-table';
                        break;
                    case 'form':
                        componentName = 'Form Component';
                        componentDescription = 'Configure form fields and validation rules';
                        componentIcon = 'fas fa-edit';
                        break;
                    case 'detail':
                        componentName = 'Detail Component';
                        componentDescription = 'Configure detail view layout and fields';
                        componentIcon = 'fas fa-eye';
                        break;
                }

                componentInfo.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                        <i class="${componentIcon}" style="color: #44D62C;"></i>
                        <strong style="color: #fff;">${componentName}</strong>
                    </div>
                    <div style="font-size: 12px; color: #ccc;">${componentDescription}</div>
                `;
            }
        }

        // 更新属性面板可见性函数
        function updatePropertiesVisibility(pageType) {
            // 页面级属性始终显示
            // 组件级属性根据页面类型调整
            const isListPage = pageType === 'list';
            const isFormPage = pageType === 'form';
            
            // 在组件级视图中控制可见性
            if (document.getElementById('component-level-properties').style.display !== 'none') {
                const componentGroups = document.querySelectorAll('#component-level-properties .properties-group');
                componentGroups.forEach(group => {
                    const groupId = group.id;
                    let shouldShow = true;
                    
                    if (groupId === 'properties-filters-group' || 
                        groupId === 'properties-table-group' || 
                        groupId === 'properties-pagination-group') {
                        shouldShow = isListPage;
                    }
                    
                    group.style.display = shouldShow ? '' : 'none';
                });
                
                // 更新组件信息
                updateSelectedComponentInfo();
            }
        }

        // 智能页面生成
        function generateSmartPage(pageType) {
            if (!currentScenario) {
                alert('请先选择一个业务场景！');
                return;
            }

            // 设置页面选择器
            const pageSelector = document.querySelector('#editor .form-input');
            if (pageSelector) {
                pageSelector.value = pageType;
                handlePageChange(pageType);
            }

            // 显示生成成功消息
            const saveStatus = document.getElementById('saveStatus');
            if (saveStatus) {
                saveStatus.innerHTML = '<i class="fas fa-magic"></i> Generated';
                saveStatus.style.color = '#44D62C';
                setTimeout(() => {
                    saveStatus.innerHTML = '<i class="fas fa-edit"></i> Modified';
                    saveStatus.style.color = '#ff9800';
                }, 2000);
            }
        }

        // 组件选择功能
        let selectedComponent = null;

        function selectComponent(wrapper) {
            // 清除之前的选择
            document.querySelectorAll('.component-wrapper').forEach(comp => {
                comp.classList.remove('selected');
            });
            
            // 选择当前组件
            wrapper.classList.add('selected');
            selectedComponent = wrapper;
            
            // 切换到组件级属性面板
            switchPropertyLevel('component');
            
            // 更新组件信息显示选中的具体组件
            updateSelectedComponentDetails(wrapper);
        }

        function updateSelectedComponentDetails(wrapper) {
            const componentInfo = document.querySelector('.selected-component-info');
            if (componentInfo && wrapper) {
                const componentType = wrapper.getAttribute('data-component');
                const template = componentTemplates[componentType];
                if (template) {
                    componentInfo.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                            <i class="${template.icon}" style="color: #44D62C;"></i>
                            <strong style="color: #fff;">${template.name}</strong>
                            <span class="smart-badge">Selected</span>
                        </div>
                        <div style="font-size: 12px; color: #ccc;">Configure properties for this specific component</div>
                    `;
                }
            }
        }

        function editComponent(wrapper) {
            selectComponent(wrapper);
            // 这里可以打开组件编辑模态框
            const componentType = wrapper.getAttribute('data-component');
            const template = componentTemplates[componentType];
            alert(`正在编辑 ${template.name} 组件\n\n请在右侧属性面板中配置组件的具体属性。`);
        }

        function deleteComponent(wrapper) {
            if (confirm('确定要删除这个组件吗？')) {
                wrapper.remove();
                selectedComponent = null;
                
                // 检查是否还有组件
                const canvasContent = document.querySelector('.canvas-content');
                if (canvasContent && canvasContent.children.length === 0) {
                    // 恢复空状态
                    const canvas = document.querySelector('.canvas-area');
                    canvas.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-plus-circle" style="font-size: 3rem; color: #666; margin-bottom: 15px;"></i>
                            <p>Drag components here to start designing</p>
                            <p style="font-size: 14px; color: #999; margin-top: 10px;">Or auto-generate page templates from metadata</p>
                        </div>
                    `;
                }
                
                // 切换回页面级属性
                switchPropertyLevel('page');
                
                // 更新保存状态
                const saveStatus = document.getElementById('saveStatus');
                if (saveStatus) {
                    saveStatus.innerHTML = '<i class="fas fa-edit"></i> Modified';
                    saveStatus.style.color = '#ff9800';
                }
            }
        }

        // 改进拖拽结束处理
        document.addEventListener('dragend', (e) => {
            if (e.target.classList.contains('component-item')) {
                e.target.style.opacity = '1';
            }
        });

        // 改进页面处理函数调用
        const originalHandlePageChange = handlePageChange;
        handlePageChange = function(pageType) {
            originalHandlePageChange(pageType);
            
            // 清除之前选中的组件
            selectedComponent = null;
            document.querySelectorAll('.component-wrapper').forEach(comp => {
                comp.classList.remove('selected');
            });
            
            // 如果当前在组件级视图，切换回页面级
            const currentLevel = document.querySelector('.level-tab.active')?.getAttribute('data-level');
            if (currentLevel === 'component') {
                switchPropertyLevel('page');
            }
        };

        // 添加键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            // Delete键删除选中的组件
            if (e.key === 'Delete' && selectedComponent) {
                deleteComponent(selectedComponent);
            }
            
            // Escape键取消选择
            if (e.key === 'Escape' && selectedComponent) {
                selectedComponent.classList.remove('selected');
                selectedComponent = null;
                switchPropertyLevel('page');
            }
        });
    </script>
</body>
</html> 