import { config } from '@vue/test-utils'
import { createVuetify } from 'vuetify'

// 模拟 localStorage
const localStorageMock = (() => {
  let store = {}
  return {
    getItem: (key) => store[key] || null,
    setItem: (key, value) => {
      store[key] = value.toString()
    },
    clear: () => {
      store = {}
    },
    removeItem: (key) => {
      delete store[key]
    }
  }
})()

// 在测试环境中设置全局 localStorage
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// 创建更完善的 Cookie 模拟
const mockCookies = {}

// 模拟 document.cookie 的行为
Object.defineProperty(document, 'cookie', {
  get: () => {
    return Object.entries(mockCookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ')
  },
  set: (cookieString) => {
    const [cookiePair] = cookieString.split(';')
    const [key, value] = cookiePair.split('=')
    mockCookies[key.trim()] = value
  },
  configurable: true
})

// 模拟 storage 存储
const mockStorage = {}

// 导出到全局以便测试文件使用
global.mockCookies = mockCookies
global.mockStorage = mockStorage

// 创建 cookie 模拟
const createMockCookie = (key) => ({
  value: mockCookies[key] || null,
  get value() {
    return mockCookies[key] || null
  },
  set value(val) {
    if (val === null) {
      delete mockCookies[key]
    } else {
      mockCookies[key] = val
    }
  }
})

// 创建 storage 模拟
const createMockStorage = (key) => ({
  value: mockStorage[key] || null,
  get value() {
    return mockStorage[key] || null
  },
  set value(val) {
    if (val === null) {
      delete mockStorage[key]
    } else {
      mockStorage[key] = val
    }
  }
})

// 修改现有的 useCookie 模拟
vi.mock('@/hooks/useCookie', () => ({
  useCookie: (key) => ({
    value: mockCookies[key] || null,
    get value() {
      return mockCookies[key] || null
    },
    set value(val) {
      if (val === null || val === undefined) {
        delete mockCookies[key]
      } else {
        mockCookies[key] = val
      }
    }
  }),
  clearAllCookie: () => {
    Object.keys(mockCookies).forEach(key => delete mockCookies[key])
  }
}))

vi.mock('@/hooks/useStorage', () => ({
  useStorage: (key) => createMockStorage(key)
}))


// 在每次测试前重置存储
beforeEach(() => {
  // 清除所有模拟的存储
  Object.keys(mockCookies).forEach(key => delete mockCookies[key])
  Object.keys(mockStorage).forEach(key => delete mockStorage[key])

  // 模拟 window.matchMedia
  window.matchMedia = vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // 兼容旧版
    removeListener: vi.fn(), // 兼容旧版
    addEventListener: vi.fn(), // 兼容新版
    removeEventListener: vi.fn(), // 兼容新版
    dispatchEvent: vi.fn(), // 兼容新版
  }));
})

// 修改 Vue 的模拟方式
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')
  return {
    ...actual,
    ref: (value) => {
      const r = actual.ref(value)
      // 确保返回一个响应式对象
      return {
        value: r.value,
        get value() {
          return r.value
        },
        set value(val) {
          r.value = val
        }
      }
    }
  }
})

// 创建全局 $api mock
const $api = vi.fn()

// 设置全局 $api
global.$api = $api

// 设置全局属性
config.global.mocks = {
  $api,  // 使用同一个 mock 实例
  $cookie: {
    get: vi.fn(),
    set: vi.fn(),
    remove: vi.fn()
  },
  // 其他全局属性...
}

// 设置全局组件
config.global.components = {
  'GlobalComponent': { /* component definition */ }
}

// 设置全局指令
config.global.directives = {
  'v-custom': { /* directive definition */ }
}

// 创建 Vuetify 实例
const vuetify = createVuetify()

// 设置全局 Vuetify
config.global.plugins = [vuetify]


