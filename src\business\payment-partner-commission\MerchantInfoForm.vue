<script setup>
import { ref, inject, watch, defineEmits, onMounted } from "vue";
const form = defineModel("form");
import { useMerchant } from "./useMerchant";
import { useCommissionList } from "./useCommissionList";
import { requiredValidator } from "@/utils/validators";
import { useCommissionDetail } from "./useCommissionDetail";
import { mapPaymentChannelItemForDuplicate } from "./usePaymentChannel";
const { merchantList } = useMerchant();
const formRef = ref(null);
const { commissionList, updateMerchantId } = useCommissionList(form.value.merchantId);
const { commissionDetail, updateCommissionId } = useCommissionDetail(form.value.commissionSchemeCode);

const formMethods = inject("formMethods", {});
// 注入更新函数
const updatePaymentChannelItems = inject('updatePaymentChannelItems');
const updateAdditionalCurrencyItems = inject('updateAdditionalCurrencyItems');

// Define emits
const emit = defineEmits(['commission-duplicated']);

onMounted(() => {
  formMethods.validate = () => formRef.value.validate();
});

watch(
  () => form.value.merchantId,
  (newVal) => {
    if (newVal) {
      form.value.commissionSchemeCode = null;
      updateMerchantId(newVal);
    }
  }
)

const handleCommissionSchemeCodeChange = async (newVal) => {
  try {
    if (!newVal) return;

    await updateCommissionId(newVal);

    if (!commissionDetail.value) {
      console.error('Commission detail is not available');
      $message.error('获取Commission详情失败');
      return;
    }

    const paymentChannelList = commissionDetail.value.paymentChannelList || [];
    const [
      { currencySettingList: razerGoldPaymentChannelItems = [], additionalCurrencySettingList: razerGoldAdditionalCurrencyItems = [] } = {},
      { currencySettingList: razerGoldPinPaymentChannelItems = [], additionalCurrencySettingList: razerGoldPinAdditionalCurrencyItems = [] } = {},
      { currencySettingList: thirdPartyPaymentChannelItems = [], additionalCurrencySettingList: thirdPartyAdditionalCurrencyItems = [] } = {}
    ] = paymentChannelList;

    // 处理 RAZER GOLD 支付渠道数据
    if (razerGoldPaymentChannelItems.length > 0) {
      const items = razerGoldPaymentChannelItems.map((item, index) => {
        const mappedItem = mapPaymentChannelItemForDuplicate(item, index);
        // 设置默认值
        mappedItem.convertCommissionRate = item.commissionRatePercentage;
        mappedItem.convertEffectiveOn = item.effectiveStartDate;
        mappedItem.minAmount = item.commissionRateMinimumAmount;
        mappedItem.fixAmount = item.commissionRateFixedAmount;
        return mappedItem;
      });
      updatePaymentChannelItems(items, 0);
    }

    // 处理 RAZER GOLD PIN 支付渠道数据
    if (razerGoldPinPaymentChannelItems.length > 0) {
      const items = razerGoldPinPaymentChannelItems.map((item, index) => {
        const mappedItem = mapPaymentChannelItemForDuplicate(item, index);
        mappedItem.convertCommissionRate = item.commissionRatePercentage;
        mappedItem.convertEffectiveOn = item.effectiveStartDate;
        mappedItem.minAmount = item.commissionRateMinimumAmount;
        mappedItem.fixAmount = item.commissionRateFixedAmount;
        return mappedItem;
      });
      updatePaymentChannelItems(items, 1);
    }

    // 处理 THIRD PARTY 支付渠道数据
    if (thirdPartyPaymentChannelItems.length > 0) {
      const items = thirdPartyPaymentChannelItems.map((item, index) => {
        const mappedItem = mapPaymentChannelItemForDuplicate(item, index);
        mappedItem.convertCommissionRate = item.commissionRatePercentage;
        mappedItem.convertEffectiveOn = item.effectiveStartDate;
        mappedItem.minAmount = item.commissionRateMinimumAmount;
        mappedItem.fixAmount = item.commissionRateFixedAmount;
        return mappedItem;
      });
      updatePaymentChannelItems(items, 2);
    }

    // 复制额外货币设置数据
    if (razerGoldAdditionalCurrencyItems.length > 0) {
      const items = razerGoldAdditionalCurrencyItems.map(item => ({
        ...item,
        convertCommissionRate: item.commissionRatePercentage,
        convertEffectiveOn: item.effectiveStartDate,
        minAmount: item.commissionRateMinimumAmount,
        fixAmount: item.commissionRateFixedAmount,
        isEditing: false,
        isPaymentChannelEditing: false,
        isMerchantCurrencyEditing: false,
        isMinAmountEditing: false,
        isFixAmountEditing: false,
        isCommissionRateEditing: false,
        isEffectiveOnEditing: false,
        status: "Draft"
      }));
      updateAdditionalCurrencyItems(items, 0);
    }

    if (razerGoldPinAdditionalCurrencyItems.length > 0) {
      const items = razerGoldPinAdditionalCurrencyItems.map(item => ({
        ...item,
        convertCommissionRate: item.commissionRatePercentage,
        convertEffectiveOn: item.effectiveStartDate,
        minAmount: item.commissionRateMinimumAmount,
        fixAmount: item.commissionRateFixedAmount,
        isEditing: false,
        isPaymentChannelEditing: false,
        isMerchantCurrencyEditing: false,
        isMinAmountEditing: false,
        isFixAmountEditing: false,
        isCommissionRateEditing: false,
        isEffectiveOnEditing: false,
        status: "Draft"
      }));
      updateAdditionalCurrencyItems(items, 1);
    }

    if (thirdPartyAdditionalCurrencyItems.length > 0) {
      const items = thirdPartyAdditionalCurrencyItems.map(item => ({
        ...item,
        convertCommissionRate: item.commissionRatePercentage,
        convertEffectiveOn: item.effectiveStartDate,
        minAmount: item.commissionRateMinimumAmount,
        fixAmount: item.commissionRateFixedAmount,
        isEditing: false,
        isPaymentChannelEditing: false,
        isMerchantCurrencyEditing: false,
        isMinAmountEditing: false,
        isFixAmountEditing: false,
        isCommissionRateEditing: false,
        isEffectiveOnEditing: false,
        status: "Draft"
      }));
      updateAdditionalCurrencyItems(items, 2);
    }

    emit('commission-duplicated');

  } catch (error) {
    console.error('Error in handleCommissionSchemeCodeChange:', error);
    $message.error('复制失败，请重试');
  }
}

defineExpose({
  validate: () => formRef.value.validate(),
})


</script>
<template>
  <VForm ref="formRef">
    <VRow>
      <VCol md="4" sm="6" cols="12">
        <AppAutocomplete v-model="form.merchantId" label="Merchant Name" placeholder="Search Merchant"
          :rules="[requiredValidator]" required :items="merchantList" />
      </VCol>
      <VCol md="4" sm="6" cols="12">
        <AppTextField v-model="form.commissionSchemeName" label="Commission Scheme Name"
          placeholder="Enter Commission Scheme Name" :rules="[requiredValidator]" required />
      </VCol>
      <VCol md="4" sm="6" cols="12">
        <AppAutocomplete v-model="form.commissionSchemeCode" :disabled="!form.merchantId"
          label="Duplicate Payment Info from Existing Commission Scheme (Optional)"
          placeholder="Select Existing Commission Scheme" :items="commissionList"
          @update:modelValue="handleCommissionSchemeCodeChange" />
      </VCol>
    </VRow>
  </VForm>
</template>

<style scoped lang="scss"></style>
