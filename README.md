# Razer Gold Admin Portal V2

A modern admin portal built with Vue 3, Vite, and Vuetify.

## 📚 Developer Guide

For new developers, please check our detailed module development guide:
- [Module Development Guide](./MODULE_GUIDE.md) - A comprehensive guide using the user/list module as an example
- [模块开发指南](./MODULE_GUIDE_CN.md) - 中文版模块开发指南

The guide covers:
- Module structure and development steps
- Component development best practices
- State management patterns
- API integration
- Testing and debugging tips
- Deployment checklist

## Project Structure

```bash
├── config/                 # Global configuration files
├── deploy/                 # Deployment related files
│   ├── mesh/              # Service Mesh configuration
│   └── sample/            # Deployment examples
├── dist/                   # Build output directory
├── public/                 # Static assets
├── server/                 # Express server for production
├── src/                    # Source code
│   ├── assets/            # Static assets (images, styles, etc.)
│   ├── business/          # Business logic components
│   ├── components/        # Reusable Vue components
│   ├── config/            # App configuration
│   │   └── navigation/    # Navigation configuration
│   ├── hooks/             # Vue composition hooks
│   ├── layouts/           # Layout components
│   ├── pages/             # Page components
│   ├── plugins/           # Vue plugins
│   ├── stores/            # Pinia stores
│   └── utils/             # Utility functions
├── test/                   # Test files
│   ├── integration/       # Integration tests
│   └── unit/             # Unit tests
└── vite.config.js         # Vite configuration
```

## Features

- 🚀 Vue 3 + Vite - Fast development and build
- 📦 Components Auto Import
- 🎨 Vuetify 3 - Material Design Framework
- 🔍 File based routing
- 🏬 State Management via Pinia
- 🎯 Unit and Integration Testing Support
- 🌐 Production-ready Express server
- 🔐 Authentication and Authorization
- 📱 Responsive Design
- 🌙 Dark Mode Support
- 🔧 Easy Configuration
- 🐳 Docker and Service Mesh Support

## Getting Started

### Prerequisites

- Node.js 16+
- pnpm 7+

### Installation

1. Clone the repository
```bash
git clone [repository-url]
```

2. Install dependencies
```bash
pnpm install
```

3. Create environment file
```bash
cp .env.dev .env.dev.local
```

4. Start development server
```bash
pnpm dev
```

### Build for Production

```bash
pnpm build
```

### Run Production Server

```bash
pnpm start
```

## Docker Support

Build image:
```bash
docker build -t razer-gold-admin-portal-v2 .
```

Run container:
```bash
docker run -p 80:80 -p 443:443 razer-gold-admin-portal-v2
```

## Configuration

### Vite Configuration

The project uses Vite as the build tool. Main configurations:

- Vue 3 Support
- Auto Import Components
- File-based Routing
- Layout System
- Path Aliases
- Auto Icon Bundling

### Environment Variables

The project uses different environment files for configuration:

- `.env.dev` - Development environment defaults (version controlled)
- `.env.qa` - QA environment defaults (version controlled)
- `.env.prod` - Production environment defaults (version controlled)
- `.env.sandbox` - Sandbox environment defaults (version controlled)
- `.env.local` - Local development overrides (git ignored)

Environment files are loaded and merged in the following order (highest priority first):
1. `.env.local`
2. `.env.[mode]` (mode can be dev/qa/prod/sandbox)

Variables in files with higher priority will override those in lower priority files.

## Development

### File-based Routing

Routes are automatically generated based on the file structure in `src/pages/`, supporting:
- Dynamic routes (e.g., `[id].vue`)
- Nested routes
- 404 and unauthorized page handling
- Route meta information

### Navigation Configuration

Navigation menu configuration is located in `src/config/navigation/` directory:
- `index.js` - Main navigation configuration
- `user.js` - User module navigation
- `audit.js` - Audit module navigation

### Component Auto-importing

Components in `src/components/` are automatically registered, supporting:
- Lazy loading
- Tree-shaking
- Global component registration
- Component naming conventions

### State Management

Uses Pinia for state management. Store files are located in `src/stores/`, supporting:
- Modular state management
- State persistence
- Reactive state
- Actions and Getters
- Plugin extensions

### Layouts

Layout components in `src/layouts/` provide different page layouts, including:
- Vertical navigation layout
- Horizontal navigation layout
- Mixed navigation layout
- Custom layout support

## Testing

Run unit tests:
```bash
pnpm test:unit
```

Run integration tests:
```bash
pnpm test:integration
```

## Deployment

1. Build the application
```bash
pnpm build
```

2. Start the production server
```bash
pnpm start
```

### Service Mesh Deployment

The project supports deployment using AWS ECS and Service Mesh, with configuration files located in the `deploy/mesh/` directory.

## License

This project is proprietary and confidential.

