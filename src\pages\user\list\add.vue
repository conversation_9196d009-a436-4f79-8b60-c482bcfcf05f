<script setup>
import { useRouter } from 'vue-router'
import UserForm from '@/business/user/list/form.vue'
import { dateUtil } from '@/utils/day'
definePage({
  meta: {
    navActiveLink: 'user-list',
    breadcrumb: 'Create New User',
  },
})

const router = useRouter()
const userFormRef = ref(null)
const isSaving = ref(false)

// form data
const formModel = ref({
  email: '',
  name: '',
  statusId: 1,
  roleList: [],
})

const handleCancel = () => {
  userFormRef.value?.resetForm()
  router.back()
}

const handleSave = async () => {
  if (isSaving.value) return
  // validate form
  const { valid, errors } = await userFormRef.value?.validate()

  if (!valid) return
  try {
    isSaving.value = true

    const params = userFormRef.value?.getParams()
    delete params.name 
    await $api('/api/admin-api/v1/account', {
      method: 'POST',
      body: params
    })
    message.success('User created successfully')
    router.back()
  } finally {
    isSaving.value = false
  }
}
</script>

<template>
  <VCard>
    <VCardItem class="pb-4 px-0">
      <VCardTitle>
        Create New User
      </VCardTitle>
    </VCardItem>
    
    <UserForm
      ref="userFormRef"
      v-model="formModel"
      type="add"
    />
    
    <VRow class="d-flex mt-3">
      <VCol cols="12" class="d-flex justify-end">
        <VBtn variant="outlined" color="default" @click="handleCancel">
          CANCEL
        </VBtn>
        <VBtn color="primary" class="ml-2" style="width: 120px;" data-testid="save-button" @click="handleSave">
          SAVE
        </VBtn>
      </VCol>
    </VRow>
  </VCard>
</template>
