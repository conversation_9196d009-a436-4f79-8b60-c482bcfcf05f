// Define the namespace for application local storage
const namespace = "gold-admin-console";

// Initialize empty object if namespace doesn't exist
if (!localStorage.getItem(namespace)) {
  localStorage.setItem(namespace, JSON.stringify({}));
}

/**
 * Create a reactive local storage
 * @param {string} key - Storage key name
 * @param {any} value - Initial value
 * @returns {Ref} Returns a reactive ref object
 */
export const useStorage = (key, value) => {
  // Create reactive reference
  const storage = ref(value);

  // Function to get storage data
  const getStorageData = () => {
    return JSON.parse(localStorage.getItem(namespace));
  };

  // Function to save data
  const saveStorageData = (newData) => {
    localStorage.setItem(namespace, JSON.stringify(newData));
  };

  // Function to remove storage item
  const removeStorage = () => {
    const data = getStorageData();
    delete data[key];
    saveStorageData(data);
  };

  // Add remove method to storage ref
  storage.remove = removeStorage;

  // Initialize value
  const data = getStorageData();  
  if (data[key] !== undefined) {
    storage.value = data[key];
  } else {
    storage.value = value;
  }

  // Watch storage changes and auto sync to localStorage
  watch(storage, (newValue) => {
    // Get latest data
    const data = getStorageData();
    // Update value for specified key
    data[key] = newValue;
    // Save back to localStorage
    saveStorageData(data);
  }, { deep: true, immediate: true }); // Add deep: true to ensure object type changes can be detected

  // Listen for localStorage modifications from other places
  window.addEventListener('storage', (e) => {
    if (e.key === namespace) {
      const newData = JSON.parse(e.newValue || '{}');
      if (newData[key] !== storage.value) {
        storage.value = newData[key] ?? null;
      }
    }
  });

  return storage;
};
