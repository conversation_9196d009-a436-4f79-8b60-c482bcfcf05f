# Build stage
FROM node:22.16.0-alpine AS builder
ARG BUILD_ENV

WORKDIR /usr/src/app

# Copy package files
COPY . ./

RUN npm install 
RUN npm run build:icons
RUN npm run build:$BUILD_ENV

# Runtime
FROM nginx:1.28.0-alpine

COPY --from=builder /usr/src/app/dist /usr/share/nginx/html
COPY --from=builder /usr/src/app/nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 443

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]