/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'unplugin-vue-router/types'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    'not-found': RouteRecordInfo<'not-found', '/:error(.*)', { error: ParamValue<true> }, { error: ParamValue<false> }>,
    'not-authorized': RouteRecordInfo<'not-authorized', '/not-authorized', Record<never, never>, Record<never, never>>,
    'user-group': RouteRecordInfo<'user-group', '/user/group', Record<never, never>, Record<never, never>>,
    'user-group-add': RouteRecordInfo<'user-group-add', '/user/group/add', Record<never, never>, Record<never, never>>,
    'user-group-detail': RouteRecordInfo<'user-group-detail', '/user/group/detail', Record<never, never>, Record<never, never>>,
    'user-group-edit': RouteRecordInfo<'user-group-edit', '/user/group/edit', Record<never, never>, Record<never, never>>,
    'user-list': RouteRecordInfo<'user-list', '/user/list', Record<never, never>, Record<never, never>>,
    'user-list-add': RouteRecordInfo<'user-list-add', '/user/list/add', Record<never, never>, Record<never, never>>,
    'user-list-detail': RouteRecordInfo<'user-list-detail', '/user/list/detail', Record<never, never>, Record<never, never>>,
    'user-list-edit': RouteRecordInfo<'user-list-edit', '/user/list/edit', Record<never, never>, Record<never, never>>,
    'user-role': RouteRecordInfo<'user-role', '/user/role', Record<never, never>, Record<never, never>>,
    'user-role-add': RouteRecordInfo<'user-role-add', '/user/role/add', Record<never, never>, Record<never, never>>,
    'user-role-edit': RouteRecordInfo<'user-role-edit', '/user/role/edit', Record<never, never>, Record<never, never>>,
  }
}
