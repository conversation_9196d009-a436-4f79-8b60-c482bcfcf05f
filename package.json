{"name": "razer-gold-console-admin", "version": "9.2.0", "private": true, "type": "module", "scripts": {"dev": "cross-env NODE_OPTIONS=\"--experimental-loader ./loader.mjs\" vite --mode dev", "dev:force": "cross-env NODE_OPTIONS=\"--experimental-loader ./loader.mjs\" vite --force --mode dev", "build:dev": "vite build --mode dev", "build:qa": "vite build --mode qa", "build:sandbox": "vite build --mode sandbox", "build:prod": "vite build --mode prod", "build": "vite build --mode prod", "start:dev": "npm run build:dev && esno server/index.js", "start:qa": "npm run build:qa && esno server/index.js", "start:prod": "npm run build:prod && esno server/index.js", "preview": "vite preview --host=127.0.0.1 --port 5050", "lint": "eslint . -c .eslintrc.cjs --fix --ext .ts,.js,.cjs,.vue,.tsx,.jsx", "test": "vitest run", "build:icons": "tsx src/plugins/iconify/build-icons.js", "test:unit": "cross-env TEST_TYPE=unit vitest run unit", "test:integration": "cross-env TEST_TYPE=integration vitest run integration", "test:watch": "vitest"}, "dependencies": {"@floating-ui/dom": "1.6.8", "@formkit/drag-and-drop": "0.1.6", "@highlightjs/vue-plugin": "^2.1.0", "@mdi/js": "^7.4.47", "@sindresorhus/is": "7.0.0", "@tiptap/extension-highlight": "^2.5.8", "@tiptap/extension-image": "^2.5.8", "@tiptap/extension-link": "^2.5.8", "@tiptap/extension-text-align": "^2.5.8", "@tiptap/pm": "^2.5.8", "@tiptap/starter-kit": "^2.5.8", "@tiptap/vue-3": "^2.5.8", "@vueuse/core": "^10.11.0", "@vueuse/math": "10.11.0", "apexcharts": "3.51.0", "big.js": "^6.2.2", "chart.js": "4.4.3", "compression": "^1.7.5", "cookie-es": "1.2.2", "dayjs": "^1.11.13", "destr": "^2.0.3", "dexie": "^4.0.11", "express": "^4.21.2", "flatpickr": "^4.6.13", "highlight.js": "^11.11.1", "interactjs": "^1.10.27", "jsonp": "^0.2.1", "jwt-decode": "4.0.0", "lodash-es": "^4.17.21", "mapbox-gl": "3.5.2", "ofetch": "1.3.4", "pinia": "^2.3.1", "roboto-fontface": "0.10.0", "shepherd.js": "13.0.1", "swiper": "11.1.9", "ufo": "1.5.4", "vue": "^3.4.35", "vue-chartjs": "5.3.1", "vue-flatpickr-component": "11.0.5", "vue-i18n": "^9.14.3", "vue-prism-component": "2.0.0", "vue-router": "4.4.0", "vue3-apexcharts": "1.5.3", "vue3-perfect-scrollbar": "2.0.0", "vuetify": "3.6.13", "webfontloader": "1.6.28"}, "devDependencies": {"@antfu/eslint-config-vue": "0.43.1", "@antfu/utils": "0.7.10", "@fullcalendar/core": "6.1.15", "@fullcalendar/daygrid": "6.1.15", "@fullcalendar/interaction": "6.1.15", "@fullcalendar/list": "6.1.15", "@fullcalendar/timegrid": "6.1.15", "@fullcalendar/vue3": "6.1.15", "@iconify-json/fa": "1.1.8", "@iconify-json/mdi": "1.1.67", "@iconify-json/tabler": "1.1.118", "@iconify/tools": "4.0.4", "@iconify/utils": "2.1.29", "@iconify/vue": "4.1.2", "@intlify/unplugin-vue-i18n": "4.0.0", "@pinia/testing": "^0.1.7", "@plugin-web-update-notification/vite": "^2.0.0", "@stylistic/stylelint-config": "1.0.1", "@stylistic/stylelint-plugin": "2.1.3", "@tiptap/extension-character-count": "^2.5.8", "@tiptap/extension-placeholder": "^2.5.8", "@tiptap/extension-subscript": "^2.5.8", "@tiptap/extension-superscript": "^2.5.8", "@tiptap/extension-underline": "^2.5.8", "@unocss/preset-rem-to-px": "66.1.0-beta.5", "@unocss/preset-wind3": "66.1.0-beta.5", "@vitejs/plugin-vue": "5.1.1", "@vitejs/plugin-vue-jsx": "4.0.0", "@vue/test-utils": "^2.4.6", "cross-env": "^7.0.3", "eslint": "8.57.0", "eslint-config-airbnb-base": "15.0.0", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-case-police": "0.6.1", "eslint-plugin-import": "2.29.1", "eslint-plugin-promise": "6.6.0", "eslint-plugin-regex": "1.10.0", "eslint-plugin-regexp": "2.6.0", "eslint-plugin-sonarjs": "0.24.0", "eslint-plugin-unicorn": "51.0.1", "eslint-plugin-vue": "9.27.0", "esno": "^0.17.0", "jsdom": "^26.0.0", "postcss-html": "1.7.0", "postcss-scss": "4.0.9", "puppeteer": "^24.2.0", "sass": "1.76.0", "shiki": "1.12.0", "stylelint": "16.8.0", "stylelint-config-idiomatic-order": "10.0.0", "stylelint-config-standard-scss": "13.1.0", "stylelint-use-logical-spec": "5.0.1", "tsx": "4.16.5", "unocss": "66.0.0", "unplugin-auto-import": "0.18.2", "unplugin-vue-components": "0.27.3", "unplugin-vue-define-options": "1.4.6", "unplugin-vue-router": "0.8.8", "vite": "5.3.5", "vite-plugin-vue-devtools": "7.3.7", "vite-plugin-vue-layouts": "0.11.0", "vite-plugin-vuetify": "2.0.3", "vite-svg-loader": "5.1.0", "vitest": "^3.0.2", "vue-shepherd": "3.0.0"}, "resolutions": {"postcss": "^8", "@tiptap/core": "^2", "@types/video.js": "^7", "sass": "1.76.0"}, "overrides": {"postcss": "^8", "@tiptap/core": "^2", "@types/video.js": "^7", "sass": "1.76.0"}}