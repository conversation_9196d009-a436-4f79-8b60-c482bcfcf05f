<script setup>
import { useRouter, useRoute } from 'vue-router'
import UserGroupForm from '@/business/user/group/form.vue'
import { useUserGroupDetail } from '@/business/user/group/useUserGroupDetail'
definePage({
  meta: {
    navActiveLink: 'user-group',
    breadcrumb: 'Edit User Group',
  },
})

const router = useRouter()
const userGroupFormRef = ref(null)
const isSaving = ref(false)
const route = useRoute()
const userGroupId = computed(() => route.query.id)
const { userGroupData: formModel } = useUserGroupDetail(route.query.id)



const handleCancel = () => {
  userGroupFormRef.value?.resetForm()
  router.back()
}

const handleSave = async () => {
  if (isSaving.value) return
  const params = userGroupFormRef.value?.getParams()

  try {
    isSaving.value = true
    await $api('api/admin-api/v1/user-group', {
    method: 'PUT',
    body: params
    })
    message.success('User Group updated successfully')
    router.back()
  } finally {
    isSaving.value = false
  }
}
</script>

<template>
  <VCard>
    <VCardItem class="pb-4 px-0">
      <VCardTitle>
        Edit User Group
      </VCardTitle>
    </VCardItem>

    <UserGroupForm ref="userGroupFormRef" v-model="formModel" type="edit" />

    <VRow class="d-flex mt-3">
      <VCol cols="5" class="d-flex justify-end">
        <VBtn variant="outlined" color="default" @click="handleCancel">
          CANCEL
        </VBtn>
        <VBtn color="primary" class="ml-2" style="width: 120px;" @click="handleSave">
          SAVE
        </VBtn>
      </VCol>
    </VRow>
  </VCard>
</template>
