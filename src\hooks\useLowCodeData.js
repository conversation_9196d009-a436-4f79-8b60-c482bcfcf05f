import { computed, inject } from 'vue'

/**
 * 低代码数据绑定 composable
 * 提供统一的低代码上下文数据访问接口
 * @param {Object} fallbackProps - 回退的 props 对象
 * @returns {Object} 包含响应式数据访问方法的对象
 */
export function useLowCodeData(fallbackProps = {}) {
  // 尝试获取低代码渲染上下文
  const renderContext = inject('lowCodeRenderContext', null)
  
  /**
   * 获取数据值，优先从低代码上下文获取，否则从 fallback 获取
   * @param {string} key - 数据键名
   * @param {any} fallbackValue - 回退值
   * @returns {any} 数据值
   */
  const getData = (key, fallbackValue) => {
    if (renderContext?.data && renderContext.data.hasOwnProperty(key)) {
      console.log(`[useLowCodeData] Using reactive data.${key}:`, renderContext.data[key])
      return renderContext.data[key]
    }
    console.log(`[useLowCodeData] Using fallback for ${key}:`, fallbackValue)
    return fallbackValue
  }

  /**
   * 创建响应式计算属性，自动处理低代码数据绑定
   * @param {string} key - 数据键名
   * @param {any} fallbackValue - 回退值
   * @returns {ComputedRef} 响应式计算属性
   */
  const useReactiveData = (key, fallbackValue) => {
    return computed(() => getData(key, fallbackValue))
  }

  /**
   * 批量创建响应式数据绑定
   * @param {Object} mapping - 键值映射对象，如 { page: props.page, totalItems: props.totalItems }
   * @returns {Object} 包含响应式计算属性的对象
   */
  const useReactiveDataBatch = (mapping) => {
    const result = {}
    for (const [key, fallbackValue] of Object.entries(mapping)) {
      result[key] = useReactiveData(key, fallbackValue)
    }
    return result
  }

  return {
    renderContext,
    getData,
    useReactiveData,
    useReactiveDataBatch,
    // 便捷方法：检查是否在低代码环境中
    isLowCodeContext: !!renderContext
  }
}

export default useLowCodeData 