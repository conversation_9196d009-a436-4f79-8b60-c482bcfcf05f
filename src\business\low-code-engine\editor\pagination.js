/**
 * 低代码平台分页组件配置
 */

// 可用的分页组件选项
export const PAGINATION_COMPONENTS = {
  TABLE_PAGINATION: 'TablePagination',           // 标准分页组件
  TABLE_PAGINATION_LOW_CODE: 'TablePaginationLowCode' // 低代码专用分页组件
}

// 默认分页配置
export const DEFAULT_PAGINATION_CONFIG = {
  // 使用的分页组件类型
  component: PAGINATION_COMPONENTS.TABLE_PAGINATION,
  
  // 分页相关的数据绑定
  dataBinding: {
    page: 'page',
    itemsPerPage: 'itemsPerPage', 
    totalItems: 'totalCount'
  },
  
  // 分页事件配置
  events: {
    'update:page': [{ action: 'handlePageChange' }]
  },
  
  // 分页组件的额外props
  extraProps: {}
}

/**
 * 获取分页组件配置
 * @param {string} componentType - 分页组件类型
 * @returns {Object} 分页配置
 */
export function getPaginationConfig(componentType = PAGINATION_COMPONENTS.TABLE_PAGINATION) {
  return {
    ...DEFAULT_PAGINATION_CONFIG,
    component: componentType
  }
}

/**
 * 创建分页组件JSON配置
 * @param {Object} config - 分页配置
 * @returns {Object} 分页组件的JSON配置
 */
export function createPaginationComponent(config = {}) {
  const finalConfig = {
    ...DEFAULT_PAGINATION_CONFIG,
    ...config
  }
  
  return {
    type: finalConfig.component,
    props: {
      page: `{{${finalConfig.dataBinding.page}}}`,
      itemsPerPage: `{{${finalConfig.dataBinding.itemsPerPage}}}`,
      totalItems: `{{${finalConfig.dataBinding.totalItems}}}`,
      ...finalConfig.extraProps
    },
    events: finalConfig.events
  }
} 