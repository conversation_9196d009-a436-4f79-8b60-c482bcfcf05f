<script setup>
import { layoutConfig } from '@layouts'
import { useLayoutConfigStore } from '@layouts/stores/config'
import { useRouter } from 'vue-router'
import {
  getComputedNavLinkToProp,
  getDynamicI18nProps,
  isNavLinkActive,
} from '@layouts/utils'
import { watch } from 'vue'

const router = useRouter()
const props = defineProps({
  item: {
    type: null,
    required: true,
  },
})

const configStore = useLayoutConfigStore()
const hideTitleAndBadge = configStore.isVerticalNavMini()

// 添加监听以检查活动状态
watch(() => isNavLinkActive(props.item, router), (newVal) => {
  console.log('Nav link active state changed:', {
    item: props.item.to,
    active: newVal
  })
})

// 添加即时的活动状态检查
const isActive = computed(() => {
  const active = isNavLinkActive(props.item, router)
  console.log('Nav link active computed:', {
    item: props.item.to,
    active: active
  })
  return active
})
</script>

<template>
  <li
    class="nav-link"
    :class="{ 
      disabled: item.disable,
      'nav-link-active': isActive 
    }"
  >
    <Component
      :is="item.to ? 'RouterLink' : 'a'"
      v-bind="getComputedNavLinkToProp(item)"
      :class="{ 'router-link-active': isActive }"
      @click="() => console.log('Nav item clicked:', item.to)"
    >
      <Component
        :is="layoutConfig.app.iconRenderer || 'div'"
        v-bind="item.icon || layoutConfig.verticalNav.defaultNavItemIconProps"
        class="nav-item-icon"
      />
      <TransitionGroup name="transition-slide-x">
        <!-- 👉 Title -->
        <Component
          :is="layoutConfig.app.i18n.enable ? 'i18n-t' : 'span'"
          v-show="!hideTitleAndBadge"
          key="title"
          class="nav-item-title"
          v-bind="getDynamicI18nProps(item.title, 'span')"
        >
          {{ item.title }}
        </Component>

        <!-- 👉 Badge -->
        <Component
          :is="layoutConfig.app.i18n.enable ? 'i18n-t' : 'span'"
          v-if="item.badgeContent"
          v-show="!hideTitleAndBadge"
          key="badge"
          class="nav-item-badge"
          :class="item.badgeClass"
          v-bind="getDynamicI18nProps(item.badgeContent, 'span')"
        >
          {{ item.badgeContent }}
        </Component>
      </TransitionGroup>
    </Component>
  </li>
</template>

<style lang="scss">
.layout-vertical-nav {
  .nav-link {
    &.nav-link-active {
      background-color: rgba(var(--v-theme-primary), 0.1);
    }
    
    a {
      display: flex;
      align-items: center;
      
      &.router-link-active {
        color: rgb(var(--v-theme-primary));
      }
    }
  }
}
</style>
