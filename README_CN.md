# Razer Gold 管理后台 V2

基于 Vue 3、Vite 和 Vuetify 构建的现代化管理后台。

## 📚 开发指南

对于新加入的开发者，请查看我们详细的模块开发指南：
- [模块开发指南](./MODULE_GUIDE_CN.md) - 以 user/list 模块为例的详细开发指南
- [Module Development Guide](./MODULE_GUIDE.md) - English version of the module development guide

指南包含以下内容：
- 模块结构和开发步骤
- 组件开发最佳实践
- 状态管理模式
- API 集成
- 测试和调试技巧
- 部署检查清单

## 项目结构

```bash
├── config/                 # 全局配置文件
├── deploy/                 # 部署相关文件
│   ├── mesh/              # Service Mesh 配置
│   └── sample/            # 部署示例
├── dist/                   # 构建输出目录
├── public/                 # 静态资源
├── server/                 # 生产环境Express服务器
├── src/                    # 源代码
│   ├── assets/            # 静态资源（图片、样式等）
│   ├── business/          # 业务逻辑组件
│   ├── components/        # 可复用的Vue组件
│   ├── config/            # 应用配置
│   │   └── navigation/    # 导航配置
│   ├── hooks/             # Vue组合式钩子
│   ├── layouts/           # 布局组件
│   ├── pages/             # 页面组件
│   ├── plugins/           # Vue插件
│   ├── stores/            # Pinia状态管理
│   └── utils/             # 工具函数
├── test/                   # 测试文件
│   ├── integration/       # 集成测试
│   └── unit/             # 单元测试
└── vite.config.js         # Vite配置
```

## 特性

- 🚀 Vue 3 + Vite - 快速的开发和构建
- 📦 组件自动导入
- 🎨 Vuetify 3 - Material Design框架
- 🔍 基于文件的路由
- 🏬 使用Pinia进行状态管理
- 🎯 单元测试和集成测试支持
- 🌐 生产就绪的Express服务器
- 🔐 身份验证和授权
- 📱 响应式设计
- 🌙 暗色模式支持
- 🔧 简单配置
- 🐳 Docker和Service Mesh支持

## 快速开始

### 前置要求

- Node.js 16+
- pnpm 7+

### 安装

1. 克隆仓库
```bash
git clone [repository-url]
```

2. 安装依赖
```bash
pnpm install
```

3. 创建环境文件
```bash
cp .env.dev .env.dev.local
```

4. 启动开发服务器
```bash
pnpm dev
```

### 生产环境构建

```bash
pnpm build
```

### 运行生产服务器

```bash
pnpm start
```

## Docker支持

构建镜像：
```bash
docker build -t razer-gold-admin-portal-v2 .
```

运行容器：
```bash
docker run -p 80:80 -p 443:443 razer-gold-admin-portal-v2
```

## 配置

### Vite配置

项目使用Vite作为构建工具。主要配置包括：

- Vue 3支持
- 组件自动导入
- 基于文件的路由
- 布局系统
- 路径别名
- 自动图标打包

### 环境变量

项目使用不同的环境文件进行配置：

- `.env.dev` - 开发环境默认配置（版本控制）
- `.env.qa` - QA环境默认配置（版本控制）
- `.env.prod` - 生产环境默认配置（版本控制）
- `.env.sandbox` - 沙箱环境配置（版本控制）
- `.env.local` - 本地开发覆盖配置（git忽略）

环境文件按以下顺序加载和合并（优先级从高到低）：
1. `.env.local`
2. `.env.[mode]`（mode可以是dev/qa/prod/sandbox）

优先级高的文件中的变量会覆盖优先级低的文件中的同名变量。

## 开发

### 基于文件的路由

路由根据`src/pages/`中的文件结构自动生成，支持：
- 动态路由（如 `[id].vue`）
- 嵌套路由
- 404和未授权页面处理
- 路由元信息配置

### 导航配置

导航菜单配置位于 `src/config/navigation/` 目录：
- `index.js` - 主导航配置
- `user.js` - 用户模块导航
- `audit.js` - 审计模块导航

### 组件自动导入

`src/components/`中的组件会被自动注册，支持：
- 按需加载
- Tree-shaking
- 全局组件注册
- 组件命名约定

### 状态管理

使用Pinia进行状态管理。Store文件位于`src/stores/`，支持：
- 模块化状态管理
- 状态持久化
- 响应式状态
- Action和Getter
- 插件扩展

### 布局

`src/layouts/`中的布局组件提供不同的页面布局，包括：
- 垂直导航布局
- 水平导航布局
- 混合导航布局
- 自定义布局支持

## 测试

运行单元测试：
```bash
pnpm test:unit
```

运行集成测试：
```bash
pnpm test:integration
```

## 部署

1. 构建应用
```bash
pnpm build
```

2. 启动生产服务器
```bash
pnpm start
```

### Service Mesh部署

项目支持使用AWS ECS和Service Mesh进行部署，配置文件位于`deploy/mesh/`目录。

## 许可证

本项目为专有和机密软件。 