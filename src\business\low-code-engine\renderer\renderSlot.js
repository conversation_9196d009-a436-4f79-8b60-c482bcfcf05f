// src/business/low-code-engine/renderer/renderSlot.js

import { h } from 'vue';
import { resolveExpression } from './utils'; // 从 utils 导入 resolveExpression

/**
 * 渲染插槽内容
 * @param {Object | Array} slotContentMeta - 插槽内容的元数据
 * @param {Object} context - 上下文（data, methods, slots, renderComponentFn等）
 * @returns {import('vue').VNode | Array<import('vue').VNode> | String | null} 渲染后的VNode或VNode数组
 */
function renderSlot(slotContentMeta, context) {
  if (!slotContentMeta) {
    return null;
  }

  // 如果是数组，递归渲染每个子元素
  if (Array.isArray(slotContentMeta)) {
    return slotContentMeta.map(item => renderSlot(item, context));
  }

  // 如果是普通文本
  if (slotContentMeta.type === 'text') {
    return resolveExpression(slotContentMeta.props.text, context);
  }

  // 如果是组件，直接调用renderComponentFn
  if (slotContentMeta.type && context.renderComponentFn) {
    const result = context.renderComponentFn(slotContentMeta, context);
    return result;
  }

  // 默认返回null或警告
  console.warn('无法识别的插槽内容元数据:', slotContentMeta);
  return null;
}

export default renderSlot;