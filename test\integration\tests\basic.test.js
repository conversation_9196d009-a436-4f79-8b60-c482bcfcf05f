import {
  clickElement,
  delay,
  fillForm,
  getElementText,
  waitForSelector,
} from "../helpers/browser.js";
import { describe, expect, test } from "vitest";

describe("Basic Page Tests", () => {
  const BASE_URL =
    import.meta.env.VITE_BASE_URL || "https://console-v2.zgold-dev.razer.com/"; // Replace with your application URL
  beforeEach(async () => {
    await page.goto(BASE_URL);
    // Wait for redirect completion
    await page.waitForNavigation({
      waitUntil: ["networkidle0", "load", "domcontentloaded"],
    });

    // Check if login is needed
    const needLogin = await page.evaluate(() => {
      return window.location.href.includes("login.microsoftonline.com");
    });

    if (needLogin) {
      // Perform login steps only if needed
      await waitForSelector('input[type="email"]');
      await page.type('input[type="email"]', import.meta.env.VITE_USER_EMAIL);
      await clickElement('input[type="submit"]');

      await waitForSelector('input[type="password"]');
      await page.type(
        'input[type="password"]',
        import.meta.env.VITE_USER_PASSWORD
      );
      await page.evaluate(() => {
        const button = document.querySelector('input[type="submit"]');
        if (button) button.click();
      });

      // Wait for login completion
      await delay(10000);
    }
  });

  test("View Commission List", async () => {
    // Go to the commission page
    await page.goto(
      BASE_URL + "payment-partner-commission/commission-scheme-managers"
    );
    // Wait for the page to load
    await delay(3000);

    // Get the commission list
    const commissionList = await page.$$("table tbody tr");

    // Check if the list is not empty
    expect(commissionList.length).toBeGreaterThan(0);

    // Search with existing commission scheme name
    await page.type(
      'input[placeholder="Search Commission Scheme Name"]',
      "etest"
    );

    // Wait for the search results to load
    await delay(3000);

    // Get the search results
    let searchResult = await page.$$("table tbody tr");

    // Check search results: either has data or shows no data message
    let noDataRow = await page.$(".v-data-table-rows-no-data");
    if (noDataRow === null) {
      // When results exist, verify they're not empty
      expect(searchResult.length).toBeGreaterThan(0);
    } else {
      // When no results, verify no-data message is shown
      expect(noDataRow).not.toBeNull();
    }

    // Search with non-existing commission scheme name
    await page.type(
      'input[placeholder="Search Commission Scheme Name"]',
      "1234567890"
    );

    // Wait for the search results to load
    await delay(3000);

    // Check if "No data available" message is shown
    noDataRow = await page.$(".v-data-table-rows-no-data");
    expect(noDataRow).not.toBeNull();

    // Clear search input and trigger update
    await page.$eval(
      'input[placeholder="Search Commission Scheme Name"]',
      (el) => {
        el.value = "";
        // Dispatch input event to ensure Vue v-model catches the change
        el.dispatchEvent(new Event("input", { bubbles: true }));
      }
    );
    await delay(1000); // Wait for data update
    await page.keyboard.press("Enter");
    await delay(3000);

    // Search with existing merchant name
    await page.type(
      'input[placeholder="Select Merchant Name"]',
      "MOL ACCESSPORTAL SDN BHD"
    );

    // Wait for the search results to load
    await delay(3000);

    // Select the first item
    await page.click(".v-list-item-title:first-child");
    await delay(3000);

    // Check search results
    searchResult = await page.$$("table tbody tr");
    noDataRow = await page.$(".v-data-table-rows-no-data");
    if (noDataRow === null) {
      // When results exist, verify they're not empty
      expect(searchResult.length).toBeGreaterThan(0);
    } else {
      // When no results, verify no-data message is shown
      expect(noDataRow).not.toBeNull();
    }
  });

  // 测试Bulk Enable Disable User
  test("Bulk Enable Disable User Alternative", async () => {
    await page.goto(BASE_URL + "user/list");
    await delay(3000);

    // 在选择复选框之前，确保页面完全加载
    await page.waitForSelector(
      ".v-data-table .v-data-table__tbody tr .v-checkbox-btn input[type='checkbox']",
      { visible: true, timeout: 5000 }
    );

    // 修改选中复选框的代码
    await page.waitForSelector(".v-data-table__thead .v-checkbox-btn");
    await page.click(".v-data-table__thead .v-checkbox-btn");

    // 等待选中状态更新
    await delay(2000);

    // 验证选中状态
    const verifyChecked = await page.evaluate(() => {
      const checkboxes = document.querySelectorAll(
        ".v-data-table .v-data-table__tbody tr .v-checkbox-btn input[type='checkbox']"
      );
      const selectedCountElement = document.querySelector(
        ".text-primary.font-weight-medium"
      );

      return {
        total: checkboxes.length,
        checked: Array.from(checkboxes).filter((cb) => cb.checked).length,
        displayedCount: selectedCountElement
          ? parseInt(selectedCountElement.textContent, 10)
          : 0,
      };
    });

    console.log("验证结果:", verifyChecked);
    expect(verifyChecked.checked).toBe(verifyChecked.displayedCount);

    // 先尝试等待 enable 按钮
    await page.waitForSelector('.v-btn[data-test-id="enable"]', {
      visible: true,
      timeout: 2000,
    });
    await page.click('.v-btn[data-test-id="enable"]');

    // 等待操作完成
    await page.waitForSelector('.v-dialog .v-btn.bg-primary', { visible: true, timeout: 3000 });

    await page.evaluate(() => {
      document.querySelector('.v-dialog .v-btn.bg-primary').click();
    });

    // 确认弹出框
    // await page.click('.v-dialog .v-btn.bg-primary');



    // 等待操作完成
    await delay(2000);
  });
});
