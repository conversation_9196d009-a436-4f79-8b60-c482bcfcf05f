import autoGlobals from "../../../../.eslintrc-auto-import.json";
import { confirmDialog } from '@/utils/dialog.js';
import { hasPermission } from "@/directives/can";
import message from '@/utils/message';
import { resolveExpression } from './utils';

const dummyHasPermission = (action, resource) => {
  return hasPermission(action, resource);
};

const dummyDataManager = {
  /**
   * 获取指定路径的数据值
   * @param {Object} dataObj - 数据对象
   * @param {string} path - 数据路径，如 'searchQuery.email'
   * @returns {any} 路径对应的值
   */
  get: (dataObj, path) => {
    try {
      return path
        .split(".")
        .reduce(
          (o, k) => (o && o[k] !== undefined ? o[k] : undefined),
          dataObj
        );
    } catch (e) {
      console.error("数据获取错误:", path, e);
      return undefined;
    }
  },
  /**
   * 设置指定路径的数据值
   * @param {Object} dataObj - 数据对象
   * @param {string} path - 数据路径，如 'searchQuery.email'
   * @param {any} value - 要设置的值
   */
  set: (dataObj, path, value) => {
    let target = dataObj;
    const pathParts = path.split(".");
    for (let i = 0; i < pathParts.length - 1; i++) {
      if (!target[pathParts[i]]) {
        target[pathParts[i]] = {}; // 如果中间路径不存在，则创建
      }
      target = target[pathParts[i]];
    }
    target[pathParts[pathParts.length - 1]] = value;
  },
};

const dummyApiManager = {
  apiDefinitions: null, // 将在render时注入
  
  /**
   * 根据 API ID 调用后端 API
   * @param {string} apiId - API 的唯一标识
   * @param {Object} resolvedParams - 已解析的请求参数
   * @returns {Promise<any>} API 响应
   */
  callApi: async function(apiId, resolvedParams) {
    // 查找 API 定义
    const apiDef = this.apiDefinitions?.find(api => api.id === apiId);
    if (!apiDef) {
      console.warn(`未找到 API 定义: ${apiId}`);
      return { success: false, error: 'API定义未找到' };
    }

    // 构造请求 URL
    let requestUrl = apiDef.url;
    const requestOptions = {
      method: apiDef.method || 'GET',
    };

    // 处理 URL 参数
    if (apiDef.urlParams && Array.isArray(apiDef.urlParams)) {
      const urlParams = new URLSearchParams();
      apiDef.urlParams.forEach(paramName => {
        if (resolvedParams[paramName] !== undefined) {
          urlParams.set(paramName, resolvedParams[paramName]);
        }
      });
      if (urlParams.toString()) {
        requestUrl += '?' + urlParams.toString();
      }
    }

    // 处理 body 参数
    if (apiDef.bodyParams && Array.isArray(apiDef.bodyParams)) {
      const bodyData = {};
      apiDef.bodyParams.forEach(paramName => {
        if (resolvedParams[paramName] !== undefined) {
          bodyData[paramName] = resolvedParams[paramName];
        }
      });
      
      // 如果只有一个 body 参数且它是数组，直接作为 body
      if (apiDef.bodyParams.length === 1 && Array.isArray(bodyData[apiDef.bodyParams[0]])) {
        requestOptions.body = bodyData[apiDef.bodyParams[0]];
      } else {
        requestOptions.body = bodyData;
      }
    } else if (resolvedParams && Object.keys(resolvedParams).length > 0) {
      // 如果没有明确的 bodyParams 配置，将所有参数作为 body
      requestOptions.body = resolvedParams;
    }

    // 尝试调用真实 API
    try {
      const apiFunction = window.$api || globalThis.$api;
      if (typeof apiFunction === 'function') {
        let response;
        if (requestOptions.body) {
          response = await apiFunction(requestUrl, { 
            method: requestOptions.method, 
            body: requestOptions.body
          });
        } else {
          response = await apiFunction(requestUrl, { 
            method: requestOptions.method, 
            params: resolvedParams 
          });
        }
        return response;
      } else {
        console.warn("全局 $api 函数未找到，使用模拟响应");
        return { success: true, data: { message: "模拟API调用成功" } };
      }
    } catch (error) {
      console.error(`API 调用失败: ${apiId}`, error);
      throw error;
    }
  },
  /**
   * 根据 API ID 和响应映射更新数据
   * @param {string} apiId - API 的唯一标识
   * @param {Object} response - API 原始响应
   * @param {Object} data - 页面数据对象
   */
  updateDataFromApiResponse: (apiId, response, data) => {
    console.warn(
      "updateDataFromApiResponse 占位符被调用，请实现真实数据映射逻辑:",
      apiId,
      response,
      data
    );
    // 真实场景会根据 apiId 查找 responseMapping 配置，然后更新 data
    // 例如：dataManager.set(data, 'userList', response.data.list);
  },
};

// 自动收集 AutoImport 的全局变量

function collectAutoImports() {
  const result = {};
  for (const key of Object.keys(autoGlobals.globals)) {
    if (typeof window !== "undefined" && window[key] !== undefined) {
      result[key] = window[key];
    } else if (
      typeof globalThis !== "undefined" &&
      globalThis[key] !== undefined
    ) {
      result[key] = globalThis[key];
    }
  }

  return result;
}

// 将定义的pageMethods转成真正的方法， 现在方法转换还是有问题的，待后面回头再来改
function transferPageMethods(pageMethods, context) {
  const result = {};
  for (const key in pageMethods) {
    const method = pageMethods[key];
    if (method.type === "custom" && method.script) {
      const params = method.params || [];
      result[key] = new Function(...params, method.script);
    } else if (method.type === "navigate") {
      // 假设 context 里有 router
      result[key] = (...args) => {
        // 支持 paramsMap
        let to = method.to;
        if (method.paramsMap && args.length) {
          // 创建包含传入参数的上下文，用于解析表达式
          const resolveContext = {
            ...context,
            method: method, // 添加当前方法定义到上下文
            ...(args[0] && typeof args[0] === 'object' ? args[0] : {}),
            item: args[0] // 确保 item 参数可以被访问
          };
          
          const params = {};
          for (const k in method.paramsMap) {
            // 使用 resolveExpression 解析模板表达式
            const resolvedValue = resolveExpression(method.paramsMap[k], resolveContext);
            params[k] = resolvedValue;
          }
          to = { path: method.to, query: params };
        }
        
        if (
          context &&
          context.appContext &&
          context.appContext.app &&
          context.appContext.app.config &&
          context.appContext.app.config.globalProperties &&
          context.appContext.app.config.globalProperties.$router
        ) {
          context.appContext.app.config.globalProperties.$router.push(to);
        } else if (window && window.$router) {
          window.$router.push(to);
        } else {
          const targetUrl = typeof to === "string" ? to : `${to.path}?${new URLSearchParams(to.query).toString()}`;
          window.location.href = targetUrl;
        }
      };
    } else if (method.type === "setVariable") {
      result[key] = (...args) => {
        // 这里只做简单实现，实际可结合 context.dataManager
        if (
          context &&
          context.dataManager &&
          typeof context.dataManager.set === "function"
        ) {
          context.dataManager.set(context.data, method.variable, method.value);
        } else if (context && context.data) {
          context.data[method.variable] = method.value;
        }
      };
    } else if (method.type === "confirm") {
      result[key] = (...args) => {
        // 这里只做简单实现，实际可结合 UI 框架
        if (window.confirm(method.text || "Are you sure?")) {
          // 支持 onConfirm
          if (Array.isArray(method.onConfirm)) {
            method.onConfirm.forEach((action) => {
              if (typeof result[action.action] === "function") {
                result[action.action](...(action.params || []));
              }
            });
          }
        }
      };
    } else {
      result[key] = () => {};
    }
  }
  return result;
}

/**
 * 创建渲染上下文对象
 * 这个函数汇集了渲染时所需的所有数据、方法、工具函数等，并通过组件树层层传递
 * @param {Object} initialData - 页面/组件的响应式数据 (通常是 reactive 或 ref 包裹的)
 * @param {Object} pageMethods - 页面级的方法定义 (methods map)
 * @param {Object} apis - API 定义
 * @param {Object} globalSlots - 全局插槽定义 (例如页面级的自定义插槽)
 * @param {Object} appContext - Vue 应用程序的上下文对象
 * @returns {Object} 渲染上下文对象
 */
export function createRenderContext(
  initialData,
  pageMethods,
  apis,
  globalSlots,
  appContext
) {
  // 1. 组装 context
  const context = {
    data: initialData,
    globalSlots,
    appContext,
    hasPermission: dummyHasPermission,
    dataManager: dummyDataManager,
    apiManager: { ...dummyApiManager, apiDefinitions: apis },
    confirmDialog: confirmDialog, // 注入 confirmDialog
    message: message, // 注入 message
    ...collectAutoImports(),
    renderComponentFn: null,
    renderSlotFn: null,
  };

  // 挂载 setData 辅助方法
  context.setData = function (key, value) {
    if (Array.isArray(this.data[key]) && Array.isArray(value)) {
      this.data[key].splice(0, this.data[key].length, ...value);
    } else {
      this.data[key] = value;
    }
  };

  // 2. 将所有 pageMethods 挂到 context 上（不处理 context 参数，全部 this 指向 proxy）
  if (pageMethods) {
    for (const key in pageMethods) {
      const method = pageMethods[key];
      if (method.type === "custom" && typeof context[key] !== "function") {
        const params = method.params || [];
        context[key] = new Function(...params, method.script);
      } else if (method.type === "navigate") {
        context[key] = (...args) => {
          let to = method.to;
          if (method.paramsMap && args.length) {
            const params = {};
            // 创建包含传入参数的上下文，用于解析表达式
            const resolveContext = {
              ...context,
              method: method, // 添加当前方法定义到上下文
              ...(args[0] && typeof args[0] === 'object' ? args[0] : {}),
              item: args[0] // 确保 item 参数可以被访问
            };
            
            for (const k in method.paramsMap) {
              // 使用 resolveExpression 解析模板表达式
              const resolvedValue = resolveExpression(method.paramsMap[k], resolveContext);
              params[k] = resolvedValue;
            }
            to = { path: method.to, query: params };
          }
          
          if (context?.appContext?.app?.config?.globalProperties?.$router) {
            context.appContext.app.config.globalProperties.$router.push(to);
          } else if (window?.$router) {
            window.$router.push(to);
          } else {
            const targetUrl = typeof to === "string" ? to : `${to.path}?${new URLSearchParams(to.query).toString()}`;
            window.location.href = targetUrl;
          }
        };
      } else if (method.type === "setVariable") {
        context[key] = (...args) => {
          if (context?.dataManager?.set) {
            context.dataManager.set(
              context.data,
              method.variable,
              method.value
            );
          } else if (context?.data) {
            context.data[method.variable] = method.value;
          }
          if (Array.isArray(method.then)) {
            method.then.forEach((action) => {
              if (typeof context[action.action] === "function") {
                context[action.action](...(action.params || []));
              }
            });
          }
        };
      } else if (method.type === "confirm") {
        context[key] = (...args) => {
          // 创建包含传入参数的上下文，用于解析表达式
          const resolveContext = {
            ...context,
            method: method, // 添加当前方法定义到上下文
            ...(args[0] && typeof args[0] === 'object' ? args[0] : {}),
            item: args[0] // 确保 item 参数可以被访问
          };
          
          // 解析动态的 text、title、按钮文本和颜色
          const text = method.text ? resolveExpression(method.text, resolveContext) : "Are you sure?";
          const title = method.title ? resolveExpression(method.title, resolveContext) : "Confirm";
          const confirmButtonText = method.confirmButtonText ? resolveExpression(method.confirmButtonText, resolveContext) : "Confirm";
          const confirmButtonColor = method.confirmButtonColor ? resolveExpression(method.confirmButtonColor, resolveContext) : "primary";
          const cancelButtonText = method.cancelButtonText ? resolveExpression(method.cancelButtonText, resolveContext) : "Cancel";
          
          // 创建一个执行 action 的通用函数
          const executeAction = (action, context, resolveContext) => {
            if (action.action === "callApi") {
              // 处理 API 调用
              if (context.apiManager && typeof context.apiManager.callApi === "function") {
                const resolvedParams = action.params ? Object.keys(action.params).reduce((acc, key) => {
                  const originalValue = action.params[key];
                  const resolvedValue = resolveExpression(originalValue, resolveContext);
                  acc[key] = resolvedValue;
                  return acc;
                }, {}) : {};
                
                return context.apiManager.callApi(action.apiId, resolvedParams).then((response) => {
                  // 如果API调用成功，执行onSuccess回调
                  if (action.onSuccess && Array.isArray(action.onSuccess)) {
                    return action.onSuccess.reduce((promise, successAction, index) => {
                      return promise.then(() => {
                        return executeAction(successAction, context, resolveContext);
                      }).catch(successErr => {
                        console.error(`第${index + 1}个onSuccess action执行失败:`, successErr);
                        throw successErr; // 重新抛出错误，这样会被外层catch捕获
                      });
                    }, Promise.resolve());
                  }
                }).catch(err => {
                  console.error(`API ${action.apiId} 调用失败或onSuccess回调执行失败:`, err);
                  // 如果API调用失败，执行onError回调
                  if (action.onError && Array.isArray(action.onError)) {
                    return action.onError.reduce((promise, errorAction, index) => {
                      return promise.then(() => {
                        return executeAction(errorAction, context, resolveContext);
                      });
                    }, Promise.resolve());
                  } else {
                    // 如果没有错误处理回调，重新抛出错误
                    throw err;
                  }
                });
              }
            } else if (action.action === "message") {
              // 处理消息提示
              const messageText = resolveExpression(action.text, resolveContext);
              if (context.message && typeof context.message[action.messageType] === "function") {
                context.message[action.messageType](messageText);
              } else if (window.$message && typeof window.$message[action.messageType] === "function") {
                window.$message[action.messageType](messageText);
              } else {
                console.log(`${action.messageType}: ${messageText}`);
              }
              return Promise.resolve();
            } else if (typeof context[action.action] === "function") {
              // 处理普通方法调用
              const resolvedParams = action.params ? (
                Array.isArray(action.params) 
                  ? action.params.map(p => resolveExpression(p, resolveContext))
                  : Object.keys(action.params).reduce((acc, key) => {
                      acc[key] = resolveExpression(action.params[key], resolveContext);
                      return acc;
                    }, {})
              ) : [];
              
              if (Array.isArray(resolvedParams)) {
                context[action.action](...resolvedParams);
              } else {
                context[action.action](resolvedParams);
              }
              return Promise.resolve();
            }
            return Promise.resolve();
          };

          // 执行 actions 的通用函数
          const executeActions = (callback) => {
            if (Array.isArray(method.onConfirm)) {
              method.onConfirm.reduce((promise, action) => {
                return promise.then(() => executeAction(action, context, resolveContext));
              }, Promise.resolve()).then(() => {
                if (callback) callback();
              }).catch(err => {
                console.error('执行确认操作时发生错误:', err);
                if (callback) callback();
              });
            } else if (callback) {
              callback();
            }
          };

          // 使用项目中的 confirmDialog
          if (typeof context.confirmDialog === 'function') {
            context.confirmDialog({
              title: title,
              text: text,
              confirmButtonText: confirmButtonText,
              confirmButtonColor: confirmButtonColor,
              cancelButtonText: cancelButtonText,
              onConfirm: (close) => {
                executeActions(close);
              },
              onCancel: () => {
                // User cancelled the operation
              }
            });
          } else if (window.confirm(text)) {
            executeActions();
          }
        };
      }
    }
  }

  // 3. Proxy 统一代理属性和方法访问
  const proxy = new Proxy(context, {
    get(target, prop, receiver) {
      // 优先查 data
      if (prop in target.data) return target.data[prop];
      // 查 context
      const value = target[prop];
      if (typeof value === "function") {
        // 自动绑定 this 为 proxy
        return value.bind(proxy);
      }
      return value;
    },
    set(target, prop, value) {
      target[prop] = value;
      return true;
    },
  });

  return proxy;
}

export default createRenderContext;
