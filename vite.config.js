import {
  VueRouterAutoImports,
  getPascalCaseRouteName,
} from "unplugin-vue-router";
import { defineConfig, loadEnv } from "vite";

import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import Layouts from "vite-plugin-vue-layouts";
import Unocss from "unocss/vite";
import VueDevTools from "vite-plugin-vue-devtools";
import VueI18nPlugin from "@intlify/unplugin-vue-i18n/vite";
import VueRouter from "unplugin-vue-router/vite";
import { fileURLToPath } from "node:url";
import svgLoader from "vite-svg-loader";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import vuetify from "vite-plugin-vuetify";
import { webUpdateNotice } from "@plugin-web-update-notification/vite";

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // Load environment variables
  const env = loadEnv(mode, process.cwd());
  console.log("Build Mode:", mode);
  console.log("Build Base URL:", env.VITE_BASE_URL);

  return {
    base: "/",
    plugins: [
      // Docs: https://github.com/posva/unplugin-vue-router
      // ℹ️ This plugin should be placed before vue plugin
      VueRouter({
        getRouteName: (routeNode) => {
          // Convert pascal case to kebab case
          return getPascalCaseRouteName(routeNode)
            .replace(/([a-z\d])([A-Z])/g, "$1-$2")
            .toLowerCase();
        },
        dts: false,
      }),
      vue({
        template: {
          compilerOptions: {
            isCustomElement: (tag) =>
              tag === "swiper-container" || tag === "swiper-slide",
          },
        },
      }),
      VueDevTools(),
      vueJsx(),
      Unocss({
        // 可以在这里添加额外配置
      }),

      // Docs: https://github.com/vuetifyjs/vuetify-loader/tree/master/packages/vite-plugin
      vuetify({
        styles: {
          configFile: "src/assets/styles/variables/_vuetify.scss",
        },
        autoImport: true,
        labComponents: true,
      }),

      // Docs: https://github.com/johncampionjr/vite-plugin-vue-layouts#vite-plugin-vue-layouts
      Layouts({
        layoutsDirs: "./src/layouts/",
      }),

      // Docs: https://github.com/antfu/unplugin-vue-components#unplugin-vue-components
      Components({
        dirs: ["src/components"],
        dts: false, // Disable auto-generated types component.d.ts file
        resolvers: [
          (componentName) => {
            // Auto import `VueApexCharts`
            if (componentName === "VueApexCharts")
              return {
                name: "default",
                from: "vue3-apexcharts",
                as: "VueApexCharts",
              };
          },
        ],
      }),

      // Docs: https://github.com/antfu/unplugin-auto-import#unplugin-auto-import
      AutoImport({
        imports: [
          "vue",
          VueRouterAutoImports,
          "@vueuse/core",
          "@vueuse/math",
          "vue-i18n",
          "pinia",
        ],
        dirs: [
          "./src/hooks/",
          "./src/utils/",
        ],
        vueTemplate: true,

        // ℹ️ Disabled to avoid confusion & accidental usage
        ignore: ["useCookies", "useStorage"],
        eslintrc: {
          enabled: true,
          filepath: "./.eslintrc-auto-import.json",
        },
        dts: false,
      }),

      // Docs: https://github.com/intlify/bundle-tools/tree/main/packages/unplugin-vue-i18n#intlifyunplugin-vue-i18n
      VueI18nPlugin({
        runtimeOnly: true,
        compositionOnly: true,
        include: [
          fileURLToPath(
            new URL("./src/plugins/i18n/locales/**", import.meta.url)
          ),
        ],
      }),
      svgLoader(),
      webUpdateNotice({
        logVersion: true,
        notificationProps: {
          title: "New Version Available",
          description: "Please refresh the page to use the latest version.",
          buttonText: "Refresh",
          dismissButtonText: "Dismiss",
        },
      }),
    ],
    define: {
      "process.env": env,
    },
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
        "@renderer": fileURLToPath(
          new URL("./src/business/low-code-engine/renderer", import.meta.url)
        ),
        "@themeConfig": fileURLToPath(
          new URL("./themeConfig.js", import.meta.url)
        ),
        "@core": fileURLToPath(new URL("./src/@core", import.meta.url)),
        "@layouts": fileURLToPath(
          new URL("./src/layouts/common", import.meta.url)
        ),
        "@images": fileURLToPath(
          new URL("./src/assets/images/", import.meta.url)
        ),
        "@styles": fileURLToPath(
          new URL("./src/assets/styles/", import.meta.url)
        ),
        "@configured-variables": fileURLToPath(
          new URL(
            "./src/assets/styles/variables/_template.scss",
            import.meta.url
          )
        ),
        "@db": fileURLToPath(
          new URL("./src/plugins/fake-api/handlers/", import.meta.url)
        ),
        "@api-utils": fileURLToPath(
          new URL("./src/plugins/fake-api/utils/", import.meta.url)
        ),
      },
    },
    // 添加到 vite.config.js
    esbuild: {
      // 使用esbuild加速JS转换
      jsxFactory: "h",
      jsxFragment: "Fragment",
      target: "es2020",
    },
    build: {
      assetsDir: "assets",
      chunkSizeWarningLimit: 5000,
      commonjsOptions: {
        transformMixedEsModules: true,
      },
      cache: true,
      rollupOptions: {
        external: ["@plugin-web-update-notification/vue"],
        output: {
          // Add hash to ensure unique filenames
          assetFileNames: "assets/[name]-[hash][extname]",
          chunkFileNames: "assets/[name]-[hash].js",
        },
      },
    },
    optimizeDeps: {
      include: [
        "dayjs",
        "dayjs/plugin/utc",
        "dayjs/plugin/timezone",
        "vue-router",
        "pinia",
        "@vueuse/core",
        "lodash-es",
        // 添加更多常用依赖
      ],
      exclude: ["vuetify"],
      entries: ["./src/**/*.vue"],
    },
    server: {
      host: "0.0.0.0",
      port: 5173,
      proxy: {
        "/api": {
          target: env.VITE_API_URL || env.VITE_BASE_URL,
          changeOrigin: true,
        },
      },
      force: false,
    },
    cacheDir: ".vite",
  };
});
