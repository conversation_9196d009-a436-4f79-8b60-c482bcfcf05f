<script setup>
import { useTheme } from 'vuetify'
import ScrollToTop from '@/components/ScrollToTop.vue'
import initCore from '@/config/initCore'
import {
  initConfigStore,
  useConfigStore,
} from '@/stores/configStore'
import { hexToRgb } from '@/utils/colorConverter'
import { useAuthStore } from '@/stores/useAuthStore'

const { global } = useTheme()

// ℹ️ Sync current theme with initial loader theme
initCore()
initConfigStore()

const configStore = useConfigStore()
const authStore = useAuthStore()

</script>

<template>
  <VLocaleProvider :rtl="configStore.isAppRTL">
    <!-- ℹ️ This is required to set the background color of active nav link based on currently active global theme's primary -->
    <VApp :style="`--v-global-theme-primary: ${hexToRgb(global.current.value.colors.primary)}`">
      <!-- Global loading -->
      <VOverlay :model-value="authStore.loading" class="align-center justify-center">
        <VProgressCircular indeterminate color="primary" />
      </VOverlay>

      <RouterView />
      <ScrollToTop />
    </VApp>
  </VLocaleProvider>
</template>
