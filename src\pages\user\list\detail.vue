<script setup>
import { useRouter, useRoute } from "vue-router";
import UserForm from "@/business/user/list/form.vue";
import { dateUtil } from "@/utils/day";
import { useUserDetail, getRoleList } from "@/business/user/list/useUserDetail";

definePage({
  meta: {
    navActiveLink: 'user-list',
    breadcrumb: 'User Detail',
  },
});

const router = useRouter();
const route = useRoute();
const userId = computed(() => route.query.id)
const { userData } = useUserDetail(userId.value)


const userFormRef = ref(null);

// form data
const formModel = ref({
  id: "",
  email: "",
  name: "",
  statusId: true,
  roleList: [],
});

watch(userData, () => {
  formModel.value.id = userData.value.accountId
  formModel.value.email = userData.value.accountEmail
  formModel.value.name = userData.value.accountName
  formModel.value.statusId = userData.value.accountStatusId
  formModel.value.roleList = getRoleList(userData.value.userGroupList).map(item => item.id)
})

const handleCancel = () => {
  userFormRef.value?.resetForm();
  router.back();
};

</script>

<template>
  <VCard>
    <VCardItem class="pb-4 px-0">
      <VCardTitle> User Detail </VCardTitle>
    </VCardItem>

    <UserForm ref="userFormRef" v-model="formModel" type="view" />

    <VRow class="d-flex mt-3">
      <VCol cols="12" class="d-flex justify-end">
        <VBtn color="primary" class="ml-2" style="width: 120px;" @click="handleCancel">
          BACK
        </VBtn>
      </VCol>
    </VRow>
  </VCard>
</template>
