import { afterAll, afterEach, beforeAll, beforeEach } from 'vitest'

import puppeteer from 'puppeteer'

beforeAll(async () => {
  // Launch browser
  global.browser = await puppeteer.launch({
    headless: false,  // Enable headed mode
    args: [
      '--no-sandbox', // Disable sandbox
      '--disable-setuid-sandbox', // Disable setuid sandbox 
      '--start-maximized'  // Maximize window on startup
    ]
  })
})

beforeEach(async () => {
  // Create new page for each test
  global.page = await global.browser.newPage()
  
  // Get screen dimensions and set viewport
  const screen = await global.page.evaluate(() => ({
    width: window.screen.availWidth,
    height: window.screen.availHeight
  }))

  // Set viewport to maximum size
  await global.page.setViewport({
    width: screen.width,
    height: screen.height
  })
  
})

afterEach(async () => {
  // Close current page
  await global.page.close()
})

afterAll(async () => {
  // Close browser
  await global.browser.close()
}) 