import "@styles/scss/template/libs/vuetify/index.scss";
import "vuetify/styles";

import { staticPrimaryColor, staticPrimaryDarkenColor, themes } from "./theme";

import { VBtn } from "vuetify/components/VBtn";
import checkboxPlugin from "./checkbox-plugin";
import { cookieRef } from "@layouts/stores/config";
import { createVueI18nAdapter } from "vuetify/locale/adapters/vue-i18n";
import { createVuetify } from "vuetify";
import { deepMerge } from "@antfu/utils";
import defaults from "./defaults";
import { getI18n } from "@/plugins/i18n/index";
import { icons } from "./icons";
import { themeConfig } from "@themeConfig";
import { useI18n } from "vue-i18n";

// Styles

export default function (app) {
  const cookieThemeValues = {
    defaultTheme: resolveVuetifyTheme(themeConfig.app.theme),
    themes: {
      light: {
        colors: {
          primary: cookieRef("lightThemePrimaryColor", staticPrimaryColor)
            .value,
          "primary-darken-1": cookieRef(
            "lightThemePrimaryDarkenColor",
            staticPrimaryDarkenColor
          ).value,
        },
      },
      dark: {
        colors: {
          primary: cookieRef("darkThemePrimaryColor", staticPrimaryColor).value,
          "primary-darken-1": cookieRef(
            "darkThemePrimaryDarkenColor",
            staticPrimaryDarkenColor
          ).value,
        },
      },
    },
  };

  const optionTheme = deepMerge({ themes }, cookieThemeValues);

  const vuetify = createVuetify({
    aliases: {
      IconBtn: VBtn,
    },
    defaults,
    icons,
    theme: optionTheme,
    locale: {
      adapter: createVueI18nAdapter({ i18n: getI18n(), useI18n }),
    },
  });

  app.use(vuetify);
  // app.use(checkboxPlugin);
}
