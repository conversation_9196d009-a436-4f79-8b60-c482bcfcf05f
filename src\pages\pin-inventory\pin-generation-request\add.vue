<script setup>
import { useRouter } from 'vue-router'
import PinForm from '@/business/pin-generator/form.vue'
definePage({
  meta: {
    navActiveLink: 'create-pin',
    breadcrumb: 'Create New RG PIN Generation Request',
  },
})

const router = useRouter()
const pinRequestFormRef = ref(null)
const isSaving = ref(false)

// form data
const formModel = ref({
    needSFTP: false,
    razerGoldPINGroup: null,
    quantity: null
})

const handleCancel = () => {
  pinRequestFormRef.value?.resetForm()
  router.back()
}

const handleSave = async () => {
  if (isSaving.value) return
  // validate form
  const { valid, errors } = await pinRequestFormRef.value?.validate()

  if (!valid) return
  isSaving.value = true
  confirmDialog({
    title: `Submit RG PIN Generation Request?`,
    text: 'Once submitted, this action cannot be undone.',
    confirmButtonText: `SUBMIT`,
    confirmButtonColor: '#44D62C',
    confirmButtonTextColor: '#000000',
    cancelButtonText: 'CANCEL',
    onConfirm: async function(close) {
      try {
        const params = pinRequestFormRef.value?.getParams()
        params.requestType = "PIN_GENERATE"
        await $api('/api/admin-api/v1/request', {
          method: 'POST',
          body: params
        })
        message.success('PIN request created successfully')
        router.back()
        close()
      } catch (error) {
        console.error(error)
        message.error(error._data?.message || error.message)
      } finally {
        isSaving.value = false
      }
    },
    onCancel: function() {
      isSaving.value = false
    }
  })
}
</script>

<template>
  <VCard>
    <VCardItem class="pb-4 px-0">
      <VCardTitle>
        Create New RG PIN Generation Request
      </VCardTitle>
    </VCardItem>
    
    <PinForm
      ref="pinRequestFormRef"
      v-model="formModel"
      type="add"
    />
    
    <VRow class="d-flex mt-3">
      <VCol cols="5" class="d-flex justify-end">
        <VBtn variant="outlined" color="default" @click="handleCancel">
          CANCEL
        </VBtn>
        <VBtn color="primary" class="ml-2" style="width: 120px;" data-testid="save-button" @click="handleSave">
          SUBMIT
        </VBtn>
      </VCol>
    </VRow>
  </VCard>
</template>
