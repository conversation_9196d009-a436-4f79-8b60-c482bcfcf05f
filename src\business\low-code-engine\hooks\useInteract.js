import interact from 'interactjs';

export function useInteract(el, options) {
  const position = ref({ x: 0, y: 0 });
  let updateFrame
  onMounted(() => {
    interact(el.value).draggable({
      modifiers: options.modifiers,
      listeners: {
        move(event) {
          if (!updateFrame) {
            updateFrame = requestAnimationFrame(() => {
              position.value.x += event.dx;
              position.value.y += event.dy;
              updateFrame = null;
            });
          }
        },
      },
    });
  });

  return { position };
}