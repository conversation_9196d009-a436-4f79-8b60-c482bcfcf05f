

/**
 * A composition function that returns a reactive group role tree.
 *
 * @returns {{
 *   groupRoleTree: Ref<VTreeviewItem[]>
 * }}
 */
export const useGroupRoleTree = (userGroupName = '') => {
  const groupRoleTree = ref([])
  const loadData = async () => {
    try {
    const res = await $api(`/api/admin-api/v1/user-group/query-all-with-role?userGroupName=${userGroupName}`, {
      method: 'GET'
    })
    groupRoleTree.value = res.data?.map(item => ({
      id: `group-${item.id}`,
      name: item.name,
      hasRole: !!item.roleList,
      children: item.roleList?.length ? item.roleList : null
    })).filter(item => item.children?.length)
    } catch (error) {
      console.error('Error loading group role tree:', error)
      groupRoleTree.value = []
    }

    // groupRoleTree.value = mockData()
  }
  onMounted(async () => {
    await loadData()
  })
  return {
    groupRoleTree,
  }
}

function mockData() {
  return [
    {
      id: 1,
      name: 'Admin Group',
      children: [
        { id: 2, name: 'Executive Director' },
        { id: 3, name: 'Operation Director' },
        { id: 4, name: 'Finance Director' },
        { id: 5, name: 'Product Director' },
        { id: 6, name: 'Compliance Director' },
        { id: 7, name: 'Procurement Director' },
      ],
    },
    {
      id: 2,
      name: 'Role Group 2',
      children: [
        { id: 8, name: 'Role 1' },
        { id: 9, name: 'Role 2' },
        { id: 10, name: 'Role 3' },
        { id: 11, name: 'Role 4' },
        { id: 12, name: 'Role 5' },
        { id: 13, name: 'Role 6' },
        { id: 14, name: 'Role 7' },
        { id: 15, name: 'Role 8' },
        { id: 16, name: 'Role 9' },
        { id: 17, name: 'Role 10' },
      ],
    },
  ]
}
