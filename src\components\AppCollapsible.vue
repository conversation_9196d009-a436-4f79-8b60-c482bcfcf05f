<script setup>
const props = defineProps({
  // 要显示的所有项目
  items: {
    type: Array,
    required: true,
  },
  // 默认显示的项目数量
  visibleCount: {
    type: Number,
    default: 1,
  },
  // 自定义渲染每个项目的插槽
  itemKey: {
    type: String,
    default: 'id',
  },
  // 芯片颜色
  chipColor: {
    type: String,
    default: 'primary',
  },
})

const isExpanded = ref(false)

// 计算可见和隐藏的项目
const visibleItems = computed(() => {
  if (!props.items.length) return []
  
  // 根据名字长度排序，选择最长的作为可见项
  const sortedItems = [...props.items].sort((a, b) => {
    const aLength = (a.name || String(a)).length
    const bLength = (b.name || String(b)).length
    return bLength - aLength
  })
  
  return [sortedItems[0]]
})

const hiddenItems = computed(() => {
  if (!props.items.length) return []
  return props.items.filter(item => !visibleItems.value.includes(item))
})

// 获取芯片的宽度
const chipRef = ref(null)
const menuWidth = computed(() => {
  if (chipRef.value) {
    return `${chipRef.value.$el.offsetWidth}px`
  }
  return 'auto'
})
</script>

<template>
  <div class="app-collapsible">
    <VMenu
      v-model="isExpanded"
      :close-on-content-click="true"
      location="bottom end"
      :min-width="menuWidth"
      offset="2"
    >
      <template #activator="{ props: menuProps }">
        <VChip
          ref="chipRef"
          :color="chipColor"
          size="small"
          label
          class="text-capitalize cursor-pointer"
          v-bind="menuProps"
          v-if="visibleItems.length"
        >
          <slot
            :item="visibleItems[0]"
            name="item"
          >
            {{ visibleItems[0] }}
          </slot>
          <template v-if="hiddenItems.length">
            <span class="ms-1 pl-1">(+{{ hiddenItems.length }})</span>
            <VIcon
              size="16"
              icon="tabler-chevron-down"
              class="ms-1"
              :class="{ 'expanded': isExpanded }"
            />
          </template>
        </VChip>
      </template>

      <VList 
        density="compact" 
        class="dropdown-list pa-0"
        nav
      >
        <VListItem
          v-for="item in hiddenItems"
          :key="item[itemKey]"
          :value="item[itemKey]"
          class="text-capitalize list-item text-xs"
          :class="`text-${chipColor}`"
          density="compact"
        >
          <slot
            :item="item"
            name="item"
          >
            {{ item }}
          </slot>
        </VListItem>
      </VList>
    </VMenu>
  </div>
</template>

<style lang="scss" scoped>
.app-collapsible {
  display: inline-flex;
  align-items: center;

  .expanded {
    transform: rotate(180deg);
    transition: transform 0.2s ease;
  }

  :deep(.v-chip) {
    .v-icon {
      margin-top: -1px;
    }
  }

  :deep(.dropdown-list) {
    .v-list-item {
      min-height: 20px;
      padding: 2px 8px;
      font-size: 0.75rem !important;
      line-height: 1;
      
      &:hover {
        background-color: rgba(var(--v-theme-primary), 0.05);
        opacity: 0.85;
      }

      &.list-item {
        margin-block: 0;
      }

      .v-list-item__content {
        padding: 2px 0;
        min-height: unset;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

:deep(.v-menu) {
  .v-overlay__content {
    background: rgb(var(--v-theme-surface));
    border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
    border-radius: 6px;
    box-shadow: 0 4px 8px rgba(var(--v-shadow-key-umbra-color), 0.1);
    overflow: hidden;
    margin-top: 2px;
  }
}
</style>