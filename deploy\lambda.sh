#!/usr/bin/env bash
set +e

declare -xu lambda_name

case $ENV in
  zgd)
  code_env=develop
  role=arn:aws:iam::903306222264:role/zgd-role-lambda
  ;;

  zgq)
  code_env=qa
  role=arn:aws:iam::903306222264:role/ZGQ-ROLE-LAMBDA
  ;;

  zgs)
  code_env=sb
  role=arn:aws:iam::903306222264:role/ZGS-ROLE-LAMBDA
  ;;

  zgp)
  code_env=production
  role=arn:aws:iam::903306222264:role/ZGP-ROLE-LAMBDA
  ;;

esac

lambda_name=${ENV}-lambda-$SERVICE_NAME
lambda_runtime=$(<./config/runtime.config)
lambda_handler=$(<./config/handler.config)
lambda_description=$(<./config/description.config)
lambda_timeout=$(<./config/timeout.config)

our_env=$(readarray -t ARRAY < ./config/env/${code_env}.config; IFS=','; echo "${ARRAY[*]}")
lambda_env="Variables={$our_env}"

# Pull NPM Libraries
npm install --omit=dev

# Archive all Project Files
# man zip
# - http://ubuild.zgold-dev.razer.com:8080/job/ZGD-DEPLOY-LAMBDA-EMAIL/16/console

echo "Zipping $lambda_name.zip"
zip -r "$lambda_name".zip * -x .git/**\* -x .idea/**\* -x config/**\* -x deploy/**\*
echo "Done Zipping"

lambdaExist=$(aws lambda get-function --function-name arn:aws:lambda:"$REGION":903306222264:function:"$lambda_name" --region "$REGION")
if [[ -z "$lambdaExist" ]]
then
  echo "create_lambda"

  common_init "$ENV" "$SERVICE_ENV" "$REGION"

  common_get_subnet
  subnets=$RETURN_VALUE
  echo "subnets $subnets"

  common_get_security_group "$ENV" "$DEPLOY_ENV" "$REGION"
  securityGroup=$RETURN_VALUE
  echo "securityGroup $securityGroup"

  echo "LAMBDA_LAYER $LAMBDA_LAYER"

  if [[ -z "$LAMBDA_LAYER" ]]
  then
    echo "create function without layer"

    aws lambda create-function \
      --function-name arn:aws:lambda:"$REGION":903306222264:function:"$lambda_name" \
      --runtime "$lambda_runtime" \
      --role "$role" \
      --handler "$lambda_handler" \
      --description "$lambda_description" \
      --timeout "$lambda_timeout" \
      --environment "$lambda_env"  \
      --zip-file fileb://"$WORKSPACE"/"$lambda_name".zip \
      --tags "$TAGS_LAMBDA" \
      --vpc-config SubnetIds="$subnets",SecurityGroupIds="$securityGroup" \
      --memory-size "$LAMBDA_MEMORY" \
      --region "$REGION"
  else

  echo "create function with layer"

    aws lambda create-function \
      --function-name arn:aws:lambda:"$REGION":903306222264:function:"$lambda_name" \
      --runtime "$lambda_runtime" \
      --role "$role" \
      --handler "$lambda_handler" \
      --description "$lambda_description" \
      --timeout "$lambda_timeout" \
      --environment "$lambda_env"  \
      --zip-file fileb://"$WORKSPACE"/"$lambda_name".zip \
      --tags "$TAGS_LAMBDA" \
      --vpc-config SubnetIds="$subnets",SecurityGroupIds="$securityGroup" \
      --region "$REGION" \
      --memory-size "$LAMBDA_MEMORY" \
      --layers "$LAMBDA_LAYER"
  fi
else
  echo "update_lambda"
  # Update AWS Lambda Function Code & Configuration
  aws lambda update-function-code --function-name arn:aws:lambda:"$REGION":903306222264:function:"$lambda_name" --zip-file fileb://"$WORKSPACE"/"$lambda_name".zip --region "$REGION"
  aws lambda wait function-updated --function-name arn:aws:lambda:"$REGION":903306222264:function:"$lambda_name" --region "$REGION"
  if [[ -z "$LAMBDA_LAYER" ]]
  then
    echo "update_function_without_layer"
    aws lambda update-function-configuration --function-name arn:aws:lambda:"$REGION":903306222264:function:"$lambda_name" --runtime "$lambda_runtime" --handler "$lambda_handler" --description "$lambda_description" --timeout "$lambda_timeout" --environment "$lambda_env" --memory-size "$LAMBDA_MEMORY" --region "$REGION"
  else
    echo "update_function_with_layer"
    aws lambda update-function-configuration --function-name arn:aws:lambda:"$REGION":903306222264:function:"$lambda_name" --runtime "$lambda_runtime" --handler "$lambda_handler" --description "$lambda_description" --timeout "$lambda_timeout" --environment "$lambda_env" --memory-size "$LAMBDA_MEMORY" --layers "$LAMBDA_LAYER" --region "$REGION"
  fi
fi
