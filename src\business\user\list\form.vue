<script setup>
import { VTreeview } from 'vuetify/labs/VTreeview'
import AppTree from '@/components/AppTree.vue'
import { useGroupRoleTree } from './useGroupRoleTree'
import { useRoute } from 'vue-router'
import { useUserDetail } from './useUserDetail'
import { useHeight } from '@/hooks/useHeight'

// form data
const formModel = defineModel({
  type: Object,
  required: true,
})

const props = defineProps({
  type: {
    type: String,
    default: 'add',
  },
})

const formRef = ref(null)
const selected = ref([])
const treeRef = ref(null)


const isView = computed(() => props.type === 'view')
const isEdit = computed(() => props.type === 'edit')
const isAdd = computed(() => props.type === 'add')

// Handle expanded state changes
const expanded = ref([])

const { groupRoleTree: items } = useGroupRoleTree()


// height calculation logic
const cardRef = ref(null)
const {height: treeviewHeight} = useHeight(cardRef)



watch(
  formModel,
  () => {
    // if roleList is not empty, set selected value
    if (formModel.value.roleList && formModel.value.roleList.length) {
      selected.value = formModel.value.roleList
    }
  },
  { deep: true, immediate: true }
)

// const calculateHeight = () => {
//   if (!cardRef.value) return

//   const cardRect = cardRef.value.$el.getBoundingClientRect()
//   const cardHeaderHeight = 68
//   const bottomPadding = 180
//   const availableHeight = document.documentElement.clientHeight - cardRect.top - cardHeaderHeight - bottomPadding

//   treeviewHeight.value = availableHeight
// }

// onMounted(() => {
//   calculateHeight()
//   window.addEventListener('resize', calculateHeight)
// })

// onUnmounted(() => {
//   window.removeEventListener('resize', calculateHeight)
// })

// expose methods and data to parent component
defineExpose({
  form: formRef,
  validate: () => {
    return formRef.value.validate()
  },
  getParams: () => {
    const params = {
      email: formModel.value.email,
      name: formModel.value.name,
      statusId: formModel.value.statusId,
      roleList: treeRef.value?.getSelectedLeafNodes()?.filter(item => !item.hasRole).map((item) => {
        return {
          id: item.id,
          name: item.name,
          statusId: item.statusId,
        }
      }),
    }
    if (formModel.value.id) {
      params.id = formModel.value.id
    }
    return params
  },
  resetForm: () => {
    formModel.value = {
      email: '',
      name: '',
      statusId: 1,
      roleList: [],
    }
    selected.value = []
  }
})
</script>

<template>
  <VCard ref="cardRef">
    <VRow class="d-flex">
      <VCol cols="12" md="5" xl="5" style="width: 40%;">
        <div class="form-wrapper">
          <VCardTitle>
            1. Enter User’s Info
          </VCardTitle>
          <VCardText>
            <VForm ref="formRef">
              <VRow>
                <VCol cols="12">
                  <AppTextField v-model="formModel.email" label="Email*" type="email" placeholder="<EMAIL>" :disabled="isEdit" :readonly="isView" :rules="isAdd || isEdit ? [requiredValidator, emailValidator] : []" />
                </VCol>

                <VCol cols="12">
                  <AppTextField v-model="formModel.name" label="Name" placeholder="" :disabled="isAdd || isEdit" :readonly="isView" />
                </VCol>

                <VCol cols="12">
                  <VSwitch v-model="formModel.statusId" label="Enabled" :false-value="0" :true-value="1"
                    color="primary" :readonly="isView" />
                </VCol>
              </VRow>
            </VForm>
          </VCardText>
        </div>
      </VCol>
      <VCol cols="12" md="7" xl="7" style="width: 60%;">
        <div class="form-wrapper">
          <VCardTitle>
            2. Assign User in User Groups and Select the User Roles
          </VCardTitle>
          <VCardText>
            <template v-if="!items?.length">
              <VCol cols="12" class="d-flex justify-center align-center pa-10">
                <VCard flat class="text-center pa-8 empty-data">
                  <VIcon icon="tabler-clipboard-x" size="96" class="mb-4 text-medium-emphasis" />
                  <h6 class="text-h6 mb-2">No data</h6>
                </VCard>
              </VCol>
            </template>
            <AppTree v-else ref="treeRef" v-model="selected" :items="items" v-model:expanded="expanded"
              :max-height="treeviewHeight" :disabled="isView" />
          </VCardText>
        </div>
      </VCol>
    </VRow>
  </VCard>
</template>

<style lang="scss" scoped>
.form-wrapper {
  background: #1e1e1e;
  border-radius: 16px;
  height: 100%;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
}

.empty-data {
  background: transparent;
}

:deep(.v-card-text) {
  padding-bottom: 0px !important;
}

:deep(.v-treeview) {
  overflow-y: auto;
  flex: 1;


  // Adjust the overall style of the tree view
  .v-treeview-group {
    margin-left: 0;

    // Background color for selected state
    &:has(.v-selection-control--dirty) {
      background-color: #303030;

      // Ensure parent node also has background color
      .v-treeview-node__root {
        background-color: #303030;
      }
    }

    // Background color for indeterminate state
    &:has(.v-checkbox--indeterminate) {
      background-color: #303030;

      .v-treeview-node__root {
        background-color: #303030;
      }
    }
  }





  // 调整根节点样式
  .v-treeview-node__root {
    min-height: 32px;
    margin-left: 0;
    padding: 4px 8px; // 添加内边距使背景色更明显
    border-radius: 4px; // 可选：添加圆角
    transition: background-color 0.2s ease; // 添加过渡效果
    pointer-events: none;
  }

  // 调整子节点容器样式
  .v-list-group__items {
    margin-left: 32px; // 增加缩进距离
    position: relative;
  }

  .v-list-item-action {
    display: none;
  }

  &.v-list {
    background: transparent !important;
    padding: 0;

    .v-list-item {
      padding: 8px 0 !important;
    }
  }

  // 只允许展开/折叠图标可点击
  .v-treeview-node__toggle {
    pointer-events: auto;
  }
}

:deep(.v-selection-control) {
  justify-content: flex-end;
  flex-flow: row-reverse;
}


:deep(.v-switch .v-label) {
  padding-inline-end: 10px;
  padding-inline-start: 0px;
}

// 确保按钮区域始终可见
.v-row:last-child {
  margin-top: auto;
  padding: 16px;
  background: var(--v-theme-surface);
  position: sticky;
  bottom: 0;
  z-index: 1;
}
</style>
