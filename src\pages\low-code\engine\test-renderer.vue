<template>
  <div class="test-renderer-page">
    <h1>Low-Code Renderer Test Page</h1>
    <component :is="renderedVNode"></component>
  </div>
</template>

<script setup>
import { ref, watch, computed, getCurrentInstance, reactive, h, onMounted, nextTick, provide } from 'vue';
import { $api } from '@/utils/request';
import { render } from '@renderer/index';
import mockPageMeta from './mockPageMeta.json';
import { useDB } from '@/business/low-code-engine/hooks/useDB';

const db = useDB();

const getPageData = async () => {
  const pageData = await db.pageContent.get('list');
  console.log(pageData);
  return pageData.pageContent;
};

const pageData = ref(null);

onMounted(async () => {
  const data = await getPageData();
  pageData.value = {
    ...data,
    forceUpdateKey: 0
  }
});

const { appContext } = getCurrentInstance();

// const pageData = reactive({
//   ...mockPageMeta.data || {},
//   // 添加一个强制更新键，用于分页高亮问题
//   forceUpdateKey: 0
// });

// 标记生命周期是否已执行，避免重复执行
let lifecycleMounted = false;

// 渲染VNode
const renderedVNode = ref(null);
let isRendering = false;

const updateRender = () => {
  if (isRendering) return;
  isRendering = true;
  
  nextTick(() => {
    // 创建带有响应式数据的meta对象，确保每次都是新的引用
    const metaWithReactiveData = {
      ...mockPageMeta,
      data: pageData  // 直接传递响应式数据，而不是复制
    };
    
    // 如果生命周期已执行过，则移除生命周期防止重复调用
    if (lifecycleMounted) {
      metaWithReactiveData.lifecycle = undefined;
    }
    
    const vnodes = render(metaWithReactiveData);
    renderedVNode.value = vnodes ? h('div', vnodes) : null;
    
    // 标记生命周期已执行
    if (!lifecycleMounted) {
      lifecycleMounted = true;
    }
    
    isRendering = false;
  });
};

// 监听数据变化，触发重新渲染
watch([
  () => pageData.accounts,
  // () => pageData.totalUsers,
  // () => pageData.userGroupList,
  // () => pageData.roleList,
  // () => pageData.selectedRows,
  // () => pageData.selectedIds,
  // () => pageData.expanded,
  // () => pageData.isLoading,
  // () => pageData.page,      // 需要监听page变化以更新分页UI
  // () => pageData.itemsPerPage,  // 需要监听itemsPerPage变化以更新分页UI
  () => pageData.forceUpdateKey, // 强制更新键
], updateRender, { deep: false, immediate: true });

// 提供响应式数据上下文给子组件
provide('lowCodeRenderContext', {
  data: pageData
});

// 页面初始化逻辑已交给 lifecycle.onMounted 处理
onMounted(() => {
  // test-renderer mounted
});
</script>

<style scoped>
.test-renderer-page {
  padding: 20px;
}
</style>