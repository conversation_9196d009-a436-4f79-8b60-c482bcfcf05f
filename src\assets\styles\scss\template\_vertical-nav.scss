@use "@configured-variables" as variables;
@use "@styles/scss/base/mixins";
@use "@layouts/styles/mixins" as layoutsMixins;

.layout-nav-type-vertical {
  // 暗色主题样式
  .v-theme--dark {
    // --v-theme-surface: 34, 34, 34; // RGB 值对应 #222222
    .layout-vertical-nav {
      --v-theme-surface: 34, 34, 34; // RGB 值对应 #222222
      background-color: rgb(var(--v-theme-surface)) !important;
      .nav-link {
        a.router-link-active {
          background: #222222;
          color: rgb(var(--v-global-theme-primary));
        }
  
        position: relative;
        
        // 当有 router-link-active 类时才显示左边框
        &:has(.router-link-active) {
          &::before {
            content: '';
            position: absolute;
            display: block;
            width: 3px;
            height: 100%;
            background-color: rgb(var(--v-global-theme-primary));
            left: 0;
            top: 0;
            z-index: 1;
          }
        }
      }
    }
  }

  // 亮色主题样式
  .v-theme--light {
    .layout-vertical-nav {
      --v-theme-surface: 255, 255, 255; // RGB 值对应 #FFFFFF
      background-color: rgb(var(--v-theme-surface)) !important;
      
      .nav-link {
        a.router-link-active {
          background: #f5f5f5;
          color: rgb(var(--v-global-theme-primary));
        }
  
        position: relative;
        
        &:has(.router-link-active) {
          &::before {
            content: '';
            position: absolute;
            display: block;
            width: 3px;
            height: 100%;
            background-color: rgb(var(--v-global-theme-primary));
            left: 0;
            top: 0;
            z-index: 1;
          }
        }
      }
    }
  }
  
  // 👉 Layout Vertical nav
  .layout-vertical-nav {
    color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));

    @include mixins.elevation(4);

    .nav-header {
      padding-inline-end: 0.125rem;

      .app-logo {
        .app-title {
          font-size: 22px;
        }
      }
    }

    // 👉 Nav group & Link
    .nav-link,
    .nav-group {
      /* shadow cut issue fix */
      // margin-block-end: -0.5rem;
      // padding-block-end: 0.5rem;

      a {
        outline: none;
      }
    }

    .nav-section-title .placeholder-icon {
      margin-inline-start: 0.0625rem;
      transform: scaleX(1.6);

      @include layoutsMixins.rtl {
        margin-inline-start: 0.125rem;
      }
    }
  }

  &.layout-vertical-nav-collapsed {
    .layout-vertical-nav:not(.hovered) {
      .nav-header {
        .header-action {
          opacity: 0;
        }
      }
    }
  }
}
