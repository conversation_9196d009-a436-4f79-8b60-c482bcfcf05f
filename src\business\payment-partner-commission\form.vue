<script setup>
import MerchantInfoForm from "./MerchantInfoForm.vue";
import Tabs from "./Tabs";
import TabItem from "./TabItem.vue";
import { ref, provide, watch, useAttrs, defineAsyncComponent } from "vue";
import { usePaymentChannel } from "./usePaymentChannel";
import bigUtil from "@/utils/bigNumber";
import { useMerchant } from "./useMerchant";

const expandedPanels = ref([0]);
const { paymentChannelList: razerGoldPaymentChannelList } = usePaymentChannel(1);
const { paymentChannelList: razerGoldPinPaymentChannelList } = usePaymentChannel(2);
const { paymentChannelList: thirdPartyPaymentChannelList } = usePaymentChannel(3);

const { merchantList } = useMerchant();


// Provide independent selected state for each tab
const razerGoldSelectedIds = ref([]);
const razerGoldPinSelectedIds = ref([]);
const thirdPartySelectedIds = ref([]);

provide('razerGoldSelectedIds', razerGoldSelectedIds);
provide('razerGoldPinSelectedIds', razerGoldPinSelectedIds);
provide('thirdPartySelectedIds', thirdPartySelectedIds);

const razerGoldPaymentChannelItems = ref([]);
const razerGoldPinPaymentChannelItems = ref([]);
const thirdPartyPaymentChannelItems = ref([]);

// Add error state
const paymentChannelError = ref(false);
const paymentChannelErrorMessage = ref("");

// Add detailed error states
const commissionRateErrors = ref({
  razerGold: [],
  razerGoldPin: [],
  thirdParty: []
});

const effectiveOnErrors = ref({
  razerGold: [],
  razerGoldPin: [],
  thirdParty: []
});

// Monitor payment channel changes
watch(
  [
    () => razerGoldPaymentChannelItems.value,
    () => razerGoldPinPaymentChannelItems.value,
    () => thirdPartyPaymentChannelItems.value
  ],
  ([razerGold, razerGoldPin, thirdParty]) => {
    const hasAnyPaymentChannel = razerGold.length > 0 || razerGoldPin.length > 0 || thirdParty.length > 0;
    if (hasAnyPaymentChannel) {
      paymentChannelError.value = false;
      paymentChannelErrorMessage.value = "";
    }
  },
  { deep: true }
);

// Add AdditionalCurrencySetting data management
const razerGoldAdditionalCurrencyItems = ref([]);
const razerGoldPinAdditionalCurrencyItems = ref([]);
const thirdPartyAdditionalCurrencyItems = ref([]);

// 提供响应式数据给子组件
provide('razerGoldPaymentChannelItems', razerGoldPaymentChannelItems);
provide('razerGoldPinPaymentChannelItems', razerGoldPinPaymentChannelItems);
provide('thirdPartyPaymentChannelItems', thirdPartyPaymentChannelItems);

// Provide AdditionalCurrencySetting data
provide('razerGoldAdditionalCurrencyItems', razerGoldAdditionalCurrencyItems);
provide('razerGoldPinAdditionalCurrencyItems', razerGoldPinAdditionalCurrencyItems);
provide('thirdPartyAdditionalCurrencyItems', thirdPartyAdditionalCurrencyItems);

// Add AdditionalCurrencySetting error states
const additionalCurrencyErrors = ref({
  razerGold: {
    paymentChannel: [],
    merchantCurrency: [],
    minAmount: [],
    commissionRate: [],
    fixAmount: [],
    effectiveOn: [],
    duplicate: []
  },
  razerGoldPin: {
    paymentChannel: [],
    merchantCurrency: [],
    minAmount: [],
    commissionRate: [],
    fixAmount: [],
    effectiveOn: [],
    duplicate: []
  },
  thirdParty: {
    paymentChannel: [],
    merchantCurrency: [],
    minAmount: [],
    commissionRate: [],
    fixAmount: [],
    effectiveOn: [],
    duplicate: []
  }
});
// Provide AdditionalCurrencySetting error states
provide('additionalCurrencyErrors', additionalCurrencyErrors);

// 添加更新函数
const updatePaymentChannelItems = (items, tabIndex) => {
  if (tabIndex === 0) {
    razerGoldPaymentChannelItems.value = items;
    // 自动选中对应的 checkbox
    razerGoldSelectedIds.value = items.map(item => item.customId);
  } else if (tabIndex === 1) {
    razerGoldPinPaymentChannelItems.value = items;
    razerGoldPinSelectedIds.value = items.map(item => item.customId);
  } else if (tabIndex === 2) {
    thirdPartyPaymentChannelItems.value = items;
    thirdPartySelectedIds.value = items.map(item => item.customId);
  }
};

const updateAdditionalCurrencyItems = (items, tabIndex) => {
  if (tabIndex === 0) {
    razerGoldAdditionalCurrencyItems.value = items;
  } else if (tabIndex === 1) {
    razerGoldPinAdditionalCurrencyItems.value = items;
  } else if (tabIndex === 2) {
    thirdPartyAdditionalCurrencyItems.value = items;
  }
};

// 提供这些函数给子组件
provide('updatePaymentChannelItems', updatePaymentChannelItems);
provide('updateAdditionalCurrencyItems', updateAdditionalCurrencyItems);

const tabIndex = ref(0);
provide('tabIndex', tabIndex);

const attrs = useAttrs();
const merchantInfoFormRef = ref(null);

// Add method to get current time
const getCurrentDateTime = () => {
  return new Date().toISOString();
}

// Add method to convert single tab data
const convertTabData = (paymentChannelItems, additionalCurrencyItems, selectedIds, typeId) => {
  if (!selectedIds.length) return null;

  return {
    paymentMethodTypeId: typeId,
    currencySettingList: paymentChannelItems.map(item => ({
      paymentMethodId: item.id,
      transactRegionId: item.regionId,
      merchantRequestedCurrencyCode: "*",
      commissionRatePercentage: item.convertCommissionRate,
      commissionRateMinimumAmount: item.minAmount || 0,
      commissionRateFixedAmount: item.fixAmount || 0,
      effectiveStartDate: dateUtil.formatToUTC8(item.convertEffectiveOn),
      effectiveEndDate: null,
    })),
    additionalCurrencySettingList: additionalCurrencyItems.map(item => ({
      paymentMethodId: item.id,
      transactRegionId: item.regionId,
      merchantRequestedCurrencyCode: "*",
      commissionRatePercentage: item.convertCommissionRate,
      commissionRateMinimumAmount: item.minAmount || 0,
      commissionRateFixedAmount: item.fixAmount || 0,
      effectiveStartDate: dateUtil.formatToUTC8(item.convertEffectiveOn),
      effectiveEndDate: null,
    }))
  };
}

const getMerchantNameByMerchantId = (merchantId) => {
  return merchantList.value.find(item => item.value === merchantId)?.title || "";
}

// 异步加载组件
const PaymentChannelPane = defineAsyncComponent(() => 
  import('./PaymentChannelPane.vue')
);

const AdditionalCurrencySetting = defineAsyncComponent(() => 
  import('./AdditionalCurrencySetting.vue')
);

defineExpose({
  validate: async () => {
    // Validate MerchantInfoForm
    let { valid, errors } = await merchantInfoFormRef.value?.validate?.()
    if (!valid) {
      return { valid, errors }
    }

    // Reset all error states
    commissionRateErrors.value = {
      razerGold: [],
      razerGoldPin: [],
      thirdParty: []
    };
    effectiveOnErrors.value = {
      razerGold: [],
      razerGoldPin: [],
      thirdParty: []
    };
    additionalCurrencyErrors.value = {
      razerGold: {
        paymentChannel: [],
        merchantCurrency: [],
        minAmount: [],
        commissionRate: [],
        fixAmount: [],
        effectiveOn: [],
        duplicate: []
      },
      razerGoldPin: {
        paymentChannel: [],
        merchantCurrency: [],
        minAmount: [],
        commissionRate: [],
        fixAmount: [],
        effectiveOn: [],
        duplicate: []
      },
      thirdParty: {
        paymentChannel: [],
        merchantCurrency: [],
        minAmount: [],
        commissionRate: [],
        fixAmount: [],
        effectiveOn: [],
        duplicate: []
      }
    };

    // Validate PaymentChannelPane
    // Check if at least one tab has data
    const hasRazerGoldData = razerGoldPaymentChannelItems.value.length > 0;
    const hasRazerGoldPinData = razerGoldPinPaymentChannelItems.value.length > 0;
    const hasThirdPartyData = thirdPartyPaymentChannelItems.value.length > 0;

    if (!hasRazerGoldData && !hasRazerGoldPinData && !hasThirdPartyData) {
      console.log('Validating PaymentChannelPane', hasRazerGoldData, hasRazerGoldPinData, hasThirdPartyData)
      paymentChannelError.value = true;
      paymentChannelErrorMessage.value = "Please select at least one payment channel";
      return {
        valid: false,
        errors: [{
          paymentChannel: "Please select at least one payment channel"
        }]
      }
    }

    paymentChannelError.value = false;
    paymentChannelErrorMessage.value = "";

    // Validate commissionRate and effectiveOn for each tab with data
    const validateItems = (items, tabName, type) => {
      if (!items.length) return { valid: true, errors: [] };

      const errors = {
        commissionRate: [],
        effectiveOn: []
      };

      items.forEach((item, index) => {
        // Validate commissionRate
        if (!item.convertCommissionRate || item.convertCommissionRate < 0 || item.convertCommissionRate > 100) {
          errors.commissionRate.push(index);
        }

        // Validate effectiveOn
        if (!item.convertEffectiveOn) {
          errors.effectiveOn.push(index);
        }
      });

      // Update error states
      if (type === 'razerGold') {
        commissionRateErrors.value.razerGold = errors.commissionRate;
        effectiveOnErrors.value.razerGold = errors.effectiveOn;
      } else if (type === 'razerGoldPin') {
        commissionRateErrors.value.razerGoldPin = errors.commissionRate;
        effectiveOnErrors.value.razerGoldPin = errors.effectiveOn;
      } else if (type === 'thirdParty') {
        commissionRateErrors.value.thirdParty = errors.commissionRate;
        effectiveOnErrors.value.thirdParty = errors.effectiveOn;
      }

      const hasErrors = errors.commissionRate.length > 0 || errors.effectiveOn.length > 0;
      return {
        valid: !hasErrors,
        errors: hasErrors ? [{
          commissionRate: errors.commissionRate.length > 0 ? `Invalid commission rate in ${tabName}` : null,
          effectiveOn: errors.effectiveOn.length > 0 ? `Invalid effective time in ${tabName}` : null
        }] : []
      };
    }

    // Validate RAZER GOLD
    if (hasRazerGoldData) {
      const razerGoldValidation = validateItems(razerGoldPaymentChannelItems.value, "RAZER GOLD", "razerGold");
      if (!razerGoldValidation.valid) {
        return razerGoldValidation;
      }
    }

    // Validate RAZER GOLD PIN
    if (hasRazerGoldPinData) {
      const razerGoldPinValidation = validateItems(razerGoldPinPaymentChannelItems.value, "RAZER GOLD PIN", "razerGoldPin");
      if (!razerGoldPinValidation.valid) {
        return razerGoldPinValidation;
      }
    }

    // Validate THIRD PARTY
    if (hasThirdPartyData) {
      const thirdPartyValidation = validateItems(thirdPartyPaymentChannelItems.value, "THIRD PARTY", "thirdParty");
      if (!thirdPartyValidation.valid) {
        return thirdPartyValidation;
      }
    }

    // Validate AdditionalCurrencySetting
    const validateAdditionalCurrencyItems = (items, tabName) => {
      // Skip validation if items is undefined or null
      if (!items) return { valid: true, errors: [] };

      // If items is empty array, validation passes
      if (items.length === 0) return { valid: true, errors: [] };

      console.log('Validating AdditionalCurrencySetting', tabName, items);

      const errors = {
        paymentChannel: [],
        merchantCurrency: [],
        minAmount: [],
        commissionRate: [],
        fixAmount: [],
        effectiveOn: [],
        duplicate: [] // 新增重复数据错误数组
      };

      // 检查重复数据
      const seen = new Set();
      items.forEach((item, index) => {
        const key = `${item.paymentMethodId}-${item.merchantCurrency}`;
        if (seen.has(key)) {
          errors.duplicate.push(index);
        } else {
          seen.add(key);
        }
      });

      items.forEach((item, index) => {
        // Validate PaymentChannel
        if (!item.paymentMethodId) {
          errors.paymentChannel.push(index);
        }

        // Validate MerchantCurrency
        if (!item.merchantCurrency) {
          errors.merchantCurrency.push(index);
        }

        // Validate minAmount
        if (!item.minAmount || parseFloat(item.minAmount) < 0) {
          errors.minAmount.push(index);
        }

        // Validate commissionRate
        if (!item.convertCommissionRate ||
          parseFloat(item.convertCommissionRate) < 0 ||
          parseFloat(item.convertCommissionRate) > 100 ||
          !/^\d+(\.\d{0,2})?$/.test(String(item.convertCommissionRate))) {
          errors.commissionRate.push(index);
        }

        // Validate fixAmount
        if (!item.fixAmount || parseFloat(item.fixAmount) < 0) {
          errors.fixAmount.push(index);
        }

        // Validate effectiveOn
        if (!item.convertEffectiveOn) {
          errors.effectiveOn.push(index);
        }
      });

      // Update error states
      if (tabName === 'RAZER GOLD') {
        additionalCurrencyErrors.value.razerGold = errors;
      } else if (tabName === 'RAZER GOLD PIN') {
        additionalCurrencyErrors.value.razerGoldPin = errors;
      } else if (tabName === 'THIRD PARTY') {
        additionalCurrencyErrors.value.thirdParty = errors;
      }

      const hasErrors = Object.values(errors).some(arr => arr.length > 0);
      if (hasErrors) {
        console.log('AdditionalCurrencySetting validation failed', tabName, errors);
      }
      return {
        valid: !hasErrors,
        errors: hasErrors ? [{
          ...errors,
          duplicate: errors.duplicate.length > 0 ? `Duplicate payment channel and merchant currency combination found in ${tabName}` : null
        }] : []
      };
    }

    // Validate RAZER GOLD AdditionalCurrencySetting
    if (razerGoldAdditionalCurrencyItems.value && razerGoldAdditionalCurrencyItems.value.length > 0) {
      const razerGoldAdditionalValidation = validateAdditionalCurrencyItems(razerGoldAdditionalCurrencyItems.value, "RAZER GOLD");
      console.log('Validating RAZER GOLD AdditionalCurrencySetting', razerGoldAdditionalValidation);
      if (!razerGoldAdditionalValidation.valid) {
        return razerGoldAdditionalValidation;
      }
    }

    // Validate RAZER GOLD PIN AdditionalCurrencySetting
    if (razerGoldPinAdditionalCurrencyItems.value && razerGoldPinAdditionalCurrencyItems.value.length > 0) {
      const razerGoldPinAdditionalValidation = validateAdditionalCurrencyItems(razerGoldPinAdditionalCurrencyItems.value, "RAZER GOLD PIN");
      if (!razerGoldPinAdditionalValidation.valid) {
        return razerGoldPinAdditionalValidation;
      }
    }

    // Validate THIRD PARTY AdditionalCurrencySetting
    if (thirdPartyAdditionalCurrencyItems.value && thirdPartyAdditionalCurrencyItems.value.length > 0) {
      const thirdPartyAdditionalValidation = validateAdditionalCurrencyItems(thirdPartyAdditionalCurrencyItems.value, "THIRD PARTY");
      if (!thirdPartyAdditionalValidation.valid) {
        return thirdPartyAdditionalValidation;
      }
    }

    // Reset all editing states after validation passes
    const resetEditingState = (items) => {
      if (!items) return;
      items.forEach(item => {
        item.isEditing = false;
        item.isPaymentChannelEditing = false;
        item.isMerchantCurrencyEditing = false;
        item.isMinAmountEditing = false;
        item.isFixAmountEditing = false;
        item.isCommissionRateEditing = false;
        item.isEffectiveOnEditing = false;
      });
    };

    // Reset editing states for all tabs
    resetEditingState(razerGoldAdditionalCurrencyItems.value);
    resetEditingState(razerGoldPinAdditionalCurrencyItems.value);
    resetEditingState(thirdPartyAdditionalCurrencyItems.value);

    return { valid: true, errors: [] };
  },
  getParams: () => {
    console.log('getParams', attrs)
    // Get data from all tabs
    const razerGoldData = convertTabData(
      razerGoldPaymentChannelItems.value,
      razerGoldAdditionalCurrencyItems.value,
      razerGoldSelectedIds.value,
      1  // RAZER GOLD
    );

    const razerGoldPinData = convertTabData(
      razerGoldPinPaymentChannelItems.value,
      razerGoldPinAdditionalCurrencyItems.value,
      razerGoldPinSelectedIds.value,
      2  // RAZER GOLD PIN
    );

    const thirdPartyData = convertTabData(
      thirdPartyPaymentChannelItems.value,
      thirdPartyAdditionalCurrencyItems.value,
      thirdPartySelectedIds.value,
      3  // THIRD PARTY
    );

    // Merge all tabs with data
    const commissionSchemeItemByTypeList = [
      razerGoldData,
      razerGoldPinData,
      thirdPartyData
    ].filter(Boolean); // Filter out null values

    return {
      requestType: "COMMISSION_SCHEME",
      companyId: attrs.form.merchantId,
      merchantName: getMerchantNameByMerchantId(attrs.form.merchantId) || "",
      name: attrs.form.commissionSchemeName || "",
      remark: "",
      commissionSchemeItemByTypeList
    };
  }
})

</script>

<template>
  <VExpansionPanels multiple v-model="expandedPanels">
    <VExpansionPanel class="mb-6 min-h-108px bg-#1E1E1E!">
      <VExpansionPanelTitle class="mt-6 text-18px! text-white leading-44px">
        1.Enter Merchant Info
      </VExpansionPanelTitle>
      <VExpansionPanelText>
        <MerchantInfoForm ref="merchantInfoFormRef" v-bind="attrs" /> 
      </VExpansionPanelText>
    </VExpansionPanel>

    <VExpansionPanel class="mb-6 min-h-108px bg-#1E1E1E!" :class="{ 'border-error border-2px': paymentChannelError }">
      <VExpansionPanelTitle class="mt-6 text-18px! text-white leading-44px">
        2.Add Payment Channels
      </VExpansionPanelTitle>
      <VExpansionPanelText>
        <div v-if="paymentChannelError" class="text-error mb-4">
          {{ paymentChannelErrorMessage }}
        </div>
        <Tabs v-model="tabIndex">
          <TabItem :label="razerGoldPaymentChannelItems.length > 0 ? `RAZER GOLD (${razerGoldPaymentChannelItems.length})` : 'RAZER GOLD'">
            <Suspense>
              <template #default>
                <keep-alive>
                  <PaymentChannelPane 
                    v-if="tabIndex === 0"
                    :key="'razer-gold'"
                    v-model:paymentChannelList="razerGoldPaymentChannelList" 
                    name="RAZER GOLD" 
                    :commission-rate-errors="commissionRateErrors.razerGold"
                    :effective-on-errors="effectiveOnErrors.razerGold" 
                  />
                </keep-alive>
              </template>
              <template #fallback>
                <div>Loading...</div>
              </template>
            </Suspense>
          </TabItem>
          <TabItem :label="razerGoldPinPaymentChannelItems.length > 0 ? `RAZER GOLD PIN (${razerGoldPinPaymentChannelItems.length})` : 'RAZER GOLD PIN'">
            <Suspense>
              <template #default>
                <keep-alive>
                  <PaymentChannelPane 
                    v-if="tabIndex === 1"
                    :key="'razer-gold-pin'"
                    v-model:paymentChannelList="razerGoldPinPaymentChannelList" 
                    name="RAZER GOLD PIN" 
                    :commission-rate-errors="commissionRateErrors.razerGoldPin"
                    :effective-on-errors="effectiveOnErrors.razerGoldPin" 
                  />
                </keep-alive>
              </template>
              <template #fallback>
                <div>Loading...</div>
              </template>
            </Suspense>
          </TabItem>
          <TabItem :label="thirdPartyPaymentChannelItems.length > 0 ? `THIRD PARTY (${thirdPartyPaymentChannelItems.length})` : 'THIRD PARTY'">
            <Suspense>
              <template #default>
                <keep-alive>
                  <PaymentChannelPane 
                    v-if="tabIndex === 2"
                    :key="'third-party'"
                    v-model:paymentChannelList="thirdPartyPaymentChannelList" 
                    name="THIRD PARTY" 
                    :commission-rate-errors="commissionRateErrors.thirdParty"
                    :effective-on-errors="effectiveOnErrors.thirdParty" 
                  />
                </keep-alive>
              </template>
              <template #fallback>
                <div>Loading...</div>
              </template>
            </Suspense>
          </TabItem>
        </Tabs>
      </VExpansionPanelText>
    </VExpansionPanel>

    <VExpansionPanel class="mb-6 min-h-108px bg-#1E1E1E!" :class="{
      'border-error border-2px': additionalCurrencyErrors.razerGold.duplicate?.length ||
        additionalCurrencyErrors.razerGoldPin.duplicate?.length ||
        additionalCurrencyErrors.thirdParty.duplicate?.length
    }">
      <VExpansionPanelTitle class="mt-6 text-18px! text-white leading-44px">
        2.1 Additional Currency Setting (Optional)
      </VExpansionPanelTitle>
      <VExpansionPanelText>
        <div>
          <div v-if="additionalCurrencyErrors.razerGold.duplicate?.length ||
            additionalCurrencyErrors.razerGoldPin.duplicate?.length ||
            additionalCurrencyErrors.thirdParty.duplicate?.length" class="text-error px-4 py-2 mb-4">
            <div v-if="additionalCurrencyErrors.razerGold.duplicate?.length">
              {{ `Duplicate payment channel and merchant currency combination found in RAZER GOLD` }}
            </div>
            <div v-if="additionalCurrencyErrors.razerGoldPin.duplicate?.length">
              {{ `Duplicate payment channel and merchant currency combination found in RAZER GOLD PIN` }}
            </div>
            <div v-if="additionalCurrencyErrors.thirdParty.duplicate?.length">
              {{ `Duplicate payment channel and merchant currency combination found in THIRD PARTY` }}
            </div>
          </div>
          <Suspense>
            <template #default>
              <AdditionalCurrencySetting :duplicate-errors="additionalCurrencyErrors" />
            </template>
            <template #fallback>
              <div>Loading Additional Currency Settings...</div>
            </template>
          </Suspense>
        </div>
      </VExpansionPanelText>
    </VExpansionPanel>
  </VExpansionPanels>
</template>

<style scoped lang="scss">
:deep(.v-expansion-panel-title__icon .v-icon) {
  block-size: 2rem !important;
  font-size: 2rem !important;
  inline-size: 2rem !important;
  font-weight: 600 !important;
}

:deep(.v-expansion-panel--active) {
  .v-expansion-panel-title__icon {
    transition: transform 0.2s ease-in-out;
    transform: rotate(-0deg) !important;
  }
}

.border-error {
  border: 2px solid rgb(var(--v-theme-error)) !important;
  border-radius: 4px;
}

.text-error {
  color: rgb(var(--v-theme-error));
}

.bg-error-bg {
  background-color: rgba(var(--v-theme-error), 0.12);
  border-radius: 4px;
}
</style>
