# 低代码平台文档

## 📚 文档导航

### 🏗️ 架构文档
- **[最新架构文档](./低代码平台最新架构文档.md)** - 完整的系统架构说明
- **[架构流程图](./低代码平台架构流程图.md)** - 系统工作流程图解
- **[实现对比总结](./低代码平台实现对比总结.md)** - 早期设计与当前实现对比

### 🛠️ 开发指南
- **[开发指南](./低代码平台开发指南.md)** - 完整的开发指南和最佳实践

### 📋 早期文档（参考）
- **[低代码平台需求与技术方案](./低代码平台需求与技术方案.md)** - 早期需求分析
- **[低代码平台元数据结构设计方案](./低代码平台元数据结构设计方案.md)** - 早期元数据设计
- **[低代码目录结构设计](./低代码目录结构设计.md)** - 早期目录结构设计

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd com.razer.gold.admin.portal.v2

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 2. 访问低代码平台
- **场景管理**: `http://localhost:3000/low-code-engine`
- **可视化编辑器**: `http://localhost:3000/low-code-engine/editor`
- **元数据管理**: `http://localhost:3000/low-code-engine/meta-manager`
- **索引管理**: `http://localhost:3000/low-code-engine/index-manager`

## 📖 核心概念

### 场景 (Scenario)
业务场景是低代码平台的核心概念，代表一个完整的业务模块，包含：
- 场景基本信息（名称、描述、图标等）
- 元数据字段定义
- 页面配置
- 数据库索引

### 元数据 (Metadata)
元数据定义了业务场景的数据结构，包括：
- 字段名称和类型
- 字段验证规则
- 字段显示配置
- 字段映射到组件

### 页面生成 (Page Generation)
系统根据元数据自动生成页面配置，支持：
- 列表页面
- 创建页面
- 编辑页面
- 详情页面

### 可视化编辑 (Visual Editor)
提供拖拽式的页面编辑功能：
- 组件库拖拽
- 属性面板配置
- 实时预览
- 布局调整

## 🏛️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    低代码平台架构                            │
├─────────────────────────────────────────────────────────────┤
│  用户界面层 (UI Layer)                                     │
│  ├── 场景管理页面 (index.vue)                              │
│  ├── 可视化编辑器 (editor.vue)                             │
│  ├── 元数据管理器 (meta-manager.vue)                       │
│  ├── 索引管理器 (index-manager.vue)                        │
│  └── 页面预览器 (preview.vue)                             │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Business Logic Layer)                         │
│  ├── 场景配置管理 (useScenarioConfig.js)                   │
│  ├── 页面生成引擎 (usePageGeneration.js)                   │
│  ├── API封装层 (useLowCodeAPI.js)                         │
│  └── 分页配置 (usePagination.js)                          │
├─────────────────────────────────────────────────────────────┤
│  元数据管理层 (Metadata Management Layer)                   │
│  ├── 页面生成器 (pageGenerator.js)                         │
│  ├── 组件映射 (componentMapping.js)                        │
│  └── 字段对话框 (FieldDialog.vue)                          │
├─────────────────────────────────────────────────────────────┤
│  渲染引擎层 (Renderer Layer)                               │
│  ├── 渲染上下文 (context.js)                               │
│  ├── 组件渲染器 (renderComponent.js)                       │
│  ├── 插槽渲染器 (renderSlot.js)                           │
│  └── 工具函数 (utils.js)                                  │
├─────────────────────────────────────────────────────────────┤
│  编辑器组件层 (Editor Components Layer)                     │
│  ├── 组件库 (ComponentLibrary.vue)                        │
│  ├── 画布区域 (CanvasArea.vue)                            │
│  ├── 属性面板 (PropertiesPanel.vue)                       │
│  └── 可编辑组件 (EditableComponent.vue)                   │
├─────────────────────────────────────────────────────────────┤
│  数据存储层 (Data Storage Layer)                           │
│  ├── IndexedDB (本地配置存储)                              │
│  └── 后端API (远程数据同步)                                │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 技术栈

### 前端技术
- **框架**: Vue 3 + Composition API
- **UI库**: Vuetify 3
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **本地存储**: IndexedDB (Dexie.js)
- **HTTP客户端**: ofetch

### 开发工具
- **构建工具**: Vite
- **代码规范**: ESLint + Prettier
- **版本控制**: Git

## 📋 主要功能

### ✅ 已实现功能
- [x] 场景管理（创建、编辑、删除）
- [x] 元数据管理（字段定义、验证规则）
- [x] 页面自动生成（列表、创建、编辑、详情）
- [x] 可视化编辑器（拖拽、属性配置）
- [x] 渲染引擎（配置转组件）
- [x] 索引管理（数据库索引）
- [x] 本地存储（IndexedDB）
- [x] API集成（后端数据同步）

### 🚧 开发中功能
- [ ] 模板系统
- [ ] 版本控制
- [ ] 协作编辑
- [ ] 性能监控

### 📅 计划功能
- [ ] AI辅助生成
- [ ] 高级组件库
- [ ] 主题定制
- [ ] 插件系统

## 🎯 使用流程

### 1. 创建场景
1. 访问场景管理页面
2. 点击"Create New Scenario"
3. 填写场景信息
4. 保存场景

### 2. 定义元数据
1. 进入元数据管理器
2. 添加字段定义
3. 配置验证规则
4. 保存元数据

### 3. 生成页面
1. 进入可视化编辑器
2. 选择页面类型
3. 系统自动生成配置
4. 可视化编辑页面

### 4. 发布页面
1. 预览页面效果
2. 保存页面配置
3. 发布到生产环境

## 🔍 开发指南

### 快速上手
详细的使用指南请参考：[开发指南](./低代码平台开发指南.md)

### 扩展开发
- 添加新组件类型
- 创建新页面类型
- 自定义验证规则
- 集成第三方库

### 调试技巧
- 使用Vue DevTools
- 浏览器调试工具
- 性能监控
- 错误处理

## 📊 性能优化

### 已实现的优化
- 代码分割和懒加载
- IndexedDB缓存策略
- Vue 3响应式优化
- 组件渲染优化

### 监控指标
- 页面加载时间
- 组件渲染性能
- API调用耗时
- 内存使用情况

## 🐛 故障排除

### 常见问题
1. **页面无法保存**: 检查网络连接和API权限
2. **组件渲染异常**: 验证配置格式和数据绑定
3. **元数据同步失败**: 检查后端服务状态

### 调试方法
1. 查看浏览器控制台
2. 使用Vue DevTools
3. 检查网络请求
4. 验证数据格式

## 📞 支持与反馈

### 文档维护
- 定期更新架构文档
- 同步代码变更
- 完善使用指南
- 收集用户反馈

### 问题反馈
- 提交Issue到GitHub
- 联系开发团队
- 查看常见问题
- 参考故障排除

---

## 📝 文档更新记录

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2024-01-XX | v1.0.0 | 初始版本，完整架构文档 | 开发团队 |
| 2024-01-XX | v1.1.0 | 添加开发指南和流程图 | 开发团队 |
| 2024-01-XX | v1.2.0 | 完善API文档和使用指南 | 开发团队 |

---

*本文档为低代码平台的完整技术文档，包含架构设计、开发指南、使用说明等内容。如有疑问或建议，请联系开发团队。* 