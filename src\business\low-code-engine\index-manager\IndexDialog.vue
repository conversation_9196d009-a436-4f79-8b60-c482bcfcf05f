<template>
  <VDialog :model-value="modelValue" max-width="600" @update:model-value="$emit('update:modelValue', $event)">
    <VCard class="dialog-card pt-2 pb-6">
      <VCardTitle>
        <div class="d-flex justify-space-between align-center">
          <span class="text-h5">{{ title }}</span>
          <VBtn icon variant="text" size="small" @click="closeDialog">
            <VIcon>mdi-close</VIcon>
          </VBtn>
        </div>
      </VCardTitle>

      <VCardText class="dialog-content">
        <VForm @submit.prevent="handleSubmit" size="large" ref="form">
          <AppTextField v-model="localFormData.indexName" label="Index Name" placeholder="e.g.: username"
            :rules="[requiredValidator]" density="compact" variant="outlined" class="mb-6"
            :readonly="mode === 'edit'" />

          <AppSelect v-model="localFormData.indexFields" label="Index Fields" :items="localFormData.metaFields"
            density="compact" placeholder="Select Index Fields" variant="outlined" class="mb-6" />

          <AppSelect v-model="localFormData.type" label="Index Type" :items="typeItems" density="compact"
            placeholder="Select Index Type" @update:model-value="handleTypeChange" variant="outlined"
            class="mb-6" />

          <VRow>
            <VCol cols="6">
              <VSwitch v-model="localFormData.unique" label="Unique" color="primary" density="compact" class="flex-1" />
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VCardActions class="px-6 flex gap-2">
        <VBtn variant="outlined" @click="$emit('update:modelValue', false)" class="w-120px">Cancel</VBtn>
        <VBtn color="primary" variant="flat" @click="handleSubmit" :loading="loading" class="w-120px">
          {{ submitButtonText }}
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useLowCodeAPI } from '../api/useLowCodeAPI'
import { requiredValidator } from '@/utils/validators'
import { FieldTypes, SourceTypes } from '../constants'

const props = defineProps({
  modelValue: Boolean,
  mode: { // 'add' or 'edit'
    type: String,
    required: true,
    default: 'add'
  },
  formData: {
    type: Object,
    required: true
  },
  fieldTypes: {
    type: Array,
    required: true
  },
  loading: Boolean
})

const emit = defineEmits(['update:modelValue', 'submit'])

const { getSelectMetadata } = useLowCodeAPI()
const originSelectMetadata = ref({})

const typeItems = [
  {
    title: 'SERVER ',
    value: 'SERVER '
  },
  {
    title: 'CLIENT',
    value: 'CLIENT'
  }
]

const sourceTypeOptions = computed(() => {
  if (localFormData.source === SourceTypes.APP_ENUMS) {
    return originSelectMetadata.value.APP_ENUMS.map(item => ({
      title: item,
      value: item
    }))
  } else if (localFormData.source === SourceTypes.DICT_TABLE) {
    return originSelectMetadata.value.DICT_TABLE.map(item => ({
      title: item,
      value: item
    }))
  }
  return []
})

const getSourceTypeOptions = async () => {
  const res = await getSelectMetadata()
  console.log('getSelectMetadata', res)
  originSelectMetadata.value = res.data
}

// Use a local copy of formData to avoid direct mutation of props
const localFormData = reactive({ ...props.formData })

watch(() => props.formData, (newVal) => {
  Object.assign(localFormData, newVal)
}, { deep: true })

const title = computed(() => props.mode === 'add' ? 'Add New Index' : 'Edit Index')
const submitButtonText = computed(() => props.mode === 'add' ? 'Add Field' : 'Update Field')

const closeDialog = () => {
  emit('update:modelValue', false)
}

const form = ref(null);

function handleTypeChange(value) {
  localFormData.type = value
}


function handleSubmit() {
  form.value.validate().then(({ valid }) => {
    if (valid) {
      emit('submit', localFormData)
    }
  })
}

watch(() => props.modelValue, (newVal) => {
  if (newVal) { // Only run when dialog is opened
    if (props.mode === 'add') {
      // Reset form data when opening in add mode
      Object.assign(localFormData, {
        indexName: '',
        indexFields: '',  // 多个字段用逗号分隔 
        type: '',
        unique: false
      })
    }
  }
})




watch(() => localFormData.source, (newSource) => {
  if ([SourceTypes.APP_ENUMS, SourceTypes.DICT_TABLE].includes(newSource)) {
    getSourceTypeOptions();
  } else {
    localFormData.type = ''; // Clear type if source is not APP_ENUMS or DICT_TABLE 
  }
});
</script>

<style scoped></style>